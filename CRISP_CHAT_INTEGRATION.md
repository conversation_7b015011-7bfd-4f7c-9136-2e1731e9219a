# Crisp Chat Integration for Customer Support

This document provides a comprehensive overview of the Crisp Chat integration implemented for customer support in the eBay Integration & Sync Shopify app.

## Overview

The integration provides seamless customer support through Crisp live chat, accessible via:

-   Navigation menu "Get Support" link
-   Dedicated support page (`/support`)
-   Shopify Admin "Get Support" button (via App Bridge Support API)
-   Programmatic access through React hooks

## Architecture

### Components Structure

```
web/frontend/components/Support/
├── SupportManager.jsx      # Main integration logic and App Bridge registration
├── SupportButton.jsx       # Reusable support button component
├── SupportTest.jsx         # Testing component (development only)
└── index.js               # Export definitions
```

### Key Features

1. **Environment Agnostic**: Works in both development and production
2. **App Bridge Integration**: Seamless integration with Shopify Admin
3. **Automatic User Data**: Pre-populates user email and name
4. **Fallback Support**: Email fallback when Crisp is unavailable
5. **Error Handling**: Comprehensive error handling and logging
6. **Internationalization**: Full i18n support

## Implementation Details

### 1. Environment Configuration

**Required Environment Variables:**

```bash
# Backend Crisp Configuration (existing)
CRISP_PLUGIN_IDENTIFIER=your-plugin-identifier
CRISP_PLUGIN_KEY=your-plugin-key
CRISP_WEBSITE_ID=your-website-id
CRISP_DEVELOPER_MAIL=your-support-email

# Frontend Crisp Configuration (added)
VITE_CRISP_ID=your-website-id  # Same as CRISP_WEBSITE_ID
```

### 2. Core Components

#### SupportManager Component

-   Registers App Bridge Support API handler
-   Manages Crisp chat opening logic
-   Provides error handling and fallbacks
-   Should be included once in the main App component

#### SupportButton Component

-   Reusable button for opening support chat
-   Configurable styling and text
-   Uses the `useSupportChat` hook internally

#### Support Page

-   Dedicated route at `/support`
-   Auto-opens Crisp chat on load
-   Provides manual chat and email options
-   Fully internationalized content

### 3. App Bridge Support Extension

**Extension Configuration:**

```toml
# extensions/support-link/shopify.extension.toml
name = "support-link"
type = "admin_link"
api_version = "2024-01"

[[extensions.targeting]]
target = "admin.app.support.link"
text = "t:text"
url = "app://support"
```

This extension ensures that "Get Support" buttons throughout the Shopify Admin redirect to the app's support page.

## Usage Examples

### Basic Usage (Automatic)

The integration works automatically once implemented. Users can:

1. Click "Get Support" in the app navigation
2. Click "Get Support" in Shopify Admin
3. Visit `/support` directly

### Programmatic Usage

```jsx
import { useSupportChat } from "../components/Support";

function MyComponent() {
    const { openSupport } = useSupportChat();

    return <Button onClick={openSupport}>Need Help?</Button>;
}
```

### Custom Support Button

```jsx
import { SupportButton } from "../components/Support";

function MyPage() {
    return (
        <SupportButton variant="primary" size="large">
            Contact Support
        </SupportButton>
    );
}
```

## Best Practices

### 1. Error Handling

-   Always provide fallback mechanisms
-   Log errors for debugging
-   Don't crash the app if Crisp fails

### 2. User Experience

-   Pre-populate user information when possible
-   Provide clear feedback when opening chat
-   Offer alternative contact methods

### 3. Performance

-   Load Crisp asynchronously
-   Don't block app initialization
-   Use lazy loading where appropriate

### 4. Security

-   Validate environment variables
-   Don't expose sensitive configuration
-   Use secure communication channels

## Troubleshooting

### Common Issues

**1. Crisp not loading in development**

```bash
# Check environment variable
grep VITE_CRISP_ID web/.env

# Should return: VITE_CRISP_ID=your-website-id
```

**2. Support button not appearing in Shopify Admin**

```bash
# Deploy the support extension
shopify app deploy
```

**3. User data not pre-populated**

-   Check that user authentication is working
-   Verify data is being set in `ProtectedRoute.jsx`
-   Check browser console for errors

**4. Chat not opening automatically**

-   Verify Crisp is loaded (check browser console)
-   Check for JavaScript errors
-   Ensure proper timing of chat open call

### Debug Mode

For debugging, temporarily add the `SupportTest` component:

```jsx
import SupportTest from "../components/Support/SupportTest";

// Add to any page for testing
<SupportTest />;
```

## Maintenance

### Regular Tasks

1. Monitor Crisp service status
2. Update environment variables if needed
3. Test functionality after app updates
4. Review error logs for issues

### Updates

When updating the integration:

1. Test in development first
2. Verify all fallbacks work
3. Check cross-browser compatibility
4. Update documentation as needed

## Security Considerations

1. **Environment Variables**: Keep Crisp credentials secure
2. **User Data**: Only send necessary user information to Crisp
3. **Error Handling**: Don't expose sensitive information in error messages
4. **Fallbacks**: Ensure fallback mechanisms are secure

## Performance Impact

The integration has minimal performance impact:

-   Crisp SDK loads asynchronously
-   No blocking operations during app initialization
-   Lazy loading of chat interface
-   Minimal bundle size increase

## Compliance

The integration follows:

-   Shopify App Store requirements
-   GDPR compliance (user data handling)
-   Accessibility standards (WCAG 2.1)
-   Security best practices

## Future Enhancements

Potential improvements:

1. **Analytics**: Track support interaction metrics
2. **Customization**: Theme-based chat styling
3. **Automation**: Automated responses for common questions
4. **Integration**: Deeper integration with app data
5. **Mobile**: Enhanced mobile experience

## Deployment Checklist

Before deploying to production:

### Pre-Deployment

-   [ ] Environment variables are set correctly
-   [ ] All components pass linting and type checking
-   [ ] Integration tested in development environment
-   [ ] Support extension is configured and ready
-   [ ] Translations are complete and accurate
-   [ ] Error handling is comprehensive

### Deployment Steps

1. **Deploy Support Extension**

    ```bash
    shopify app deploy
    ```

2. **Update Environment Variables**

    - Ensure production `VITE_CRISP_ID` is set
    - Verify all Crisp configuration is correct

3. **Deploy Application**
    - Follow standard deployment process
    - Monitor for any errors during deployment

### Post-Deployment

-   [ ] Test "Get Support" in navigation menu
-   [ ] Test support page functionality
-   [ ] Test App Bridge support integration
-   [ ] Verify Crisp chat opens correctly
-   [ ] Check user data pre-population
-   [ ] Test fallback mechanisms
-   [ ] Monitor error logs for issues

### Rollback Plan

If issues occur:

1. Remove `<SupportManager />` from `App.jsx`
2. Remove support link from navigation
3. Revert environment variable changes
4. Redeploy without support integration

## Support

For issues with this integration:

1. Check the troubleshooting section
2. Review browser console for errors
3. Test with the `SupportTest` component
4. Contact the development team with specific error details
