# eBay Upload Error Resolution Guide

## Overview

When uploading products from Shopify to eBay, various errors can occur due to differences in platform requirements, data formatting issues, or policy violations. Our system automatically detects and resolves these errors to ensure smooth product uploads.

This guide explains each type of error and how our system handles them.

---

## Error Types and Resolution Methods

### 1. Price-Related Errors

#### **Error Code: 73 - Invalid Price**

**What it means:**
The product price doesn't meet eBay's requirements (too low, too high, or improperly formatted).

**How we resolve it:**
- User provides a corrected price through the error resolution interface
- System updates the product with the new price override
- Product automatically re-uploads to eBay with the corrected price

**User action required:** Enter a valid price in the resolution popup

---

### 2. Weight and Shipping Errors

#### **Error Codes: 717, 219021, 21916495 - Invalid Weight**

**What it means:**
The product weight is missing, incorrectly formatted, or doesn't meet eBay's shipping requirements.

**How we resolve it:**
- User provides correct weight and weight unit (lbs, kg, oz)
- System updates the product with weight override
- Product automatically re-uploads with proper shipping calculations

**User action required:** Enter valid weight and select correct unit (pounds, kilograms, ounces)

---

### 3. Inventory and Quantity Errors

#### **Error Codes: 515, 942 - Invalid Quantity**

**What it means:**
The product quantity is invalid (negative, too high, or formatting issues).

**How we resolve it:**
- User provides corrected quantity and selects which store location to update
- System automatically handles all the technical setup behind the scenes
- Updates inventory for all product variations at once
- Keeps Shopify and eBay inventory in sync
- Works whether inventory is already enabled for the location or needs to be set up

**User action required:** 
- Enter valid quantity number
- Choose which store location to update

**What happens automatically:**
- System enables inventory for the location if not already
- All product variations get updated with the same quantity
- Inventory syncs between Shopify and eBay
- Changes are saved and applied immediately
- Detailed logs track all changes for troubleshooting

---

#### **Error Code: 21919188 - Inventory Override Needed**

**What it means:**
The system needs permission to override default inventory settings for this product category.

**How we resolve it:**
- User sets inventory override preference
- System applies setting to all products in the same profile
- All affected products automatically re-upload with new inventory rules

**User action required:** Choose inventory override method
**Bulk action:** Applies to all products in the current profile

---

### 4. Title and Content Errors

#### **Error Code: 70 - Title Too Long**

**What it means:**
The product title exceeds eBay's character limit (typically 80 characters).

**How we resolve it:**
- **Single product:** User provides shortened title
- **Bulk option:** System automatically truncates all titles in the profile
- Smart truncation preserves important keywords
- Considers title prefix/suffix settings

**User action required:** Provide shortened title or enable auto-truncation

**Bulk features:**
- Automatic title truncation for entire product profile
- Preserves title prefixes and suffixes
- Intelligent keyword preservation

---

#### **Error Code: 21920309 - Disallowed Characters**

**What it means:**
The product title contains characters that eBay doesn't allow (special symbols, emojis, etc.).

**How we resolve it:**
- **Single product:** User provides cleaned title
- **Bulk option:** System automatically removes forbidden characters from all affected products
- Updates user settings to automatically clean future uploads

**User action required:** Provide clean title or enable automatic character cleaning

**Forbidden characters removed:** `` ` $ ^ | < * > ~ = + ``

---

### 5. Description Errors

#### **Error Code: 106 - Missing Description**

**What it means:**
eBay requires a product description, but the Shopify product doesn't have one.

**How we resolve it:**
- **Single product:** User provides description
- **Bulk option:** System automatically uses product title as description for all affected products
- Ensures all products meet eBay's content requirements

**User action required:** Provide description or enable auto-mapping from title

---

### 6. Duplicate and Policy Errors

#### **Error Code: ******** - Duplicate SKU in Variants**

**What it means:**
Multiple product variants have the same SKU, which eBay doesn't allow.

**How we resolve it:**
- **Single product:** Enable duplicate SKU override for this product
- **Bulk option:** Update account settings to automatically handle duplicate SKUs
- System generates unique identifiers when needed

**User action required:** Choose to override for single product or enable global duplicate SKU handling

---

#### **Error Code: ******** - Duplicate Listing Policy Violation**

**What it means:**
eBay detected that this product might be too similar to existing listings.

**How we resolve it:**
- User provides a unique, optimized title
- System updates product metadata with the new title
- Helps differentiate the listing from similar products

**User action required:** Provide a unique, descriptive title

---

### 7. Product Status Errors

#### **Error Code: 291 - Product Needs Relisting**

**What it means:**
The eBay listing was ended or suspended and needs to be relisted.

**How we resolve it:**
- System automatically triggers the relisting process
- Uses specialized relisting handler to restore the product
- Maintains product history and settings

**User action required:** None - automatically handled

---

#### **Error Code: 1 - End and Upload Again**

**What it means:**
Product variations have changed significantly and the listing needs to be recreated.

**How we resolve it:**
- **Single product:** Enable variation state override for this product
- **Bulk option:** Update account settings to handle variation changes automatically
- System manages the end-and-reupload process

**User action required:** Choose single product override or enable global variation handling

---

## Resolution Options

### Single Product Resolution
- Fix one product at a time
- Immediate resolution with custom values
- No impact on other products

### Bulk Resolution
- Fix all products with the same error type
- Apply consistent rules across product profiles
- Update account settings for future uploads
- Significant time savings for large catalogs

### Automatic Resolution
- Some errors are resolved automatically by the system
- No user intervention required
- Background processing ensures continuous uploads

---

## Best Practices

### For Store Owners
1. **Review product data** before initial upload to minimize errors
2. **Use bulk resolution** when multiple products have similar issues
3. **Enable automatic settings** for common, recurring errors
4. **Monitor upload progress** through the dashboard

### For Support Teams
1. **Identify error patterns** to prevent future occurrences
2. **Guide users toward bulk solutions** when appropriate
3. **Document custom resolutions** for complex cases
4. **Escalate platform-specific issues** to development team

---

## Error Prevention Tips

### Product Preparation
- Ensure all required fields are filled in Shopify
- Use consistent formatting for prices, weights, and descriptions
- Avoid special characters in titles and descriptions
- Verify product images meet eBay requirements

### Account Settings
- Configure title prefixes and suffixes appropriately
- Set up inventory management preferences
- Enable automatic character cleaning
- Use consistent SKU formatting across variants

### Regular Maintenance
- Review and update product profiles regularly
- Monitor error reports for recurring issues
- Keep product data synchronized between platforms
- Update shipping and inventory settings as needed

---

## Getting Help

### Self-Service Options
- Use the error resolution interface for immediate fixes
- Enable bulk resolution for efficiency
- Check the FAQ section for common issues

### Support Channels
- Contact support for complex or recurring errors
- Request bulk data cleanup for large catalogs
- Report platform-specific issues for investigation

### System Monitoring
- Dashboard shows real-time upload progress
- Error reports provide detailed information
- Automatic notifications for critical issues

---

*This documentation is regularly updated to reflect system improvements and new error handling capabilities.* 