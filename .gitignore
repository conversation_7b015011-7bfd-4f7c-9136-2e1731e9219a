/web/frontend/node_modules
/web/public/hot
/web/public/assets
/web/public/index.html
/web/public/storage
/web/storage/*.key
/web/storage/db.sqlite
/web/vendor
/web/ebayKeyset.json
#/web/composer.lock
.env
.env.*
!.env.example
!.env.testing
web/.phpunit.result.cache
docker-compose.override.yml
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log
package-lock.json
yarn.lock
shopify.app.toml
shopify.app.*.toml
**/node_modules

.idea
.vscode
.history
php_errors.log
web/php_errors.log
web/shopify.web.toml
.shopify
.qodo
.ssh/
