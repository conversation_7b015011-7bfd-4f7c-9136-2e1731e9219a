# eBay Integration Sync - Project Overview

## Purpose
This application integrates eBay with Shopify, allowing merchants to sync their eBay products to Shopify stores seamlessly.

## Tech Stack
- **Backend**: Laravel 8 with PHP 8.1+
- **Frontend**: React 18 with Shopify Polaris UI components
- **API Integrations**: Shopify API, eBay SDK
- **Database**: MySQL
- **Queue System**: Redis/Database queues for background jobs

## Core Functionality
1. **Product Sync**: Sync eBay products to Shopify
2. **Order Management**: Handle order synchronization between platforms
3. **Inventory Management**: Keep inventory levels in sync
4. **User Management**: Handle user authentication and profiles
5. **Webhooks**: Process real-time updates from both platforms

## Key Features
- Bulk product operations
- Real-time progress tracking with Pusher
- Multi-location support
- Advanced filtering and sorting
- Error handling and retry mechanisms
- Rate limiting compliance for both APIs

## Architecture Patterns
- Repository pattern for data access
- Service layer for business logic
- Job queues for background processing
- Event-driven architecture for decoupling
- Observer pattern for model events