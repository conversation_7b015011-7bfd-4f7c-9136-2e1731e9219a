# Frontend Architecture

## Technology Stack
- **React 18**: Main framework
- **Shopify Polaris**: UI component library
- **React Context**: State management
- **React Router**: Navigation
- **i18next**: Internationalization
- **Custom Hooks**: Reusable logic

## Directory Structure
```
frontend/
├── assets/                # Static assets (images, CSS)
├── components/            # React components
│   ├── Banner/           # Banner components
│   ├── Common/           # Shared components
│   ├── Dashboard/        # Dashboard-specific
│   ├── Modal/            # Modal dialogs
│   ├── Product/          # Product-related components
│   ├── Orders/           # Order management
│   ├── Settings/         # Settings components
│   └── providers/        # Context providers
├── context/              # React contexts
├── hooks/                # Custom hooks
├── pages/                # Page components
├── util/                 # Utility functions
└── vendor/               # Third-party dependencies
```

## Key Components

### Core Pages
- **Dashboard**: Main application dashboard
- **Products**: Product management interface
- **Orders**: Order synchronization interface
- **Settings**: Application configuration
- **Profile**: User profile management

### Reusable Components
- **ProtectedRoute**: Authentication wrapper
- **LoadingState**: Loading indicators
- **Skeleton**: Skeleton loading states
- **Modal**: Modal dialog system
- **Banner**: Notification banners

### Context Providers
- **AppProvider**: Global application state
- **AuthProvider**: Authentication state
- **ModalProvider**: Modal management
- **NotificationProvider**: User notifications

### Custom Hooks
- **useAuthenticatedFetch**: Authenticated API calls
- **useAppQuery**: Query management
- **useDebounce**: Input debouncing
- **useLocalStorage**: Local storage management

## Design Patterns

### Component Organization
- Functional components with hooks
- Context for global state
- Custom hooks for reusable logic
- Component composition over inheritance

### State Management
- React Context for global state
- Local state for component-specific data
- Custom hooks for shared logic
- Reducer pattern for complex state

### UI Patterns
- Consistent Polaris component usage
- Responsive design with Grid/BlockStack
- Error boundaries for error handling
- Progressive loading states