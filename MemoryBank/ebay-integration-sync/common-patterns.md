# Common Development Patterns

## PHP/Laravel Patterns

### Strict Type Hinting
```php
// Always use strict type declarations
public function processOrder(Order $order, array $items): OrderResult
{
    // Implementation
}
```

### Constructor Property Promotion
```php
// Use PHP 8.0+ constructor property promotion
class ProductService
{
    public function __construct(
        private readonly ProductRepository $productRepository,
        private readonly EbayApiService $ebayService,
        private readonly ShopifyApiService $shopifyService,
    ) {}
}
```

### Service Layer Pattern
- Business logic encapsulated in services
- Controllers remain thin
- Services are injected via dependency injection
- Services return DTOs or domain objects

### Repository Pattern
- Data access abstraction
- Interface-based implementation
- Testable data layer
- Consistent query patterns

### Error Handling
- Custom exceptions for specific scenarios
- Global exception handlers
- Logging with context
- User-friendly error messages

## React/Frontend Patterns

### Functional Components with Hooks
```jsx
// Always use functional components
const ProductList = ({ products, loading }) => {
  const [selectedProducts, setSelectedProducts] = useState([]);
  // Component logic
};
```

### Custom Hooks for Reusable Logic
```jsx
// Extract common logic into custom hooks
const useProductSelection = (products) => {
  const [selected, setSelected] = useState([]);
  // Selection logic
  return { selected, toggleSelection, clearSelection };
};
```

### Context for Global State
```jsx
// Use Context for application-wide state
const AppContext = createContext();
export const useApp = () => useContext(AppContext);
```

### Protected Route Pattern
```jsx
// Consistent authentication checking
<ProtectedRoute>
  <ComponentRequiringAuth />
</ProtectedRoute>
```

## API Communication Patterns

### Authenticated Fetch Hook
- Consistent API calling pattern
- Automatic token handling
- Error boundary integration
- Loading state management

### Error Handling Strategy
- Consistent error response format
- User-friendly error messages
- Automatic retry for transient failures
- Graceful degradation

### Pagination Pattern
- Cursor-based pagination for large datasets
- Consistent pagination DTOs
- Frontend pagination components
- Infinite scroll where appropriate

## Database Patterns

### Migration Strategy
- Incremental schema changes
- Rollback-safe migrations
- Data seeding for reference data
- Index optimization

### Model Relationships
- Proper foreign key constraints
- Eager loading to prevent N+1 queries
- Soft deletes where appropriate
- Timestamps for audit trails

### Query Optimization
- Database indexes on frequently queried columns
- Query result caching
- Batch operations for bulk updates
- Database connection pooling

## Queue Processing Patterns

### Job Design
- Single responsibility per job
- Idempotent job processing
- Proper error handling and retries
- Job progress tracking

### Queue Strategy
- Separate queues for different priorities
- Dead letter queues for failed jobs
- Job batching for related operations
- Rate limiting for API calls

## Testing Patterns

### Unit Testing
- Test business logic in isolation
- Mock external dependencies
- Test edge cases and error conditions
- Maintain high test coverage

### Integration Testing
- Test API endpoints
- Test database interactions
- Test external service integrations
- Test webhook processing

### Frontend Testing
- Component unit tests
- Integration tests with mocked APIs
- User interaction testing
- Accessibility testing