# Backend Architecture

## Directory Structure
```
app/
├── Console/Commands/       # Artisan commands
├── DTO/                   # Data Transfer Objects
├── Enums/                 # Enumerations
├── Events/                # Laravel events
├── Exceptions/            # Custom exceptions
├── Filters/               # Query filters and scopes
├── Http/                  # Controllers, middleware, requests, resources
├── Jobs/                  # Queue jobs
├── Lib/                   # Custom libraries and handlers
├── Listeners/             # Event listeners
├── Mail/                  # Mail classes
├── Models/                # Eloquent models
├── Module/                # Business logic modules
├── Observers/             # Model observers
├── Providers/             # Service providers
├── Repositories/          # Repository pattern implementation
├── Scopes/                # Query scopes
├── Services/              # Service layer
└── Traits/                # Reusable traits
```

## Key Components

### Models
- **EbayProduct**: eBay product representation
- **ShopifyProduct**: Shopify product representation
- **User**: Application users
- **Shop**: Shopify shop information
- **BulkQueryOperation**: Bulk operation tracking

### Services
- **AppConfigurationService**: Application configuration management
- **LocationService**: Shopify location management
- **Shopify Services**: Various Shopify API integrations

### Jobs
- **Product Jobs**: Handle product synchronization
- **Order Jobs**: Manage order processing
- **Notification Jobs**: Send emails and notifications
- **Policy Jobs**: Fetch eBay policies

### Events & Listeners
- Product import completion events
- Order synchronization events
- Bulk operation progress tracking

## Design Patterns Used
1. **Repository Pattern**: Data access abstraction
2. **Service Layer**: Business logic encapsulation
3. **Observer Pattern**: Model event handling
4. **Factory Pattern**: Object creation
5. **Strategy Pattern**: Algorithm selection
6. **Command Pattern**: Queue jobs