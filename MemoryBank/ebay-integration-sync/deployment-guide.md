# Deployment Guide

## Environment Setup

### Development Environment
```bash
# Prerequisites
- PHP 8.1+
- Node.js 16+
- MySQL 8.0+
- Redis
- Composer
- NPM/Yarn

# Setup Steps
git clone <repository>
cd ebay-integration-sync
composer install
npm install
cp .env.example .env
php artisan key:generate
php artisan migrate
php artisan db:seed
npm run dev
```

### Staging Environment
- Mirror production configuration
- Use staging API endpoints
- Implement proper testing data
- Configure CI/CD pipeline
- Enable debug mode for testing

### Production Environment
- Optimize PHP configurations
- Configure web server (Nginx/Apache)
- Set up SSL certificates
- Configure caching layers
- Implement monitoring and logging

## Docker Configuration

### Laravel Container
```dockerfile
# Dockerfile.laravel
FROM php:8.1-fpm
RUN apt-get update && apt-get install -y \
    git curl libpng-dev libonig-dev libxml2-dev zip unzip
RUN docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd
COPY . /var/www
WORKDIR /var/www
RUN composer install --optimize-autoloader --no-dev
```

### Nginx Configuration
```nginx
# nginx.conf
server {
    listen 80;
    index index.php index.html;
    root /var/www/public;
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.laravel
    volumes:
      - ./web:/var/www
    depends_on:
      - db
      - redis
  
  web:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    ports:
      - "80:80"
    depends_on:
      - app
  
  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: ebay
      MYSQL_ROOT_PASSWORD: password
    volumes:
      - db_data:/var/lib/mysql
  
  redis:
    image: redis:alpine
    
volumes:
  db_data:
```

## CI/CD Pipeline

### GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy Application
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.1
      - name: Install dependencies
        run: composer install
      - name: Run tests
        run: php artisan test
  
  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to production
        run: |
          # Deployment commands
```

### Deployment Steps
1. **Code Quality Checks**
   - Run PHP CS Fixer
   - Execute PHPStan analysis
   - Run test suite
   - Check for security vulnerabilities

2. **Build Process**
   - Install PHP dependencies
   - Install Node.js dependencies
   - Build frontend assets
   - Optimize autoloader

3. **Database Migration**
   - Backup current database
   - Run new migrations
   - Verify data integrity
   - Rollback if issues detected

4. **Application Deployment**
   - Upload new code
   - Update environment variables
   - Clear application caches
   - Restart queue workers

## Environment Configuration

### Production Environment Variables
```env
# Application
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=production_db
DB_USERNAME=production_user
DB_PASSWORD=secure_password

# Cache & Queue
CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# External APIs
EBAY_CLIENT_ID=production_client_id
EBAY_CLIENT_SECRET=production_client_secret
SHOPIFY_API_KEY=production_shopify_key
SHOPIFY_API_SECRET=production_shopify_secret

# Mail
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=mail_password
```

### Security Configuration
- Enable HTTPS everywhere
- Configure CSP headers
- Set secure session cookies
- Implement rate limiting
- Regular security updates

## Monitoring and Logging

### Application Monitoring
```php
// config/logging.php
'channels' => [
    'production' => [
        'driver' => 'stack',
        'channels' => ['daily', 'slack'],
    ],
    
    'daily' => [
        'driver' => 'daily',
        'path' => storage_path('logs/laravel.log'),
        'level' => 'info',
        'days' => 14,
    ],
    
    'slack' => [
        'driver' => 'slack',
        'url' => env('LOG_SLACK_WEBHOOK_URL'),
        'username' => 'Laravel Log',
        'level' => 'error',
    ],
],
```

### Health Checks
```php
// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status' => 'healthy',
        'database' => DB::connection()->getPdo() ? 'connected' : 'disconnected',
        'cache' => Cache::get('health-check') ? 'working' : 'not working',
        'queue' => Queue::size() < 1000 ? 'normal' : 'high',
    ]);
});
```

### Performance Monitoring
- Set up APM tools (New Relic, DataDog)
- Monitor key metrics (response time, error rate)
- Configure alerting thresholds
- Implement custom metrics tracking

## Backup and Recovery

### Database Backups
```bash
# Daily backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://backups/database/
rm backup_$DATE.sql
```

### File Backups
- Regular backups of uploaded files
- Configuration file backups
- Log file archival
- Application code snapshots

### Recovery Procedures
1. **Database Recovery**
   - Restore from latest backup
   - Replay transaction logs if available
   - Verify data integrity
   - Update application caches

2. **Application Recovery**
   - Deploy previous stable version
   - Restore configuration files
   - Restart all services
   - Monitor for issues

## Performance Optimization

### Production Optimizations
```bash
# Laravel optimizations
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan queue:restart

# Composer optimizations
composer install --optimize-autoloader --no-dev

# OPcache configuration
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
```

### Web Server Optimization
- Enable gzip compression
- Configure browser caching
- Optimize SSL configuration
- Enable HTTP/2 support

### Database Optimization
- Regular ANALYZE TABLE operations
- Monitor slow query log
- Optimize database configuration
- Implement connection pooling

## Rollback Procedures

### Application Rollback
1. Deploy previous version
2. Rollback database migrations
3. Clear application caches
4. Restart queue workers
5. Monitor application health

### Database Rollback
1. Stop application processes
2. Restore database backup
3. Verify data consistency
4. Update application configuration
5. Resume normal operations

### Emergency Procedures
- Enable maintenance mode
- Notify stakeholders
- Implement temporary fixes
- Document incident details
- Conduct post-mortem review