# API Integrations

## eBay API Integration

### Authentication
- OAuth 2.0 flow implementation
- Token refresh mechanism
- Rate limiting compliance
- Sandbox and production environments

### Key eBay APIs Used
1. **Trading API**: Legacy product and order management
2. **Inventory API**: Modern inventory management
3. **Account API**: User account information
4. **Sell Feed API**: Bulk operations
5. **Browse API**: Product browsing and search

### Common eBay Operations
- Product listing and updates
- Inventory synchronization
- Order fulfillment
- Category management
- Policy management (shipping, return, payment)

### Error Handling
- Rate limit exceptions
- Token expiration handling
- Duplicate product upload detection
- API-specific error mapping

## Shopify API Integration

### Authentication
- Shopify App authentication
- Session management
- Webhook verification
- Scope management

### Key Shopify APIs Used
1. **Admin API**: Core store management
2. **GraphQL API**: Bulk operations
3. **REST API**: Traditional operations
4. **Webhook API**: Real-time updates

### Common Shopify Operations
- Product creation and updates
- Order synchronization
- Collection management
- Location management
- Webhook handling

### Webhook Handlers
- Product updates
- Order updates
- Collection changes
- App uninstallation
- Location creation/deactivation

## Rate Limiting Strategy

### eBay Rate Limits
- Trading API: 5,000 calls/day
- Inventory API: 1,000 calls/hour
- Browse API: Variable based on usage

### Shopify Rate Limits
- REST API: 2 calls/second (burst: 40)
- GraphQL API: 1,000 cost units/minute
- Webhook processing: No strict limits

### Implementation
- Queue-based processing
- Exponential backoff
- Rate limit monitoring
- Graceful degradation

## Data Synchronization

### Product Sync Flow
1. Fetch eBay products
2. Transform data format
3. Create/update Shopify products
4. Handle errors and retries
5. Update sync status

### Order Sync Flow
1. Receive order webhooks
2. Match with eBay listings
3. Create eBay fulfillment
4. Update Shopify order status
5. Handle exceptions

### Inventory Sync
- Real-time inventory updates
- Bulk inventory corrections
- Location-based inventory
- Stock level thresholds