# Database Schema Overview

## Core Tables

### User Management
```sql
-- users: Application users
-- shops: Shopify shop information
-- sessions: User sessions with OAuth tokens
-- user_preferences: User-specific configuration
```

### Product Management
```sql
-- ebay_products: eBay product data
-- shopify_products: Shopify product mappings
-- product_sync_status: Synchronization tracking
-- bulk_query_operations: Bulk operation tracking
```

### Order Management
```sql
-- orders: Order synchronization data
-- order_line_items: Individual order items
-- order_sync_logs: Synchronization history
```

### System Configuration
```sql
-- app_configurations: Application settings
-- app_plans: Subscription plan information
-- activity_logs: User activity tracking
-- failed_jobs: Failed queue jobs
```

### eBay Specific
```sql
-- ebay_users: eBay user account data
-- ebay_categories: eBay category mappings
-- ebay_store_categories: Custom store categories
-- ebay_policies: Shipping, return, payment policies
```

### Shopify Specific
```sql
-- shopify_locations: Store locations
-- shopify_collections: Product collections
-- shopify_webhooks: Webhook configurations
```

## Key Relationships

### Product Relationships
- `ebay_products` → `shopify_products` (one-to-one mapping)
- `users` → `ebay_products` (user owns products)
- `bulk_query_operations` → `ebay_products` (batch operations)

### Order Relationships
- `orders` → `order_line_items` (one-to-many)
- `order_line_items` → `ebay_products` (product linkage)
- `users` → `orders` (user owns orders)

### Configuration Relationships
- `users` → `user_preferences` (one-to-one)
- `users` → `ebay_users` (eBay account linkage)
- `users` → `app_configurations` (user settings)

## Indexing Strategy

### Primary Indexes
- Primary keys on all tables
- Unique constraints on business keys
- Foreign key indexes for relationships

### Performance Indexes
```sql
-- Product queries
INDEX idx_ebay_products_user_id ON ebay_products(user_id);
INDEX idx_ebay_products_item_id ON ebay_products(item_id);
INDEX idx_ebay_products_sku ON ebay_products(sku);

-- Order queries
INDEX idx_orders_user_id ON orders(user_id);
INDEX idx_orders_status ON orders(status);
INDEX idx_orders_created_at ON orders(created_at);

-- Session queries
INDEX idx_sessions_user_id ON sessions(user_id);
INDEX idx_sessions_shop_domain ON sessions(shop_domain);
```

## Data Types and Constraints

### Common Patterns
- `id`: BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY
- `user_id`: BIGINT UNSIGNED (foreign key)
- `created_at`, `updated_at`: TIMESTAMP
- `deleted_at`: TIMESTAMP NULL (soft deletes)

### JSON Columns
- Product specifications and variants
- API response caching
- Configuration settings
- Error details and logs

### Enum Columns
- Order status values
- Sync status values
- User roles and permissions
- Product conditions

## Migration Strategy

### Versioning
- Sequential migration numbers
- Descriptive migration names
- Rollback-safe operations
- Data seeding for reference data

### Best Practices
- Add columns before removing
- Use transactions for complex changes
- Test migrations on staging data
- Backup before major changes