<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Ebay RU Value
    |--------------------------------------------------------------------------
    | Helpful Link: https://developer.ebay.com/my/auth
    */

    'ru_value' => env('EBAY_RU_VALUE', 'Roshan_Bhattara-SubashGh-CartPr-wkihuts'),

    /*
   |--------------------------------------------------------------------------
   | Ebay Client ID
   |--------------------------------------------------------------------------
   | Helpful Link: https://developer.ebay.com/my/keys
   */

    'client_id' => env('EBAY_CLIENT_ID', 'SubashGh-CartProd-SBX-3ab9522e2-ee95fff2'),

    /*
   |--------------------------------------------------------------------------
   | Ebay Client Secret
   |--------------------------------------------------------------------------
   | Helpful Link: https://developer.ebay.com/my/keys
   */

    'client_secret' => env('EBAY_CLIENT_SECRET', 'SBX-ab9522e2257e-dd2b-4804-abfc-90a0'),

    /*
   |--------------------------------------------------------------------------
   | Ebay OAuth token-request endpoints
   |--------------------------------------------------------------------------
   | Helpful Link: https://developer.ebay.com/api-docs/static/oauth-auth-code-grant-request.html
   */

    'oauth_endpoint' => env('EBAY_OAUTH_ENDPOINT', 'https://api.sandbox.ebay.com/identity/v1/oauth2/token'),

    'dev_id' => env('EBAY_DEV_ID', '8a0a7991-f0c3-4fbb-a1f6-0d8ef3f09d6a'),

    'sandbox' => env('EBAY_IS_SANDBOX', false),
    'verification_token' => env('EBAY_VERIFICATION_TOKEN'),
    'mockAPI' => env('EBAY_ORDER_MOCK_API'),
    'sandbox_url' => env('EBAY_SANDBOX_URL', 'https://api.sandbox.ebay.com'),
    'production_url' => env('EBAY_PRODUCTION_URL', "https://api.ebay.com"),

    'customer_support_email_address' => env('CUSTOMER_SUPPORT_EMAIL_ADDRESS', "<EMAIL>"),
    'smtp_mail_from_address' => env('MAIL_FROM_ADDRESS', "<EMAIL>"),
    'customer_support_person_email_address' => env('CUSTOMER_SUPPORT_PERSON_EMAIL_ADDRESS', "<EMAIL>"),
    'customer_support_phone' => env('CUSTOMER_SUPPORT_PHONE', "****** 415 2736"),
    /*
    |--------------------------------------------------------------------------
    | Order Sync Failed Mail Interval
    |--------------------------------------------------------------------------
    | The interval in minutes between sending order sync failed emails.
    */
    'order_sync_failed_mail_interval' => env('ORDER_SYNC_FAILED_MAIL_INTERVAL_IN_MINUTES', 360),

    /*
    |--------------------------------------------------------------------------
    | Eps Test Mode
    |--------------------------------------------------------------------------
    | The mode to test the eps upload.
    */
    'eps_test_mode' => env('EPS_TEST_MODE', false),
];
