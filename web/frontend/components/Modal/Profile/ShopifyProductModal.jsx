import {useAppQuery} from "../../../hooks/index.js";
import React, {useCallback, useContext, useEffect, useState} from "react";
import {
    Box,
    Button,
    ButtonGroup,
    Checkbox,
    Divider,
    Filters,
    Image,
    InlineGrid,
    InlineStack,
    LegacyFilters,
    Link,
    Modal,
    ResourceItem,
    ResourceList,
    Scrollable,
    Spinner,
    Text
} from "@shopify/polaris";
import ImageThumbnail from "../../Thumbnail/ImageThumbnail.jsx";
import debounce from "../../../util/Debounce.js";
import {t} from "i18next";
import {ebayIcon, shopifyIcon} from "../../../assets/index.js";
import {useNavigate} from "react-router-dom";
import FilterShopifyProductsByCollection from "../../Product/Filter/FilterShopifyProductsByCollection.jsx";
import EmptyProductState from "../../Product/Empty State/EmptyProductState.jsx";
import SelectAllCheckboxComponent from "../../Framework/SelectAllCheckboxComponent.jsx";
import {ALL, COLLECTION, PRODUCT} from "../../../data/constant.js";
import EbayConnectionContext from "../../../context/EbayConnection/EbayConnectionContext.jsx";
import BaseBannerComponent from "../../Banner/BaseBannerComponent.jsx";

export default function ShopifyProductModal({profileId, setAddProductModalOpen}) {
    const navigate = useNavigate()
    const [queryValue, setQueryValue] = useState("");
    const [searchQuery, setSearchQuery] = useState("");
    const [consent, setConsent] = useState(false);
    const [products, setProducts] = useState([]);
    const [nextPageUrl, setNextPageUrl] = useState("");
    const [selectedItems, setSelectedItems] = useState([] || "");
    const [isLoadingAddAction, setIsLoadingAddAction] = useState(false);
    const fullUrl = new URL(window.location.origin + `/api/shopify/products?excluded_profile=${profileId}&related_fields=profile`);
    const [url, setUrl] = useState(fullUrl.href);
    const [showConsentCheckbox, setShowConsentCheckbox] = useState(false);
    const [selectedCollection, setSelectedCollection] = useState(undefined)
    const [shopifyCollectionOptions, setShopifyCollectionOptions] = useState([])
    const [appliedFilters, setAppliedFilters] = useState([])
    const ebayConnectionContext = useContext(EbayConnectionContext)

    const debouncedSearchOnChange = useCallback(
        debounce((value) => {
            setSearchQuery(value)
        }, 600),
        []
    )

    const handleQueryValueChange = useCallback(
        (value) => {
            setQueryValue(value)
            debouncedSearchOnChange(value)
        }, [debouncedSearchOnChange])

    const {isFetching} = useAppQuery(
        {
            url: url,
            reactQueryOptions: {
                onSuccess: (res) => {
                    setNextPageUrl(res.result.products.next_page_url);
                    const urlParams = new URLSearchParams(url);
                    if (urlParams.get('page')) {
                        setProducts((prevVariants) => [...prevVariants, ...res.result.products.data]);
                    } else {
                        setProducts(res.result.products.data)
                    }
                },
            },
        }
    );

    const handleScrollableBottomCall = useCallback(() => {
        if (nextPageUrl && !isFetching) {
            setUrl(nextPageUrl);
        }
    }, [nextPageUrl, isFetching])
    const resetModal = () => {
        setAddProductModalOpen(false)
    }
    const performAPICall = async () => {
        const response = await fetch('/api/ebay/product-upload', {
            method: "POST",
            body: JSON.stringify({
                items: selectedItems === ALL ? selectedCollection : selectedItems,
                profile_type: selectedItems === ALL ? COLLECTION : PRODUCT,
                move_all_collection_products: selectedItems === ALL,
                profile_id: profileId
            }),
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        return response.json()
    }

    const handleAddProductsToProfile = () => {
        if (!selectedItems.length) {
            setAddProductModalOpen(false)
            return;
        }
        setIsLoadingAddAction(true)
        performAPICall().then((res) => {
            setIsLoadingAddAction(false)
            if (res.success && res.result.process_completed) {
                if (res.result.refresh_token_expired){
                    ebayConnectionContext.setEbayConnection(
                        { ...ebayConnectionContext.ebayConnection, refresh_token_expired: 1 }
                    )
                    setAddProductModalOpen(false)
                }
            }else if (res.success){
                navigate('/progress', {
                    state: {
                        progressType: 'uploadMessageShopify',
                        redirectUrl: '/profiles/products/' + profileId,
                        isShopify: true
                    }
                })
            }
        })
    }
    const handleChange = useCallback(
        (newChecked) => setConsent(newChecked),
        [],
    );
    const handleSelectionChange = (newSelectedItems) => {
        setSelectedItems(newSelectedItems);
        if (newSelectedItems === 'All') {
            return;
        }
        const hasEbayProduct = newSelectedItems.some(item => {
            const product = products.find(p => p.id === item)
            if (product && (product.ebay_product_id || product.profile_id)) {
                return product.id
            }
            return false;
        });
        setShowConsentCheckbox(hasEbayProduct);
    };

    function disambiguateLabel(key, value) {
        switch (key) {
            case 'collection':
                const collection = shopifyCollectionOptions.find(option => option.value === value[0]);
                return collection ?
                    t('shopifyProducts.filters.collectionFilter.selectionLabel', {label: collection.label}) :
                    '';
            default:
                return value;
        }
    }

    function isEmpty(value) {
        if (Array.isArray(value)) {
            return value.length === 0;
        } else {
            return value === '' || value == null;
        }
    }

    useEffect(() => {
        if (selectedCollection && !isEmpty(selectedCollection)) {
            fullUrl.searchParams.set('collection_id_filter_by', selectedCollection.toString())
        }
        if (searchQuery && searchQuery.length > 0) {
            fullUrl.searchParams.set('search', searchQuery)
        }
        setSelectedItems([])
        setUrl(fullUrl.href)
    }, [selectedCollection, searchQuery])
    const handleCollectionRemove = useCallback(() => {
        setSelectedCollection(undefined)
    }, [])
    const handleQueryValueRemove = useCallback(() => {
        setQueryValue('')
        setSearchQuery('')
    }, [])

    useEffect(() => {
        const filters = []
        if (selectedCollection && !isEmpty(selectedCollection)) {
            const key = 'collection';
            filters.push({
                key,
                label: disambiguateLabel(key, selectedCollection),
                onRemove: () => {
                    handleCollectionRemove()
                },
            });
        }
        setAppliedFilters(filters)
    }, [selectedCollection]);

    const filters = [
        {
            key: 'collection',
            label: t('shopifyProducts.filters.collectionFilter.filterName'),
            filter: (
                <FilterShopifyProductsByCollection
                    selectedCollection={selectedCollection}
                    setSelectedCollection={setSelectedCollection}
                    shopifyCollectionOptions={shopifyCollectionOptions}
                    setShopifyCollectionOptions={setShopifyCollectionOptions}
                />
            ),
            pinned: true,
            shortcut: true,
        },
    ];


    const renderResourceItem = (item) => {
        const {id, title} = item

        const media = (
            <ImageThumbnail size="small" source={item.image_src} alt="shopify-variant-image"/>
        )


        return (
            <ResourceItem
                id={id}
                media={media}
                onClick={() => {
                    handleSelectionChange([...selectedItems, id])
                }}
            >
                <Box>
                    <InlineGrid columns={['twoThirds', 'oneThird']}>
                        <div>
                            <Text variant="bodyMd" fontWeight="bold" as="h3" breakWord={true}>
                                {title}
                            </Text>
                            {
                                item.profile?.id &&
                                <div>
                                    <InlineStack>
                                        Current Profile: <Text as={"p"} fontWeight={"bold"}
                                                               tone={"magic-subdued"}>{item.profile.profile_name}</Text>
                                    </InlineStack>
                                </div>
                            }
                        </div>
                        <div>
                            {
                                item.shopify_product_id &&
                                <Link
                                    url={item.shopify_product_url}
                                    target={"_blank"}
                                >
                                    <Image source={shopifyIcon} alt="shopify-icon" width={30}/>
                                </Link>

                            }
                            {item.ebay_product_id &&
                                <Link
                                    url={item.ebay_product_url}
                                    target={"_blank"}
                                    onClick={() => window.open(item.ebay_product_url, '_blank')}
                                >
                                    <Image source={ebayIcon} alt="eBay-icon" width={60}/>
                                </Link>
                            }
                        </div>
                    </InlineGrid>
                </Box>
            </ResourceItem>
        )
    }

    return (
        <Modal onHide={resetModal} onClose={resetModal} open title={t('common.addProducts')}>
            <Box style={{ display: 'flex', flexDirection: 'column', height: '70vh' }}>
                <Box paddingInline={"300"} paddingBlock="200">
                    <Filters
                        queryPlaceholder={t('common.searchPlaceholder')}
                        queryValue={queryValue}
                        filters={filters}
                        appliedFilters={appliedFilters}
                        onQueryChange={handleQueryValueChange}
                        onQueryClear={handleQueryValueRemove}
                        onClearAll={handleQueryValueRemove}
                    />
                </Box>
                {
                    selectedCollection && products.length > 0 &&
                    <BaseBannerComponent
                        bannerKey="shopify-product-modal-banner"
                        tone="info"
                    >
                        <p>
                            {t('profiles.shopifyProductModal.selectAllResultInfo')}
                        </p>
                    </BaseBannerComponent>
                }
                {
                    !!products.length &&
                    <SelectAllCheckboxComponent
                        setSelectedItems={handleSelectionChange}
                        selectedItems={selectedItems}
                        items={products}
                        canSelectAll={selectedCollection?.length}
                    />
                }

                <Box style={{ flex: 1, overflow: 'hidden' }}>
                    <Scrollable style={{ height: "100%" }}
                                onScrolledToBottom={handleScrollableBottomCall}>
                        <ResourceList
                            items={products}
                            renderItem={renderResourceItem}
                            showHeader={false}
                            selectedItems={selectedItems}
                            selectable
                            onSelectionChange={handleSelectionChange}
                            emptySearchState={isFetching ? <></> : <EmptyProductState/>}
                            emptyState={isFetching ? <></> : <EmptyProductState/>}
                        />
                        {
                            isFetching &&
                            <InlineStack align="center">
                                <Spinner size="large"/>
                            </InlineStack>
                        }
                    </Scrollable>
                </Box>

                <Box style={{ flexShrink: 0 }}>
                    <Divider borderColor="border-secondary"/>
                    <Box padding="200">
                        {
                            ((showConsentCheckbox && selectedItems.length) || selectedItems === ALL) &&
                            <Checkbox
                                label={t('profiles.shopifyProductModal.confirmationCheckboxLabel')}
                                checked={consent}
                                onChange={handleChange}
                                helpText={t('profiles.shopifyProductModal.confirmationCheckboxHelpText')}
                            />
                        }
                        <InlineStack>
                            <ButtonGroup>
                                <Button variant="primary"
                                        onClick={handleAddProductsToProfile}
                                        loading={isLoadingAddAction}
                                        disabled={(showConsentCheckbox || selectedItems === ALL) && !consent}>Done
                                </Button>
                                <Button onClick={() => setAddProductModalOpen(false)}
                                        disabled={isLoadingAddAction}>Cancel
                                </Button>
                            </ButtonGroup>
                        </InlineStack>
                    </Box>
                </Box>
            </Box>
        </Modal>
    );
}
