import { <PERSON><PERSON>tack, <PERSON>, <PERSON>Field, Modal } from "@shopify/polaris";
import React, { useCallback, useContext, useState } from "react";
import { t } from "i18next";
import EbayConnectionContext from "../../../context/EbayConnection/EbayConnectionContext.jsx";
import { useNavigate } from "react-router-dom";
import { useAuthenticatedFetch } from "../../../hooks/index.js";
import BaseBannerComponent from "../../Banner/BaseBannerComponent.jsx";

export default function ChangeEbayStoreModal({ onClose }) {
    const fetch = useAuthenticatedFetch()
    const [consentValue, setConsentValue] = useState('')
    const [loading, setLoading] = useState(false)
    const ebayConnectionContext = useContext(EbayConnectionContext)
    const navigate = useNavigate();

    const handleConsentValue = useCallback(
        (newValue) => setConsentValue(newValue),
        [],
    )
    const performRemoveApiCall = async () => {
        const response = await fetch('/api/ebay/change-store', {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 'ebay_user_id': ebayConnectionContext.ebayConnection.id })
        })
        return response.json()
    }

    const handleChangeEbayStore = () => {
        setLoading(true)
        performRemoveApiCall().then((res) => {
            setLoading(false)
            onClose()
            res.success ?
                navigate('/welcome') :
                console.error(res);
        })
    }

    const handleCancel = () => {
        onClose()
    }

    return (
        <Modal
            open={true}
            onClose={onClose}
            title={t('dashboard.changeEbayStoreModal.title')}
            primaryAction={{
                content: t('common.confirm'),
                onAction: handleChangeEbayStore,
                disabled: consentValue.toUpperCase() !== 'CONFIRM',
                loading: loading
            }}
            secondaryActions={[{
                content: t('common.cancel'),
                onAction: handleCancel,
                disabled: loading
            }]}
        >
            <Box padding={"200"}>
                <BlockStack gap={"200"}>
                    <BaseBannerComponent
                        bannerKey="change-ebay-store-modal-banner"
                        tone="warning"
                    >
                        {t('dashboard.changeEbayStoreModal.message')}
                    </BaseBannerComponent>
                    <Box padding={"200"}>
                        <TextField
                            label={t('common.IConsentTheRequest')}
                            value={consentValue}
                            onChange={handleConsentValue}
                            helpText={t('dashboard.changeEbayStoreModal.helpText')}
                            autoComplete="off"
                        />
                    </Box>
                </BlockStack>
            </Box>
        </Modal>
    );
}
