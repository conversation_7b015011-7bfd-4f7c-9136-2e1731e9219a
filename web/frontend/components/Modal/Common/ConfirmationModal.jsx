import { Modal } from "@shopify/polaris";
import { t } from "i18next";
import PropTypes from 'prop-types';

export default function ConfirmationModal({
    open,
    message,
    title,
    onConfirm,
    onCancel,
    confirmText = t('common.confirm'),
    cancelText = t('common.cancel'),
    isLoading = false
}) {
    return (
        <Modal
            open={open}
            onClose={onCancel}
            title={title}
            primaryAction={{
                content: confirmText,
                onAction: onConfirm,
                disabled: isLoading,
                loading: isLoading
            }}
            secondaryActions={[{
                content: cancelText,
                onAction: onCancel,
                disabled: isLoading
            }]}
        >
            <Modal.Section>
                {message}
            </Modal.Section>
        </Modal>
    );
}

ConfirmationModal.propTypes = {
    open: PropTypes.bool.isRequired,
    message: PropTypes.node.isRequired,
    title: PropTypes.string.isRequired,
    onConfirm: PropTypes.func.isRequired,
    onCancel: PropTypes.func.isRequired,
    confirmText: PropTypes.string,
    cancelText: PropTypes.string,
    isLoading: PropTypes.bool
};
