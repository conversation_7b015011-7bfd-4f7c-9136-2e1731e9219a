import {useState} from 'react';
import {useAuthenticatedFetch} from "../../../hooks/index.js";
import {t} from 'i18next';
import ConfirmationModal from "../Common/ConfirmationModal.jsx";
import PropTypes from 'prop-types';

export default function LinkProductModal({refetchProducts, item, open, onClose}) {
    const [isApiCall, setIsApiCall] = useState(false)
    const fetch = useAuthenticatedFetch()

    const performCreateApiCall = async (itemId) => {
        const response = await fetch(`/api/ebay/product/create/${itemId}`, {
            method: "POST"
        })
        const responseData = await response.json(); // Assuming the response always has a message
        return {success: response.ok, message: responseData.message};
    }

    const createInShopify = async (itemId) => {
        setIsApiCall(true)
        const res = await performCreateApiCall(itemId)
        shopify.toast.show(res.message, {
            duration: 3000,
            isError: !res.success
        });
        refetchProducts().then(() => {
                setIsApiCall(false)
            }
        ).then(() => {
            onClose()
        })
    }

    const handleCancel = () => {
        onClose()
    }

    return (
        <ConfirmationModal
            open={open}
            title={t('ebayProducts.importEbayProductModal.title', {title: item.title})}
            message={t('ebayProducts.importEbayProductModal.description')}
            onConfirm={() => createInShopify(item.id)}
            onCancel={handleCancel}
            confirmText={t('ebayProducts.createInShopify')}
            isLoading={isApiCall}
        />
    );
}

LinkProductModal.propTypes = {
    refetchProducts: PropTypes.func.isRequired,
    item: PropTypes.shape({
        id: PropTypes.number.isRequired,
        title: PropTypes.string.isRequired
    }).isRequired,
    open: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired
};
