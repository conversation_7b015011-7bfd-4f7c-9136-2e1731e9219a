import { BlockStack, Box, TextField, Text, Modal } from "@shopify/polaris";
import { useCallback, useState } from "react";
import { t } from "i18next";
import BaseBannerComponent from "../../Banner/BaseBannerComponent";
import PropTypes from 'prop-types';

export default function EndListingInEbayModal({ onDelete, loading, open, onClose }) {
    const [value, setValue] = useState('');

    const handleChange = useCallback(
        (newValue) => setValue(newValue),
        [],
    );
    
    const handleConfirm = useCallback(() => {
        if (value.toUpperCase() === 'DELETE') {
            onDelete();
        }
    }, [value, onDelete]);

    const handleCancel = () => {
        onClose();
    };

    return (
        <Modal
            open={open}
            onClose={onClose}
            title="End listing in eBay?"
            primaryAction={{
                content: t('common.confirm'),
                onAction: handleConfirm,
                disabled: value.toUpperCase() !== 'DELETE',
                loading: loading
            }}
            secondaryActions={[{
                content: t('common.cancel'),
                onAction: handleCancel,
                disabled: loading
            }]}
        >
            <Modal.Section>
                <BlockStack gap={"200"}>
                    <BaseBannerComponent
                        bannerKey="end-listing-in-ebay-modal-banner"
                        tone="warning"
                    >{t('common.unexpectedBadThingsMessage')}</BaseBannerComponent>
                    
                    <Box padding={"200"}>
                        <Text variant="bodyMd" as="p">
                            This action <b>CANNOT </b> be undone. This will permanently end your listing in eBay before
                            the expiry date. Please type <b>DELETE </b> below in the input field to confirm your action.
                        </Text>
                    </Box>

                    <Box padding={"200"}>
                        <TextField
                            label={t('common.iConsentRequest')}
                            placeholder={t('common.writeToConfirm', {text: 'DELETE'})}
                            value={value}
                            onChange={handleChange}
                            autoComplete="off"
                        />
                    </Box>
                </BlockStack>
            </Modal.Section>
        </Modal>
    );
}

EndListingInEbayModal.propTypes = {
    onDelete: PropTypes.func.isRequired,
    loading: PropTypes.bool,
    open: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired
};
