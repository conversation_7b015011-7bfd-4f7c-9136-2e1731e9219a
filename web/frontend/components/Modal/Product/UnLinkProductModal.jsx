import {useAuthenticatedFetch} from "../../../hooks/index.js";
import {useState} from "react";
import ConfirmationModal from "../Common/ConfirmationModal.jsx";
import PropTypes from 'prop-types';

export default function UnLinkProductModal({refetchProducts, item, open, onClose}) {
    const [isApiCall, setIsApiCall] = useState(false)
    const fetch = useAuthenticatedFetch()

    const performUnlinkProductApiCall = async (itemId) => {
        const response = await fetch(`/api/ebay/unlink-listing/${itemId}`, {
            method: "POST"
        })
        const responseData = await response.json(); // Assuming the response always has a message
        return { success: response.ok, message: responseData.message };

    }
    const unlinkProductInShopify = async (itemId) => {
        setIsApiCall(true)
        const res = await performUnlinkProductApiCall(itemId)

        shopify.toast.show(res.message, {
            duration: 3000,
            isError: !res.success
        });

        // closes modal and send toast
        refetchProducts().then(() => {
            setIsApiCall(false)
        }).then(() => {
            onClose()
        })
    }

    const handleCancel = () => {
        onClose()
    }

    return (
        <ConfirmationModal
            open={open}
            title={`Delete ${item.title} from Shopify`}
            message="This process cannot be undone. Please wait until it completes"
            onConfirm={() => unlinkProductInShopify(item.id)}
            onCancel={handleCancel}
            confirmText="Delete Now"
            isLoading={isApiCall}
        />
    );
}

UnLinkProductModal.propTypes = {
    refetchProducts: PropTypes.func.isRequired,
    item: PropTypes.shape({
        id: PropTypes.number.isRequired,
        title: PropTypes.string.isRequired
    }).isRequired,
    open: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired
};
