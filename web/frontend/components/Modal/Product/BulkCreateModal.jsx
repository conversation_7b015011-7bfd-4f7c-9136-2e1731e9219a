import {useState} from 'react';
import {useAuthenticatedFetch} from "../../../hooks/index.js";
import ConfirmationModal from "../Common/ConfirmationModal.jsx";
import PropTypes from 'prop-types';
import {t} from "i18next";

export default function BulkCreateModal({selectedItemLocation, open, onClose}) {

    const [isApiCall, setIsApiCall] = useState(false)
    const fetch = useAuthenticatedFetch()

    const performBulkCreateApiCall = async () => {
        const response = await fetch(`/api/ebay/product/bulk-create`, {
            method: "POST",
            body: JSON.stringify({
                createAllProducts: true,
                selectedItemLocation: selectedItemLocation
            }),
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        setIsApiCall(false)
        return response.ok
    }

    const bulkCreateInShopify = async () => {
        setIsApiCall(true)
        performBulkCreateApiCall()
        //navigate to import progress page
    }

    const handleCancel = () => {
        onClose()
    }
   

    return (
        <ConfirmationModal
            open={open}
            title={t('ebayProducts.bulkCreateModal.title')}
            message={t('ebayProducts.bulkCreateModal.message')}
            onConfirm={bulkCreateInShopify}
            onCancel={handleCancel}
            confirmText={t('ebayProducts.bulkCreateModal.confirmText')}
            isLoading={isApiCall}
        />
    );
}

BulkCreateModal.propTypes = {
    selectedItemLocation: PropTypes.string,
    open: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired
};
