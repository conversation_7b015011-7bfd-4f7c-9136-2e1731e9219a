import { ChoiceList } from "@shopify/polaris";
import { useCallback } from "react";

export default function StatusFilter({ statusOptions, selectedStatus, setStatus }) {
    const handleStatusChange = useCallback((value) => {
        setStatus(value[0])
    }, [])

    return (
        <ChoiceList
            titleHidden
            allowMultiple={false}
            choices={statusOptions}
            selected={selectedStatus}
            onChange={handleStatusChange}
            title={""}
        />
    )
}