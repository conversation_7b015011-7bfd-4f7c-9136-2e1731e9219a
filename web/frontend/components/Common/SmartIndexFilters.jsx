import { IndexFilters, useSetIndexFiltersMode } from "@shopify/polaris";
import { useCallback, useEffect, useState } from "react";
import debounce from "../../util/Debounce";

export default function SmartIndexFilters({
    activeTab = 'all',
    setActiveTab,
    setSearchQuery,
    handleFiltersClearAll = null,
    handleCancelAction: customHandleCancelAction = null,
    loading = false,
    filters = [],
    appliedFilters = [],
    views = [],
    searchPlaceHolder = '',
    sortOptions = [],
    sortSelected = [],
    onSort = () => {}
}) {
    const [queryValue, setQueryValue] = useState('')
    const {mode, setMode} = useSetIndexFiltersMode()
    const [selected, setSelected] = useState(0)

    useEffect(() => {
        setSelected(views.findIndex((view) => view.key === activeTab) || 0);
    }, [activeTab])
   
    const tabs = views.map((item, index) => {
        return {
            id: `${item.tabName}-${index}`,
            content: `${item.tabName}`,
            badge: item?.count,
            accessibilityLabel: item.tabName,
            index,
            onAction: () => {
                setActiveTab(item.key)
            },
            isLocked: index === 0,
            panelID: `${item.tabName}-${index}`,
        }
    })
      
    const handleCancelAction = useCallback(() => {
        if (customHandleCancelAction) {
            customHandleCancelAction()
        }
        if(queryValue) {
            setQueryValue('')
            setSearchQuery('')
        }
    }, [queryValue]);


    // search
    const setSearchedQueryOnChange = (value) => {
        setSearchQuery(value)
    }

    const debouncedSearchOnChange = debounce(setSearchedQueryOnChange, 600);

    // sets the query value when any key is pressed
    const handleOnQueryChangeWrapper = useCallback(
        (value) => {
            setQueryValue(value)
            debouncedSearchOnChange(value)
        },
        [],
    );
    const handleOnQueryRemoveWrapper = useCallback(() => {
        setQueryValue('')
        setSearchQuery('')
    }, []);

    return (
        <IndexFilters
            cancelAction={{
                onAction: handleCancelAction,
                disabled: false,
                loading: false,
            }}
            sortOptions={sortOptions}
            sortSelected={sortSelected}
            onSort={onSort}
            queryValue={queryValue}
            onQueryChange={handleOnQueryChangeWrapper}
            onQueryClear={handleOnQueryRemoveWrapper}
            queryPlaceholder={searchPlaceHolder}
            tabs={tabs}
            mode={mode}
            setMode={setMode}
            canCreateNewView={false}
            filters={filters}
            loading={loading}
            selected={selected}
            onSelect={setSelected}
            appliedFilters={appliedFilters}
            onClearAll={handleFiltersClearAll}
            
        />
    )
}