import {Bleed, Card, Icon, Text, TextField, Modal} from "@shopify/polaris";
import React, {useCallback, useEffect, useState} from "react";
import StarRating from "../Rating/StarRating.jsx";
import {XSmallIcon} from "@shopify/polaris-icons";
import {t} from "i18next";
import {useAppQuery} from "../../hooks/index.js";

export default function AppRatingBanner() {

   useAppQuery({
        url: `/api/rating/session`, reactQueryOptions: {
            onSuccess: (data) => {
                if (data?.result !== undefined) {
                    dismissRatingBanner();
                } else {
                    sessionStorage.setItem('showRatingBanner','true')
                    setShowRatingBanner(true);
                }
            },
        },
    });

    const [showRatingBanner, setShowRatingBanner] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [showModal, setShowModal] = useState(false);

    const [rating, setRating] = useState(0);

    const [review, setReview] = useState('');

    const handleReviewTextChange = useCallback((value) => setReview(value), []);

    const [showThanksCard, setShowThanksCard] = useState(false)

    const renderThanksCard = () => {
        return <Card background={'bg-surface-secondary'}>
            <div className="flex">
                <div className="col">
                    <Text as={"p"}>
                        {t('appRating.thanksForRatingText')}
                    </Text>

                </div>

                <div style={{marginTop: '0px'}} className="col-auto icon-1x">
                    <a href="#"
                       onClick={() => setShowThanksCard(false)}
                       style={{"textDecoration": "none"}}
                    >
                        <Icon source={XSmallIcon} tone={'base'}/>
                    </a>
                </div>
            </div>
        </Card>
    };

    useEffect(function () {

        if (rating > 0 && rating < 5) {
            showReviewModal()
        }

        if (rating === 5) {
            dismissRatingBanner()
            submitRating()
            window.open('https://apps.shopify.com/ebay-integration-sync?show_store_picker=1#modal-show=WriteReviewModal', '_blank');
        }
    }, [rating])

    const handleReset = () => {
        setRating(0)
        setShowModal(false)
    }

    const handleRating = (rate) => {
        setRating(rate)
    };

    const showReviewModal = () => {
        setShowModal(true);
    }

    const handleCloseModal = () => {
        setShowModal(false);
        handleReset();
    }

    const dismissRatingBanner = () => {
        setShowRatingBanner(false)
        sessionStorage.removeItem('showRatingBanner')
    }

    const cancelRatingBanner = () => {
        setRating(0)
        dismissRatingBanner()
        submitRating({
            isCancelled: 1
        })
    }

    const renderRatingExperienceText = () => {
        return <>

            <Text fontWeight={"bold"} as={"p"}>
                {t('appRating.ratingBannerTitle')}
            </Text>

            <div className='spacer-bottom-1x'/>

            <Text fontWeight={"regular"} as={"p"}>
                {t('appRating.ratingBannerContent')}
            </Text>

        </>
    }

    const handleSubmit = (formData = {}) => {
        setIsLoading(true)

        submitRating(formData).then(function () {
            setShowThanksCard(true)
            dismissRatingBanner()
            setShowModal(false)
        })
    }

    const submitRating = async (formData) => {
        const response = await fetch('api/rating/session', {
            method: "POST", body: JSON.stringify({
                ...{
                    'rating': rating, 'remarks': review, 'source': 'internal'
                }, ...formData
            }), headers: {
                'Accept': 'application/json', 'Content-Type': 'application/json'
            }
        })

        if (!response.ok) {
            throw new Error('Error while saving rating. Please try again later. Status : ' + response.status);
        }

        return response.json()
    }


    return (<>

        {showThanksCard && renderThanksCard()}

        {showRatingBanner && <div>
            {showModal && (
                <Modal
                    open={true}
                    onClose={handleCloseModal}
                    title={t('appRating.reviewModalTitle')}
                    primaryAction={{
                        content: t('common.submit'),
                        onAction: () => handleSubmit(),
                        loading: isLoading
                    }}
                >
                    <Modal.Section>
                        <TextField
                            type="text"
                            placeholder={t('appRating.reviewModalTextFieldPlaceHolder')}
                            label=""
                            value={review}
                            onChange={handleReviewTextChange}
                            multiline={4}
                            autoComplete="review"
                        />
                    </Modal.Section>
                </Modal>
            )}

            <Card>
                <div className="flex">
                    <div className="col">

                        <Text fontWeight={"medium"} as={'h1'}>
                            {renderRatingExperienceText()}
                        </Text>

                        <div style={{marginTop: '10px'}}>
                            <Bleed marginInlineStart={100}>
                                <StarRating
                                    totalStars={5}
                                    currentRating={rating}
                                    onClick={handleRating}
                                />
                            </Bleed>
                        </div>
                    </div>

                    <div style={{marginTop: '3px'}} className="col-auto icon-1x">
                        <a href="#"
                           onClick={() => cancelRatingBanner()}
                           style={{"textDecoration": "none"}}
                        >
                            <Icon source={XSmallIcon} tone={'base'}/>
                        </a>
                    </div>
                </div>
            </Card>
        </div>}
    </>);

}
