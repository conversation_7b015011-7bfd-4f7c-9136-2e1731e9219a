import { Card, Text, Button, InlineGrid, BlockStack, Box, Icon, InlineStack, Divider, Badge } from "@shopify/polaris";
import { useTranslation } from "react-i18next";
import whatsNewData from '../../data/whatsNew.json';


export default function WhatsNew() {
  const { t } = useTranslation();
    const getStatusBadge = (status) => {
    switch (status) {
      case 'coming-soon':
        return (
          <Badge tone="info">
            {t('dashboard.DashboardPage.AppUpdate.comingSoon')}
          </Badge>
        );
      case 'new-read':
        return (
          <Badge tone="info">
            {t('dashboard.DashboardPage.AppUpdate.newRead')}
          </Badge>
        );
      case 'updated':
        return (
          <Badge tone="info">
            {t('dashboard.DashboardPage.AppUpdate.updated')}
          </Badge>
        );
      default:
        return (
          <Badge tone="info">
            {t('dashboard.DashboardPage.AppUpdate.new')}
          </Badge>
        );
    }
  };

  return (
    <>
      <Card roundedAbove="sm">
        <BlockStack gap={300}>
          <Box>
            <Text as="h2" variant="headingSm">
            {t('dashboard.DashboardPage.AppUpdate.whatsNew')}
            </Text>
          </Box>
          <Divider />
          
          {whatsNewData.features.map((feature, index) => (
            <Box paddingBlockEnd={0} key={index}>
              <BlockStack gap="100">
                <Box>
                    {getStatusBadge(feature.status)}
                </Box>
                
                <Box paddingBlockEnd={200}>
                  <InlineStack wrap={false} align="space-between" gap={200}>
                    <Box minWidth="100px" maxWidth="80%">
                      <Text as="h3" variant="bodyMd">
                        {feature.title}
                      </Text>
                    </Box>
                    
                    {feature.learnMoreUrl && (
                      <Box style={{alignSelf: "flex-end"}}>
                        <Button
                          variant="plain"
                          onClick={() => window.open(feature.learnMoreUrl, "_blank")}
                        >
                          {t('common.learnMore')}
                        </Button>
                      </Box>
                    )}
                  </InlineStack>
                </Box>
              </BlockStack>
            </Box>
          ))}
        </BlockStack>
      </Card>
    </>
  );
}