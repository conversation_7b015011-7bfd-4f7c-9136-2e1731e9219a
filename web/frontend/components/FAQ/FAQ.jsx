import {BlockStack, Layout, Page} from "@shopify/polaris";
import {useState} from "react";
import {faqData} from "../../data/faq.js";
import FaqBanner from "../Banner/FaqBanner.jsx";
import FaqSection from "./FaqSection.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function FAQ() {
    const [openFaqId, setOpenFaqId] = useState(null);
    return (
        <>
            <TitleBar title={"FAQ"} />
            <Page>
                <FaqBanner/>
                <Layout>
                    <Layout.Section>
                        <BlockStack gap="loose">
                            {faqData.map((section) => (
                                <FaqSection
                                    key={section.id}
                                    section={section}
                                    openFaqId={openFaqId}
                                    setOpenFaqId={setOpenFaqId}
                                />
                            ))}
                        </BlockStack>
                    </Layout.Section>
                </Layout>
            </Page>
        </>
    );

}
