import React, { useState, useEffect } from 'react';
import { Card, Text, BlockStack, Button, Badge, InlineStack } from '@shopify/polaris';
import { Crisp } from 'crisp-sdk-web';
import { useSupportChat } from './SupportManager';

/**
 * SupportTest Component
 * 
 * A test component to verify Crisp integration is working properly.
 * This can be temporarily added to any page for testing purposes.
 */
export default function SupportTest() {
    const [crispStatus, setCrispStatus] = useState('checking');
    const [environmentInfo, setEnvironmentInfo] = useState({});
    const { openSupport } = useSupportChat();

    useEffect(() => {
        // Check Crisp status
        const checkCrispStatus = () => {
            try {
                if (typeof Crisp !== 'undefined' && Crisp.chat) {
                    setCrispStatus('loaded');
                } else {
                    setCrispStatus('not-loaded');
                }
            } catch (error) {
                setCrispStatus('error');
                console.error('Crisp status check error:', error);
            }
        };

        // Get environment info
        setEnvironmentInfo({
            isDev: !import.meta.env.PROD,
            isProd: import.meta.env.PROD,
            crispId: import.meta.env.VITE_CRISP_ID || 'not-set',
            mode: import.meta.env.MODE
        });

        checkCrispStatus();
        
        // Recheck every 2 seconds for the first 10 seconds
        const interval = setInterval(checkCrispStatus, 2000);
        setTimeout(() => clearInterval(interval), 10000);

        return () => clearInterval(interval);
    }, []);

    const getStatusBadge = () => {
        switch (crispStatus) {
            case 'loaded':
                return <Badge tone="success">Loaded</Badge>;
            case 'not-loaded':
                return <Badge tone="critical">Not Loaded</Badge>;
            case 'error':
                return <Badge tone="critical">Error</Badge>;
            default:
                return <Badge>Checking...</Badge>;
        }
    };

    const testCrispOpen = () => {
        try {
            openSupport();
        } catch (error) {
            console.error('Test error:', error);
            alert('Error opening support: ' + error.message);
        }
    };

    const testCrispDirect = () => {
        try {
            if (typeof Crisp !== 'undefined' && Crisp.chat) {
                Crisp.chat.open();
            } else {
                alert('Crisp is not available');
            }
        } catch (error) {
            console.error('Direct test error:', error);
            alert('Direct test error: ' + error.message);
        }
    };

    return (
        <Card>
            <BlockStack gap="400">
                <Text variant="headingMd" as="h3">
                    Support Integration Test
                </Text>
                
                <BlockStack gap="200">
                    <Text variant="headingSm" as="h4">Environment Info</Text>
                    <InlineStack gap="200" wrap>
                        <Badge tone={environmentInfo.isDev ? "info" : "subdued"}>
                            Dev: {environmentInfo.isDev ? 'Yes' : 'No'}
                        </Badge>
                        <Badge tone={environmentInfo.isProd ? "info" : "subdued"}>
                            Prod: {environmentInfo.isProd ? 'Yes' : 'No'}
                        </Badge>
                        <Badge tone={environmentInfo.crispId !== 'not-set' ? "success" : "critical"}>
                            Crisp ID: {environmentInfo.crispId !== 'not-set' ? 'Set' : 'Not Set'}
                        </Badge>
                    </InlineStack>
                </BlockStack>

                <BlockStack gap="200">
                    <Text variant="headingSm" as="h4">Crisp Status</Text>
                    <InlineStack gap="200">
                        <Text variant="bodyMd" as="span">Status:</Text>
                        {getStatusBadge()}
                    </InlineStack>
                </BlockStack>

                <BlockStack gap="200">
                    <Text variant="headingSm" as="h4">Test Actions</Text>
                    <InlineStack gap="200" wrap>
                        <Button 
                            onClick={testCrispOpen}
                            variant="primary"
                            disabled={crispStatus !== 'loaded'}
                        >
                            Test Support Hook
                        </Button>
                        <Button 
                            onClick={testCrispDirect}
                            variant="secondary"
                            disabled={crispStatus !== 'loaded'}
                        >
                            Test Direct Crisp
                        </Button>
                    </InlineStack>
                </BlockStack>

                <Text variant="bodySm" as="p" tone="subdued">
                    This test component verifies that Crisp is properly loaded and can be opened.
                    Remove this component before deploying to production.
                </Text>
            </BlockStack>
        </Card>
    );
}
