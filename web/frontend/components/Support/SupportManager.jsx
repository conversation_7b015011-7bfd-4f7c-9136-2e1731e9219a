import { useEffect, useCallback } from 'react';
import { Crisp } from 'crisp-sdk-web';
import { useAppBridge } from '@shopify/app-bridge-react';

/**
 * SupportManager Component
 * 
 * Handles Crisp chat integration and App Bridge Support API registration.
 * This component should be included in the main app to enable support functionality.
 */
export default function SupportManager() {
    const app = useAppBridge();

    /**
     * Opens Crisp chat widget
     * Includes error handling and fallback options
     */
    const openCrispChat = useCallback(() => {
        try {
            // Check if Crisp is loaded and available
            if (typeof Crisp !== 'undefined' && Crisp.chat) {
                // Open the Crisp chat widget
                Crisp.chat.open();
                
                // Optional: Show a specific message or trigger
                Crisp.message.show('text', 'Hello! How can we help you today?');
                
                console.log('Crisp chat opened successfully');
            } else {
                // Fallback: redirect to support email
                console.warn('Crisp chat not available, falling back to email');
                window.location.href = 'mailto:<EMAIL>?subject=Support Request';
            }
        } catch (error) {
            console.error('Error opening Crisp chat:', error);
            
            // Fallback: redirect to support email
            window.location.href = 'mailto:<EMAIL>?subject=Support Request';
        }
    }, []);

    /**
     * Register App Bridge Support API handler
     * This enables the "Get Support" button in the Shopify admin to trigger our custom handler
     */
    useEffect(() => {
        if (app && typeof shopify !== 'undefined' && shopify.support) {
            try {
                // Register the support handler with App Bridge
                shopify.support.registerHandler(openCrispChat);
                console.log('App Bridge Support handler registered successfully');
            } catch (error) {
                console.error('Error registering App Bridge Support handler:', error);
            }
        }
    }, [app, openCrispChat]);

    // This component doesn't render anything visible
    return null;
}

/**
 * Hook for manually triggering support chat
 * Can be used by other components to open support chat programmatically
 */
export const useSupportChat = () => {
    const openSupport = useCallback(() => {
        try {
            if (typeof Crisp !== 'undefined' && Crisp.chat) {
                Crisp.chat.open();
            } else {
                // Fallback to email
                window.location.href = 'mailto:<EMAIL>?subject=Support Request';
            }
        } catch (error) {
            console.error('Error opening support chat:', error);
            window.location.href = 'mailto:<EMAIL>?subject=Support Request';
        }
    }, []);

    return { openSupport };
};
