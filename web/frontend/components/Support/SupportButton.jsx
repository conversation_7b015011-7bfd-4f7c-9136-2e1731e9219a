import React from 'react';
import { But<PERSON> } from '@shopify/polaris';
import { QuestionCircleIcon } from '@shopify/polaris-icons';
import { useSupportChat } from './SupportManager';
import { t } from 'i18next';

/**
 * SupportButton Component
 * 
 * A reusable button component that opens Crisp chat when clicked.
 * Can be used in navigation menus, pages, or anywhere support access is needed.
 */
export default function SupportButton({ 
    variant = 'plain', 
    size = 'medium',
    fullWidth = false,
    children,
    ...props 
}) {
    const { openSupport } = useSupportChat();

    return (
        <Button
            variant={variant}
            size={size}
            fullWidth={fullWidth}
            icon={QuestionCircleIcon}
            onClick={openSupport}
            {...props}
        >
            {children || t("common.getSupport", "Get Support")}
        </Button>
    );
}
