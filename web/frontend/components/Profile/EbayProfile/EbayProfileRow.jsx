import {<PERSON><PERSON><PERSON>, But<PERSON>, IndexTable, Link, Popover, Text} from "@shopify/polaris";
import {useCallback, useState} from "react";
import {MenuHorizontalIcon} from "@shopify/polaris-icons";
import {useNavigate} from "react-router-dom";
import {useAuthenticatedFetch} from "../../../hooks/index.js";
import {t} from "i18next";
import {Trans} from "react-i18next";
import DeleteProfileModal from "../../Modal/Profile/DeleteProfileModal.jsx";


export default function EbayProfileRow({profile, refetchProfiles}) {
    const fetch = useAuthenticatedFetch();
    const [active, setActive] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    const [showModal, setShowModal] = useState(false);
    const [endListingsCheckbox, setEndListingsCheckbox] = useState(false)
    const [reasonSelected, setReason] = useState(['NotAvailable'])
    const navigate = useNavigate()
    const toggleActive = useCallback(() => setActive((active) => !active), []);
    const handleModalStateChange = useCallback(() => setShowModal(!showModal), [showModal]);

    const handleViewProducts = () => {
        toggleActive();
        navigate(`/profiles/products/` + profile.id)
    }
    const handleEditProfile = () => {
        toggleActive();
        navigate(`/profiles/` + profile.id)
    }

    const handleStatusClick = (status) => {
        navigate(`/profiles/products/${profile.id}?filter=${status}`)
    }
    const handleDeleteProfile = () => {
        setIsDeleting(true);
        const formData = {
            endListingsCheckbox, reasonSelected
        }
        performDeleteApiCall(formData)
            .then((res) => {
                    handleModalStateChange();
                    refetchProfiles()
                }
            )
            .then(() => {
                setIsDeleting(false)
            })
    }

    const performDeleteApiCall = async (data) => {
        const response = await fetch(`/api/ebay/profile/${profile.id}`, {
            method: "DELETE",
            body: JSON.stringify(data),
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        return response.json()
    }

    const activator = (
        <Button 
            icon={MenuHorizontalIcon} 
            variant="plain" 
            onClick={toggleActive}
        />
    );


    return (
        <IndexTable.Row id={profile.id} key={profile.id} position={1}>
            <IndexTable.Cell className="mw-200 white-space-normal">
                <Link onClick={() => handleViewProducts()} removeUnderline>
                    <Text  as={'span'} variant={'bodyMd'} fontWeight={'bold'}>{profile.profile_name}</Text>
                </Link>
            </IndexTable.Cell>

            <IndexTable.Cell>
                {profile.category_name}
            </IndexTable.Cell>

            <IndexTable.Cell>
                {profile.products_count}
            </IndexTable.Cell>

            <IndexTable.Cell>
           
            
                <Trans 
                    i18nKey="profiles.productStatusCount"
                    values={{uploaded: profile.uploaded_products, failed: profile.failed_products}}
                    components={{
                        uploadedLink: <Link monochrome onClick={() => handleStatusClick('uploaded')} removeUnderline />,
                        failedLink:  <Link monochrome onClick={() => handleStatusClick('failed')} removeUnderline />,
                        failedText: <Text as={'span'} variant={'bodyMd'} tone="critical"></Text>
                    }}
                />
            </IndexTable.Cell>

            <IndexTable.Cell>
                <div>
                    <Popover
                        active={active}
                        activator={activator}
                        autofocusTarget="first-node"
                        onClose={toggleActive}
                    >
                        <ActionList
                            actionRole="menuitem"
                            items={[
                                {
                                    content: t('profiles.viewProfile'),
                                    onAction: handleViewProducts,
                                },{
                                    content: t('profiles.editProfile'),
                                    onAction: handleEditProfile,
                                },
                                {
                                    content: t('profiles.deleteProfile'),
                                    onAction: handleModalStateChange,
                                },
                            ]}
                        />
                    </Popover>
                </div>
            </IndexTable.Cell>
            <DeleteProfileModal handleDeleteProfile={handleDeleteProfile}
                                handleModalStateChange={handleModalStateChange}
                                isDeleting={isDeleting}
                                showModal={showModal}
                                setReason={setReason}
                                endListingsCheckbox={endListingsCheckbox}
                                reasonSelected={reasonSelected}
                                setEndListingsCheckbox={setEndListingsCheckbox}/>
        </IndexTable.Row>
    );
}
