import { <PERSON><PERSON>ta<PERSON>, Card, Page } from "@shopify/polaris";
import { useAppQuery } from "../../../hooks/index.js";
import EbayProfileIndexTable from "./EbayProfileIndexTable.jsx";
import EbayProfileBanner from "../../Banner/EbayProfileBanner.jsx";
import { t } from "i18next";
import { useNavigate } from "react-router-dom";
import { TabsSkeletonComponent } from "../../Skeleton/TabsSkeletonComponent.jsx";
import React, { useCallback, useContext, useEffect, useState } from "react";
import EbayConnectionContext from "../../../context/EbayConnection/EbayConnectionContext.jsx";
import RefreshTokenExpiredWarningBanner from "../../Banner/RefreshTokenExpiredWarningBanner.jsx";
import LearnMoreLinks from "../../../util/LearnMoreLinks.jsx";
import SmartIndexFilters from "../../Common/SmartIndexFilters.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function EbayProfileListPanel() {
    const navigate = useNavigate();
    const ebayConnectionContext = useContext(EbayConnectionContext)
    const backendURL = window.location.origin
    const apiPath = '/api/ebay/profiles'
    const fullUrl = new URL(backendURL.concat(apiPath))
    const [url, setUrl] = useState(fullUrl.href)
    const [activeTab, setActiveTab] = useState('all')
    const [profiles, setProfiles] = useState([])
    const [isFirstRender, setIsFirstRender] = useState(true)

    const handleSearch = useCallback((term) => {
        const searchUrl = new URL(backendURL.concat(apiPath));
        if (term) {
            searchUrl.searchParams.set('search', term);
        }
        setUrl(searchUrl.href);
    }, [backendURL, apiPath]);

    const { isFetching, refetch: refetchProfiles } = useAppQuery({
        url: url,
        reactQueryOptions: {
            onSuccess: (data) => {
                setProfiles(data.result)
            }
        }
    });

    useEffect(() => {
        if (isFirstRender && !isFetching) {
            setIsFirstRender(false)
        }
        if (isFetching) {
            shopify.loading(true);
        }
        if (!isFetching) {
            shopify.loading(false);
        }
    }, [isFetching, isFirstRender])

    const views = [
        {
            tabName: t('common.all'),
            key: 'all',
        },
    ]
    const renderProfileTable = () => {
        return <>
            <SmartIndexFilters
                activeTab={activeTab}
                setSearchQuery={handleSearch}
                loading={isFetching}
                setActiveTab={setActiveTab}
                views={views}
                searchPlaceHolder={t('profiles.searchPlaceholder')}
            />
            <EbayProfileIndexTable profilesData={profiles} refetchProfiles={refetchProfiles} setUrl={setUrl} />
        </>
    }

    return (
        <>
            <TitleBar title={t("common.ebayProfiles")}>
                <button variant="primary" onClick={() => navigate('/shopify/products')}>
                    {t("common.uploadToEbay")}
                </button>
            </TitleBar>
            <Page>
                <BlockStack gap="400">
                   <EbayProfileBanner />
                    {!!ebayConnectionContext.ebayConnection.refresh_token_expired && <RefreshTokenExpiredWarningBanner />}
                    <Card padding={"0"}>
                        {isFirstRender ? <TabsSkeletonComponent /> : renderProfileTable()}
                    </Card>
                    <LearnMoreLinks />
                </BlockStack>
            </Page>
        </>
    )
}
