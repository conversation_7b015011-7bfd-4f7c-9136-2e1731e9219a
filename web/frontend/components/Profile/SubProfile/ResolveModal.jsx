import {Modal} from "@shopify/polaris";
import PriceForm from "./ErrorResolution/PriceForm.jsx";
import TitleTooLong from "./ErrorResolution/TitleTooLong.jsx";
import {useAuthenticatedFetch} from "../../../hooks/index.js";
import DescriptionMissing from "./ErrorResolution/DescriptionMissing.jsx";
import QuantityInvalid from "./ErrorResolution/QuantityInvalid.jsx";
import {useNavigate} from "react-router-dom";
import WeightInvalid from "./ErrorResolution/WeightInvalid.jsx";
import DisallowedCharacter from "./ErrorResolution/DisallowedCharacter.jsx";
import DuplicateSkuInVariants from "./ErrorResolution/DuplicateSkuInVariants.jsx";
import EndAndUploadAgain from "./ErrorResolution/EndAndUploadAgain.jsx";
import AuctionEnded from "./ErrorResolution/AuctionEnded.jsx";
import DuplicateListingViolationPolicy from "./ErrorResolution/DuplicateListingViolationPolicy.jsx";
import InventoryOverride from "./ErrorResolution/InventoryOverride.jsx";
import { t } from "i18next";

export default function ResolveModal({
                                         active,
                                         handleChange,
                                         error,
                                         products,
                                         handlePostAPICallAction,
                                         profileId,
                                         profile,
                                     }) {
    const freight_shipping = profile.shipping_policy.freight_shipping
    const fetch = useAuthenticatedFetch();
    const navigate = useNavigate()
    const product = products.find((product) => {
        return product.id === error.shopify_product_id
    })

    const performAPICall = async (formData) => {
        const response = await fetch(`/api/ebay/resolve-product-upload/${error.id}`, {
            method: "POST",
            body: JSON.stringify(formData),
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        return response.json()
    }

    const submitForm = async (formData) => {
        performAPICall(formData).then((res) => {
            if (res.success) {
                if (res.result.redirect) {
                    navigate('/progress', {
                        state: {
                            progressType: 'uploadMessageShopify',
                            redirectUrl: '/profiles/products/' + profileId,
                            isShopify: true
                        }
                    })
                } else {
                    handlePostAPICallAction()
                }
            }else{
                if (res.result.refresh_token_expired){
                    shopify.toast.show(res.message,{
                        duration: 3000,
                        isError: true
                    })
                    handleChange()
                }
            }
        })
    }

    const renderSolutionMarkup = () => {
        switch (error.error_code) {
            case 70:
                return <TitleTooLong submitForm={submitForm} product={product} profile={profile}></TitleTooLong>
            case 73:
                return <PriceForm submitForm={submitForm}></PriceForm>
            case 106:
                return <DescriptionMissing submitForm={submitForm} product={product}></DescriptionMissing>
            case 515:
            case 942:
                return <QuantityInvalid submitForm={submitForm}></QuantityInvalid>
            case 717:
            case 219021:
            case 21916495:
                return <WeightInvalid
                    submitForm={submitForm}
                    freight_shipping={freight_shipping}
                    errorData={error}
                ></WeightInvalid>
            case 21920309:
                return <DisallowedCharacter submitForm={submitForm} product={product}></DisallowedCharacter>
            case 21916585:
                return <DuplicateSkuInVariants submitForm={submitForm} product={product} handleChange={handleChange} />
            case 1:
                return <EndAndUploadAgain submitForm={submitForm} product={product} />
            case 291:
                return <AuctionEnded submitForm={submitForm} product={product} />
            case 21919067:
                return <DuplicateListingViolationPolicy submitForm={submitForm} product={product} profile={profile} />
            case 21919188:
                return <InventoryOverride submitForm={submitForm} product={product} profile={profile} />
        }
    }

    return (
        <Modal
            open={active}
            onClose={handleChange}
            title={t('common.resolving') +' : ' + error.message}
        >
            <Modal.Section>
                {renderSolutionMarkup()}
            </Modal.Section>
        </Modal>
    )
}
