import {But<PERSON>, Di<PERSON>r, Form, FormLayout, TextField, Select, Grid, BlockStack} from "@shopify/polaris";
import {useCallback, useState, useEffect, useMemo} from "react";
import {t} from "i18next";
import BaseBannerComponent from "../../../Banner/BaseBannerComponent";
import { useAppQuery } from "../../../../hooks";

export default function QuantityInvalid({submitForm}) {
    const [isDisabled, setIsDisabled] = useState(true)
    const [isLoading, setIsLoading] = useState(false)
    const [quantity, setQuantity] = useState(1);
    const [selectedLocation, setSelectedLocation] = useState('');

    const {
        data: locationsData,
        isLoading: locationsLoading
    } = useAppQuery({
        url: "/api/shopify/locations?connected_only=true",
        reactQueryOptions: {
            onSuccess: (data) => {
                if (data.success && data.locations && data.locations.length > 0) {
                    // Convert to string to ensure consistency
                    setSelectedLocation(String(data.locations[0].shopify_location_id));
                }
            }
        }
    });

    const locations = locationsData?.locations || [];

    // Validate form and update disabled state
    useEffect(() => {
        const isQuantityValid = quantity !== '' && parseInt(quantity) >= 1;
        const isLocationValid = selectedLocation !== '';
        const isFormValid = isQuantityValid && isLocationValid && !locationsLoading;
        setIsDisabled(!isFormValid);
    }, [quantity, selectedLocation, locationsLoading]);

    const locationOptions = useMemo(() => {
        if (locationsLoading) {
            return [{label: t('common.loading'), value: '', disabled: true}];
        }
        
        if (!locations.length) {
            return [{label: t('common.noLocationsAvailable'), value: '', disabled: true}];
        }
        
        // Don't include placeholder option, just return the actual locations
        return locations.map(location => ({
            label: location.name,
            value: String(location.shopify_location_id) // Convert to string for consistency
        }));
    }, [locations, locationsLoading]);

    const handleQuantityChange = useCallback((value) => {
        // Filter and clean the input value
        if (value === '') {
            setQuantity('');
        } else {
            // Remove any non-digit characters
            const cleanedValue = value.replace(/[^0-9]/g, '');
            // Convert to number and ensure it's at least 1
            if (cleanedValue && parseInt(cleanedValue) >= 1) {
                setQuantity(cleanedValue);
            } else if (cleanedValue === '') {
                setQuantity('');
            }
        }
    }, []);
    const handleLocationChange = useCallback((value) => {
        setSelectedLocation(value);
    }, []);

    const handleSubmit = () => {
        const formData = {
            quantity: quantity,
            locationId: selectedLocation
        };
        setIsLoading(true);
        submitForm(formData);
    }

    return (
        <Form onSubmit={handleSubmit}>
            <BlockStack gap="400">
                <BaseBannerComponent
                    bannerKey="quantity-invalid-banner"
                >
                    <p>{t('errors.quantityInvalidDescription')} {t('errors.quantityLocationUpdateNote')}</p>
                </BaseBannerComponent>

                <Grid>
                    <Grid.Cell columnSpan={{xs: 6, sm: 6, md: 6, lg: 6}}>
                        <TextField
                            value={quantity}
                            onChange={handleQuantityChange}
                            label={t('common.quantity')}
                            type="integer"
                            autoComplete="off"
                        />
                    </Grid.Cell>
                    <Grid.Cell columnSpan={{xs: 6, sm: 6, md: 6, lg: 6}}>
                        <Select
                            label={t('common.location')}
                            options={locationOptions}
                            onChange={handleLocationChange}
                            value={selectedLocation}
                            disabled={locationsLoading}
                        />
                    </Grid.Cell>
                </Grid>

                <Divider/>
                
                <div style={{
                    'display': 'flex',
                    'flexDirection': 'row-reverse',
                    'justifyContent': 'space-between'
                }}>
                    <Button disabled={isDisabled} submit loading={isLoading} variant="primary">
                        {t('common.submit')}
                    </Button>
                </div>
            </BlockStack>
        </Form>
    );
}
