import {useNavigate, useParams} from "react-router-dom";
import {
    Button,
    ButtonGroup,
    LegacyCard,
    Form,
    FormLayout,
    Text,
    TextField,
} from "@shopify/polaris";
import React, {useCallback, useContext, useEffect, useState} from "react";
import AttributeSelection from "./ProfileComponents/AttributeMapping/AttributeSelection.jsx";
import BusinessPolicies from "./ProfileComponents/BusinessPolicies/BusinessPolicies.jsx";
import CategoryFilter from "./ProfileComponents/CategoryFilter/CategoryFilter.jsx";
import PriceSetting from "./ProfileComponents/AdditionalSettings/PriceSetting/PriceSetting.jsx";
import {useAppQuery, useAuthenticatedFetch} from "../../hooks/index.js";
import TitleSetting from "./ProfileComponents/AdditionalSettings/TtileSetting/TitleSetting.jsx";
import SyncSetting from "./ProfileComponents/AdditionalSettings/SyncSetting/SyncSetting.jsx";
import {t} from "i18next";
import {helpScoutArticles} from "../../util/HelpscoutArticles.js";
import CreateProfileUploadDisabledBanner from "./ProfileComponents/CreateProfileUploadDisabledBanner.jsx";
import InventorySetting from "./ProfileComponents/AdditionalSettings/InventorySetting/InventorySetting.jsx";
import AutomaticProductUploadSetting
    from "./ProfileComponents/AdditionalSettings/AutomaticProductUploadSetting/AutomaticProductUploadSetting.jsx";
import ItemLocationCard from "./ProfileComponents/ItemLocation/ItemLocationCard.jsx";
import EbayConnectionContext from "../../context/EbayConnection/EbayConnectionContext.jsx";
import {joinWords} from "../../util/StringUtility.js";
import VatSetting from "./ProfileComponents/VatSetting/VatSetting.jsx";
import InfoCardBanner from "../Common/InfoCardBanner.jsx";
import EditProfileSkeleton from "../Skeleton/Profile/EditProfileSkeleton.jsx";
import LearnMoreLinks from "../../util/LearnMoreLinks.jsx";


export default function EbayProfileEdit() {
    const fetch = useAuthenticatedFetch();
    const [requiredFields, setRequiredFields] = useState([
        'shipping_policy',
        'return_policy',
        'payment_policy',
        'category_id',
        'condition_id',
        'profile_name',
        'item_location_country_code',
        'item_location_city'
    ])
    const params = useParams()
    const navigate = useNavigate();
    const [isLoading, setIsLoading] = useState(false)
    const [formErrors, setFormErrors] = useState({})

    const [profileHasCollection, setProfileHasCollection] = useState(false)

    const [formValues, setFormValues] = useState({
        shipping_policy: '',
        return_policy: '',
        payment_policy: '',
        profile_id: '',
        profile_name: '',
        category_id: '',
        category_name: '',
        attributes_mapping: {},
        condition_id: '',
        modify_price_by: '',
        price_option: '',
        modify_price_amount_by: '',
        title_prefix: '',
        title_suffix: '',
        truncate_title: '',
        price_sync: '',
        title_sync: '',
        description_sync: '',
        variation_sync: '',
        override_inventory_by: '',
        sub_conditions: {},
        item_location_country_code: '',
        item_location_post_code:'',
        item_location_city:'',
        primary_store_category_id:'',
        secondary_store_category_id:'',
        vat_percentage:'',
        image_sync: ''
    });

    let itemLocationCountryCode = '';
    let itemLocationPostCode = '';
    let itemLocationCity = '';

    const ebayConnectionContext = useContext(EbayConnectionContext)


    const {data, isFetching} = useAppQuery({
        url: `/api/ebay/profile/` + params.id,
        reactQueryOptions: {
            onSuccess: (data) => {
                setProfileHasCollection(data.result.collections.length !== 0)

                if (!data.result.item_location_country_code){
                    if (ebayConnectionContext.ebayConnection.registration_address){
                        const ebayUserRegistrationAddress = JSON.parse(
                            ebayConnectionContext.ebayConnection.registration_address
                        );
                        itemLocationCountryCode = ebayUserRegistrationAddress.Country ?? ''
                        itemLocationPostCode = ebayUserRegistrationAddress.PostalCode ?? ''
                        itemLocationCity = joinWords([
                            ebayUserRegistrationAddress.CityName ?? '',
                            ebayUserRegistrationAddress.StateOrProvince ?? '',
                        ],', ')
                    }
                }else{
                    itemLocationCountryCode = data.result.item_location_country_code ?? ''
                    itemLocationPostCode = data.result.item_location_post_code ?? ''
                    itemLocationCity = data.result.item_location_city ?? ''
                }

                setFormValues((prevValues) => ({
                    ...prevValues,
                    shipping_policy: data.result.shipping_policy_id.toString(),
                    return_policy: data.result.return_policy_id.toString(),
                    payment_policy: data.result.payment_policy_id.toString(),
                    category_id: data.result.category_id,
                    category_name: data.result.category_name,
                    attributes_mapping: data.result.attributes_mapping,
                    condition_id: data.result.condition_id ? data.result.condition_id.toString() : '',
                    modify_price_by: data.result.modify_price_by,
                    modify_price_amount_by: data.result.modify_price_amount_by,
                    title_prefix: data.result.title_prefix,
                    title_suffix: data.result.title_suffix,
                    price_sync: data.result.price_sync,
                    title_sync: data.result.title_sync,
                    truncate_title: data.result.truncate_title,
                    description_sync: data.result.description_sync,
                    variation_sync: data.result.variation_sync,
                    profile_name: data.result.profile_name,
                    override_inventory_by: data.result.override_inventory_by,
                    price_option: data.result.modify_price_amount_by >= 0 ? 'increase' : 'decrease',
                    automatic_product_upload: data.result.automatic_product_upload,
                    sub_conditions: data.result.sub_conditions ? JSON.parse(data.result.sub_conditions) : {},
                    item_location_country_code: itemLocationCountryCode,
                    item_location_post_code: itemLocationPostCode,
                    item_location_city: itemLocationCity,
                    primary_store_category_id: data.result.primary_store_category_id
                        ? data.result.primary_store_category_id.toString() : ''  ,
                    secondary_store_category_id: data.result.secondary_store_category_id
                    ? data.result.secondary_store_category_id.toString() : '',
                    vat_percentage: data.result.vat_percentage.toString(),
                    image_sync: data.result.image_sync,
                }))
            },
        },
    });

    const errorMessages = {
        shipping_policy: t('profiles.uploadButtonDisabledMessages.shippingPolicy'),
        return_policy: t('profiles.uploadButtonDisabledMessages.returnPolicy'),
        payment_policy: t('profiles.uploadButtonDisabledMessages.paymentPolicy'),
        category_id: t('profiles.uploadButtonDisabledMessages.category'),
        attributes_mapping: t('profiles.uploadButtonDisabledMessages.attributes'),
        condition_id: t('profiles.uploadButtonDisabledMessages.condition'),
        profile_name: t('profiles.uploadButtonDisabledMessages.profile_name'),
        item_location_country_code: t('profiles.itemLocation.country'),
        item_location_city: t('profiles.itemLocation.city'),
    }
    const updateErrorAndCheckEmpty = (fieldName) => {
        if (fieldName === 'attributes_mapping') {
            return errorMessages[fieldName]
        }
        return formValues[fieldName] === '' ? errorMessages[fieldName] : ''
    }
    const updateRequiredFields = (item, removeItem = false) => {
        if (removeItem) {
            setRequiredFields(requiredFields.filter(field => field !== item));
        } else {
            if (!requiredFields.includes(item)) {
                setRequiredFields([...requiredFields, item]);
            }
        }
    }
    useEffect(() => {
        const errors = {};
        requiredFields.forEach(fieldName => {
            errors[fieldName] = updateErrorAndCheckEmpty(fieldName)
        })
        setFormErrors(errors)
    }, [requiredFields, formValues]);

    const handleSubmit = () => {
        setIsLoading(true)
        performCreateApiCall(formValues)
            .then((res) => {
                if (res.success && res.result.process_completed) {
                    if (res.result.refresh_token_expired){
                        ebayConnectionContext.setEbayConnection(
                            { ...ebayConnectionContext.ebayConnection, refresh_token_expired: 1 }
                        )
                    }
                    navigate('/profiles/products/' + res.result.profile_id)
                }else if (res.success) {
                        if (res.result.should_redirect) {
                            navigate('/progress', {
                                state: {
                                    progressType: 'uploadMessageShopify',
                                    redirectUrl: '/profiles/products/' + params.id,
                                    isShopify: true
                                }
                            })
                        } else {
                            navigate('/profiles')
                        }
                    } else {
                        setIsLoading(false)
                        shopify.toast.show(t('profiles.uploadFailedMessage'),{
                            duration: 3000,
                            isError: true
                        })
                    }
                }
            )
    };

    const performCreateApiCall = async (data) => {

        const response = await fetch('/api/ebay/profile/update/' + params.id, {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        return response.json()
    }

    const handleProfileNameChange = useCallback((newValue) => handleInputChange('profile_name', newValue), [])

    const handleInputChange = (name, value) => {
        setFormValues((prevValues) => ({
            ...prevValues,
            [name]: value,
        }));
    };


    if (isFetching) {
        return (
            <EditProfileSkeleton/>
        )
    }

    return <>
        <div style={{"marginBottom": "15px"}}>

            <InfoCardBanner message={t('profiles.editProfileBanner')}
                            learnMoreLink={helpScoutArticles.profile.updateProfileURL}/>
        </div>
        <div style={{'marginTop': '15px'}}>
            <LegacyCard>
                <LegacyCard.Section>
                    <Form onSubmit={handleSubmit}>
                        <FormLayout>
                            <div style={{'display': 'flex', 'justifyContent': 'space-between'}}>
                                <Text variant="bodyMd" as="span"
                                      fontWeight="semibold">{data.result.profile_name}</Text>
                            </div>
                            <BusinessPolicies
                                selectedShippingPolicy={formValues.shipping_policy}
                                isShippingCalculated={formValues.is_shipping_policy_calculated}
                                selectedReturnPolicy={formValues.return_policy}
                                selectedPaymentPolicy={formValues.payment_policy}
                                handleInputChange={handleInputChange}
                            />
                            <CategoryFilter
                                attributesMapping={formValues.attributes_mapping}
                                conditionId={formValues.condition_id}
                                subConditions={formValues.sub_conditions}
                                selectedProfileID={''}
                                isProfileEdit={true}
                                categoryId={formValues.category_id}
                                categoryName={formValues.category_name}
                                primaryStoreCategoryId={formValues.primary_store_category_id}
                                secondaryStoreCategoryId={formValues.secondary_store_category_id}
                                handleInputChange={handleInputChange}
                                updateRequiredFields={updateRequiredFields}
                            />

                            {formValues.category_id &&
                                <AttributeSelection
                                    categoryId={formValues.category_id}
                                    attributesMapping={formValues.attributes_mapping}
                                    handleInputChange={handleInputChange}
                                    updateRequiredFields={updateRequiredFields}
                                />}
                            <InventorySetting handleInputChange={handleInputChange}
                                              overrideInventoryBy={formValues.override_inventory_by}/>
                            {
                                profileHasCollection ?
                                    <AutomaticProductUploadSetting
                                        automaticUpload={formValues.automatic_product_upload}
                                        handleInputChange={handleInputChange}
                                    /> : ''
                            }
                            <VatSetting vat_percentage={formValues.vat_percentage} handleInputChange={handleInputChange}/>
                            <PriceSetting
                                modifyPriceBy={formValues.modify_price_by}
                                modifyPriceAmountBy={formValues.modify_price_amount_by}
                                handleInputChange={handleInputChange}
                                priceOptionSet={formValues.price_option}
                            />
                            <TitleSetting
                                titlePrefix={formValues.title_prefix}
                                titleSuffix={formValues.title_suffix}
                                handleInputChange={handleInputChange}
                                truncateTitle={formValues.truncate_title}
                            />
                            <SyncSetting
                                priceSync={formValues.price_sync}
                                titleSync={formValues.title_sync}
                                descriptionSync={formValues.description_sync}
                                variationSync={formValues.variation_sync}
                                imageSync={formValues.image_sync}
                                handleInputChange={handleInputChange}
                            />

                            <ItemLocationCard
                                itemLocationCountryCode={formValues.item_location_country_code}
                                itemLocationPostCode={formValues.item_location_post_code}
                                itemLocationCity={formValues.item_location_city}
                                handleInputChange={handleInputChange}
                            />

                            <LegacyCard>
                                <LegacyCard.Section>
                                    <Text variant="bodyMd" as="span" fontWeight="semibold"
                                          style={{'display': 'inline'}}>{t('common.profileName')}</Text>
                                    <div style={{'marginTop': '1rem'}}>
                                        <TextField
                                            label={t('profiles.profileNameTextFieldLabel')}
                                            value={formValues.profile_name}
                                            onChange={handleProfileNameChange}
                                            autoComplete="off"
                                        />
                                    </div>
                                </LegacyCard.Section>
                            </LegacyCard>

                        </FormLayout>
                        <div style={{'marginTop': '10px'}}>
                            <CreateProfileUploadDisabledBanner formErrors={formErrors}/>
                        </div>
                        <ButtonGroup>
                            <Button
                                submit
                                loading={isLoading}
                                disabled={!(Object.values(formErrors).every(error => error === ''))}
                                variant="primary">{t('profiles.updateProfile')}</Button>
                        </ButtonGroup>
                    </Form>
                </LegacyCard.Section>
            </LegacyCard>
        </div>
    </>;
}
