import {
    <PERSON><PERSON>tack,
    Button,
    ButtonGroup,
    Collapsible,
    Form,
    FormLayout,
    LegacyCard,
    LegacyStack,
    Text,
    TextField
} from '@shopify/polaris';

import React, { useEffect, useState, useCallback, useContext } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useAuthenticatedFetch } from "../../hooks/index.js";
import AttributeSelection from "./ProfileComponents/AttributeMapping/AttributeSelection.jsx";
import BusinessPolicies from "./ProfileComponents/BusinessPolicies/BusinessPolicies.jsx";
import CategoryFilter from "./ProfileComponents/CategoryFilter/CategoryFilter.jsx";
import PriceSetting from "./ProfileComponents/AdditionalSettings/PriceSetting/PriceSetting.jsx";
import TitleSetting from "./ProfileComponents/AdditionalSettings/TtileSetting/TitleSetting.jsx";
import SyncSetting from "./ProfileComponents/AdditionalSettings/SyncSetting/SyncSetting.jsx";
import SelectProfile from "./ProfileComponents/SelectProfile.jsx";
import { helpScoutArticles } from "../../util/HelpscoutArticles.js";
import CreateProfileUploadDisabledBanner from "./ProfileComponents/CreateProfileUploadDisabledBanner.jsx";
import InventorySetting from "./ProfileComponents/AdditionalSettings/InventorySetting/InventorySetting.jsx";
import AutomaticProductUploadSetting
    from "./ProfileComponents/AdditionalSettings/AutomaticProductUploadSetting/AutomaticProductUploadSetting.jsx";

import ItemLocationCard from "./ProfileComponents/ItemLocation/ItemLocationCard.jsx"
import EbayConnectionContext from "../../context/EbayConnection/EbayConnectionContext.jsx";
import { joinWords } from '../../util/StringUtility.js'
import { Trans, useTranslation } from "react-i18next";
import VatSetting from './ProfileComponents/VatSetting/VatSetting.jsx';
import LearnMore from "../Util/LearnMore.jsx";
import InfoCardBanner from '../Common/InfoCardBanner.jsx';
export default function EbaySelectProfile({ profileType }) {
    const { t } = useTranslation()

    const [requiredFields, setRequiredFields] = useState([
        'shipping_policy',
        'return_policy',
        'payment_policy',
        'category_id',
        'condition_id',
        'profile_name',
        'item_location_country_code',
        'item_location_city'
    ])
    const fetch = useAuthenticatedFetch();
    const navigate = useNavigate()
    const location = useLocation();
    let items = [];
    if (!location.state || !location.state.length) {
        if (profileType === 'product') {
            navigate(`/shopify/products`)
        } else {
            navigate(`/shopify/products/`)
        }
    } else {
        items = location.state.map((item) => {
            return item['id']
        });
    }

    const [isUploading, setIsLoading] = useState(false)
    const [enableSaveButton, setEnableSaveButton] = useState(false);
    const [open, setOpen] = useState(false);
    const handleToggle = useCallback(() => setOpen((open) => !open), []);
    const [formErrors, setFormErrors] = useState({})

    const ebayConnectionContext = useContext(EbayConnectionContext)
    let itemLocationCountryCode = '';
    let itemLocationPostCode = '';
    let itemLocationCity = '';

    if (ebayConnectionContext.ebayConnection.registration_address) {
        const ebayUserRegistrationAddress = JSON.parse(ebayConnectionContext.ebayConnection.registration_address);
        itemLocationCountryCode = ebayUserRegistrationAddress.Country ?? ''
        itemLocationPostCode = ebayUserRegistrationAddress.PostalCode ?? ''
        itemLocationCity = joinWords([
            ebayUserRegistrationAddress.CityName ?? '',
            ebayUserRegistrationAddress.StateOrProvince ?? '',
        ], ', ')
    }

    const [formValues, setFormValues] = useState({
        shipping_policy: '',
        is_shipping_policy_calculated: false,
        return_policy: '',
        payment_policy: '',
        profile_id: '',
        profile_name: '',
        category_id: '',
        category_name: '',
        attributes_mapping: {},
        condition_id: '',
        modify_price_by: '1',
        modify_price_amount_by: 0.00,
        automatic_product_upload: 1,
        title_prefix: '',
        title_suffix: '',
        truncate_title: 0,
        price_sync: 0,
        title_sync: 0,
        description_sync: 0,
        items: items,
        profile_type: profileType,
        variation_sync: true,
        override_inventory_by: '',
        price_option: 'increase',
        is_profile_selected: false,
        sub_conditions: {},
        item_location_country_code: itemLocationCountryCode,
        item_location_post_code: itemLocationPostCode,
        item_location_city: itemLocationCity,
        primary_store_category_id: '',
        secondary_store_category_id: '',
        vat_percentage: '',
        image_sync: 1,
    });

    const handleSubmit = () => {
        if (!enableSaveButton) {
            return;
        }
        setIsLoading(true)
        performCreateApiCall(formValues)
            .then((res) => {
                if (res.success && res.result.process_completed) {
                    if (res.result.refresh_token_expired){
                        ebayConnectionContext.setEbayConnection(
                            { ...ebayConnectionContext.ebayConnection, refresh_token_expired: 1 }
                        )
                    }
                    navigate('/profiles/products/' + res.result.profile_id)
                } else if (res.success) {
                    navigate('/progress', {
                        state: {
                            progressType: 'uploadMessageShopify',
                            redirectUrl: '/profiles/products/' + res.result.profile_id,
                            isShopify: true
                        }
                    })
                } else {
                    shopify.toast.show(t('profiles.uploadFailedMessage'))
                    setIsLoading(false)
                }
            }
            )
    };

    const performCreateApiCall = async (data) => {
        const response = await fetch('/api/ebay/product-upload', {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        return response.json()
    }


    const handleInputChange = (name, value) => {
        if (formValues.profile_id) {
            shopify.toast.show(t('profiles.profileDeselectedMessage'))
        }
        setFormValues((prevValues) => ({
            ...prevValues,
            [name]: value,
            'profile_id': '',
            'is_profile_selected': false
        }));
        updateRequiredFields('profile_name')
    };

    const handleProfileNameChange = useCallback((newValue) => handleInputChange('profile_name', newValue), [])


    const errorMessages = {
        shipping_policy: t('profiles.uploadButtonDisabledMessages.shippingPolicy'),
        return_policy: t('profiles.uploadButtonDisabledMessages.returnPolicy'),
        payment_policy: t('profiles.uploadButtonDisabledMessages.paymentPolicy'),
        category_id: t('profiles.uploadButtonDisabledMessages.category'),
        attributes_mapping: t('profiles.uploadButtonDisabledMessages.attributes'),
        condition_id: t('profiles.uploadButtonDisabledMessages.condition'),
        profile_name: t('profiles.uploadButtonDisabledMessages.profile_name'),
        item_location_country_code: t('profiles.itemLocation.country'),
        item_location_city: t('profiles.itemLocation.city'),
    }

    // Helper function to check if a field is empty and update errors
    const updateErrorAndCheckEmpty = (fieldName) => {
        if (fieldName === 'attributes_mapping') {
            return errorMessages[fieldName]
        }
        return formValues[fieldName] === '' || JSON.stringify(formValues[fieldName]) === "{}" ? errorMessages[fieldName] : ''
    }
    const updateRequiredFields = (item, removeItem = false) => {
        if (removeItem) {
            setRequiredFields(requiredFields.filter(field => field !== item));
        } else {
            if (!requiredFields.includes(item)) {
                setRequiredFields([...requiredFields, item]);
            }
        }
    }

    useEffect(() => {
        const errors = {};
        requiredFields.forEach(fieldName => {
            errors[fieldName] = updateErrorAndCheckEmpty(fieldName)
        })
        setFormErrors(errors)
        const isValid = Object.values(errors).every(error => error === '')
        setEnableSaveButton(isValid)
    }, [requiredFields, formValues]);

    const renderProfileNameMarkup = () => {
        if (!formValues.profile_id) {
            return (
                <div style={{ 'marginTop': '1rem' }}>
                    <LegacyCard>
                        <LegacyCard.Section>
                            <LegacyStack spacing={"extraTight"}>
                                <Text variant="bodyMd" as="span" fontWeight="semibold">{t('common.profileName')}
                                </Text>
                                <span style={{'color': 'red'}}>*</span>
                                <LearnMore url={helpScoutArticles.profile.selectProfileURL}/>
                            </LegacyStack>
                            <div style={{'marginTop': '1rem'}}>
                                <TextField
                                    label=""
                                    value={formValues.profile_name}
                                    onChange={handleProfileNameChange}
                                    autoComplete="off"
                                />
                            </div>
                        </LegacyCard.Section>
                    </LegacyCard>
                </div>
            )
        }
        return <></>
    }

    return <>
        <BlockStack gap="400">
            <InfoCardBanner message={t('profiles.editProfileBanner')}
                learnMoreLink={helpScoutArticles.profile.updateProfileURL} />
            <SelectProfile formValues={formValues} setFormValues={setFormValues}
                updateRequiredFields={updateRequiredFields} />
            <LegacyCard>
                <LegacyCard.Section>
                    <Form onSubmit={handleSubmit}>
                        <FormLayout>
                            <div style={{ 'display': 'flex', 'justifyContent': 'space-between' }}>
                                <Text variant="bodyMd" as="span"
                                    fontWeight="semibold">
                                    <p>
                                        {t('profiles.bannerDescription')}
                                        <a style={{ 'marginLeft': '5px' }}
                                            href={helpScoutArticles.profile.selectProfileURL} target={"_blank"}>
                                            {t('common.learnMore')}
                                        </a>
                                    </p>
                                </Text>
                            </div>
                            <BusinessPolicies
                                selectedShippingPolicy={formValues.shipping_policy}
                                isShippingCalculated={formValues.is_shipping_policy_calculated}
                                selectedReturnPolicy={formValues.return_policy}
                                selectedPaymentPolicy={formValues.payment_policy}
                                handleInputChange={handleInputChange}
                            />
                            <CategoryFilter
                                conditionId={formValues.condition_id}
                                subConditions={formValues.sub_conditions}
                                selectedProfileID={formValues.profile_id.toString()}
                                categoryId={formValues.category_id}
                                categoryName={formValues.category_name}
                                primaryStoreCategoryId={formValues.primary_store_category_id}
                                secondaryStoreCategoryId={formValues.secondary_store_category_id}
                                handleInputChange={handleInputChange}
                                updateRequiredFields={updateRequiredFields}
                            />

                            {formValues.category_id &&
                                <AttributeSelection
                                    categoryId={formValues.category_id}
                                    attributesMapping={formValues.attributes_mapping}
                                    handleInputChange={handleInputChange}
                                    updateRequiredFields={updateRequiredFields}
                                />}
                            <InventorySetting overrideInventoryBy={formValues.override_inventory_by}
                                handleInputChange={handleInputChange} />
                            <LegacyCard sectioned>
                                <LegacyStack vertical>
                                    <Text variant="bodyMd" as="span"
                                        fontWeight="semibold">{t('profiles.additionalSettings')}</Text>
                                    <Text as={'p'}>
                                        <Trans
                                            i18nKey="profiles.additionalSettingsDescription"
                                        >
                                            <Button
                                                onClick={handleToggle}
                                                ariaExpanded={open}
                                                ariaControls="basic-collapsible"
                                                size={"slim"}
                                                variant="plain">
                                            </Button>
                                        </Trans>
                                    </Text>

                                    <Collapsible
                                        open={open}
                                        id="basic-collapsible"
                                        transition={{ duration: '500ms', timingFunction: 'ease-in-out' }}
                                        expandOnPrint
                                    >
                                        {
                                            profileType === 'collection' || formValues.collections_count ?
                                                <AutomaticProductUploadSetting
                                                    automaticUpload={formValues.automatic_product_upload}
                                                    handleInputChange={handleInputChange}
                                                /> :
                                                ''
                                        }
                                        <VatSetting vat_percentage={formValues.vat_percentage} handleInputChange={handleInputChange} />
                                        <PriceSetting
                                            modifyPriceBy={formValues.modify_price_by}
                                            modifyPriceAmountBy={formValues.modify_price_amount_by}
                                            handleInputChange={handleInputChange}
                                            priceOptionSet={formValues.price_option}
                                        />

                                        <TitleSetting
                                            titlePrefix={formValues.title_prefix}
                                            titleSuffix={formValues.title_suffix}
                                            handleInputChange={handleInputChange}
                                            truncateTitle={formValues.truncate_title}
                                        />
                                        <SyncSetting
                                            priceSync={formValues.price_sync}
                                            titleSync={formValues.title_sync}
                                            descriptionSync={formValues.description_sync}
                                            variationSync={formValues.variation_sync}
                                            imageSync={formValues.image_sync}
                                            handleInputChange={handleInputChange}
                                        />

                                    </Collapsible>
                                </LegacyStack>
                            </LegacyCard>
                            <ItemLocationCard
                                itemLocationCountryCode={formValues.item_location_country_code}
                                itemLocationPostCode={formValues.item_location_post_code}
                                itemLocationCity={formValues.item_location_city}
                                handleInputChange={handleInputChange}
                            />

                            {renderProfileNameMarkup()}


                        </FormLayout>
                        <div style={{ 'marginTop': '10px' }}>
                            <CreateProfileUploadDisabledBanner formErrors={formErrors} />
                        </div>
                        <ButtonGroup>
                            <Button
                                submit
                                disabled={!enableSaveButton}
                                loading={isUploading}
                                variant="primary">
                                {t('common.proceedToUpload')}
                            </Button>
                        </ButtonGroup>
                    </Form>
                </LegacyCard.Section>
            </LegacyCard>
            <div className='spacer-bottom-.5x' />
        </BlockStack>
    </>
}



