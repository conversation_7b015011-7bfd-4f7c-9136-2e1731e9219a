import { Card, RadioButton, BlockStack } from "@shopify/polaris";
import OrderSyncLimitReachedBanner from "../Banner/OrderSyncLimitReachedBanner";
import { TabsSkeletonComponent } from "../Skeleton/TabsSkeletonComponent";
import OrdersIndexTable from "./OrdersIndexTable";
import { useAppQuery } from "../../hooks";
import { useCallback, useEffect, useState } from "react";
import { t } from "i18next";
import SmartIndexFilters from "../Common/SmartIndexFilters";
import StatusFilter from "../Common/Filter/StatusFilter";


export default function OrderListPanel() {
    const [isFirstRender, setIsFirstRender] = useState(true)
    const [activeTab, setActiveTab] = useState('all')
    const [orders, setOrders] = useState([])
    const [search, setSearchQuery] = useState('')
    const [pagination, setPagination] = useState({})
    const [appliedFilters, setAppliedFilters] = useState([])
    const backendURL = window.location.origin
    const apiPath = '/api/ebay/orders';
    const fullUrl = new URL(backendURL.concat(apiPath))
    const [url, setUrl] = useState('')

    useEffect(() => {
        if (search.length > 0) {
            fullUrl.searchParams.set('search', search)
        }
        if (activeTab !== 'all') {
            fullUrl.searchParams.set('status', activeTab)
        }
        setUrl(fullUrl.href)
    }, [search, activeTab])

    const { isFetching, refetch: refetchOrders } = useAppQuery({
        url: `${url}`, reactQueryOptions: {
            onSuccess: (res) => {
                setOrders(res.result.data)
                setPagination({
                    current_page: res.result.current_page,
                    last_page: res.result.last_page,
                    from: res.result.from,
                    prevPageUrl: res.result.prev_page_url,
                    nextPageUrl: res.result.next_page_url,
                })
            }
        }
    });

    useEffect(() => {
        if (isFirstRender && !isFetching) {
            setIsFirstRender(false)
        }
        if (isFetching) {
            shopify.loading(true);
        }
        if (!isFetching) {
            shopify.loading(false);
        }
    }, [isFetching, isFirstRender])

    const handleFiltersClearAll = useCallback(() => {
        setActiveTab('all')
    }, []);

    // Define views array for reuse
    const views = [
        {
            tabName: t('orders.tableTabs.all'),
            key: 'all',
        },
        {
            tabName: t('common.synced'),
            key: 'synced'
        },
        {
            tabName: t('common.notSynced'),
            key: 'not_synced'
        }
    ];

    // Build applied filters
    useEffect(() => {
        const filters = []
        
        // Status filter - only show when a specific status is selected (not 'all')
        if (activeTab !== 'all') {
            const currentView = views.find(view => view.key === activeTab);
            if (currentView) {
                    filters.push({
                        key: 'statusFilter',
                        label: t('common.statusFilter', { status: currentView.tabName }),
                        onRemove: () => setActiveTab('all'),
                        pinned: true,
                    });
            }
        }
    
        const sortedFilters = filters.sort((a, b) => a.key.localeCompare(b.key));
        const sortedAppliedFilters = appliedFilters.sort((a, b) => a.key.localeCompare(b.key));
        // Only update appliedFilters if the new filters are different
        const areFiltersEqual = JSON.stringify(sortedFilters) === JSON.stringify(sortedAppliedFilters);
        if (!areFiltersEqual) {
            setAppliedFilters(filters);
        }
    }, [activeTab, views]);

    const filters = [
        {
            key: 'statusFilter',
            label: t('common.status'),
            filter: <StatusFilter statusOptions={views.filter(view => view.key !== 'all').map(view => ({
                label: view.tabName,
                value: view.key,
            }))}
            selectedStatus={[activeTab]}
            setStatus={setActiveTab} />,
            pinned: true,
        },
    ];

    return (
        <>
            <OrderSyncLimitReachedBanner />
            <Card padding="0">
                <SmartIndexFilters
                    activeTab={activeTab}
                    setActiveTab={setActiveTab}
                    setSearchQuery={setSearchQuery}
                    handleFiltersClearAll={handleFiltersClearAll}
                    loading={isFetching}
                    views={views}
                    appliedFilters={appliedFilters}
                    filters={filters}
                    searchPlaceHolder={t('orders.tableTabs.searchPlaceHolder')}
                />
                {
                    isFirstRender ?
                        <TabsSkeletonComponent /> :
                        <OrdersIndexTable
                            orders={orders}
                            refetchOrders={refetchOrders}
                            pagination={pagination}
                            setUrl={setUrl}
                        />
                }
            </Card>
        </>
    )
}