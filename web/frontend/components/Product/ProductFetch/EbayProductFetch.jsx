import {But<PERSON>, <PERSON>, Text} from "@shopify/polaris";
import {useState} from "react";
import {useAuthenticatedFetch} from "../../../hooks/index.js";
import { TitleBar } from "@shopify/app-bridge-react";

export default function EbayProductFetch({refetchProducts}) {
    const [isApiCalling, setIsApiCalling] = useState(false);
    const fetch = useAuthenticatedFetch();

    const performApiCall = async () => {
        const response = await fetch("/api/ebay/fetchEbayListings");

        if (response.ok) {
            setIsApiCalling(false)
            refetchProducts()
        }
    }

    const fetchEbayProducts = () => {
        setIsApiCalling(true)
        performApiCall()
    };

    return (
        <>
            <TitleBar title={"eBay Products"} />
            <Page>
                <Text variant="headingXl" as="h1">
                    No Products Found
                </Text>
                <p>
                    Looks like you haven't imported your eBay products yet. Please import products from eBay.
                </p>
                <div style={{width: "auto", marginTop: 20}}>
                    <Button loading={isApiCalling} onClick={fetchEbayProducts} variant="primary">Import eBay Products</Button>
                </div>
            </Page>
        </>
    );
}
