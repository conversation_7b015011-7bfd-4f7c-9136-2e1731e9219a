import {Button, IndexTable, Text} from "@shopify/polaris";
import {t} from "i18next";
import {useState, useCallback} from "react";
import ImageThumbnail from "../../Thumbnail/ImageThumbnail.jsx";
import ConfirmationModal from "../../Modal/Common/ConfirmationModal.jsx";

export default function BulkActionTable({products, handleDelete, isShopify = false}) {
    const [removeIntendedItemId, setRemoveIntendedItemId] = useState(0)
    const [showConfirmationModal, setShowConfirmationModal] = useState(false)
    
    const resourceName = {
        singular: 'product',
        plural: 'products',
    };

    const showImage = (image_src, image_url) => {
        if (isShopify) {
            return <ImageThumbnail source={image_src} size="small"/>
        }
        return <ImageThumbnail source={JSON.parse(image_url)[0] ?? null} size="small"/>
    }
    
    const handleRemoveConfirmation = (id) => {
        setRemoveIntendedItemId(id)
        setShowConfirmationModal(true)
    }
    
    const handleCancel = () => {
        setRemoveIntendedItemId(0)
        setShowConfirmationModal(false)
    }

    const memoizedHandleDelete = useCallback(() => {
        handleDelete(removeIntendedItemId)
        shopify.toast.show(t('common.productRemoved'), {duration:2000})
        setShowConfirmationModal(false)
        setRemoveIntendedItemId(0)
    }, [handleDelete, removeIntendedItemId]);
    
    const memoizedHandleCancel = useCallback(() => {
        handleCancel()
    }, []);

    const rowMarkup = products.map(
        (
            {
                id,
                title,
                image_src,
                image_url,
                price,
                quantity,
                product_type,
                shopify_product_id,
                shopify_product_url,
                shopify_product_handle
            },
            index,
        ) => (
            <IndexTable.Row
                id={id}
                key={id}
                position={index}
            >
                <IndexTable.Cell>
                    {showImage(image_src, image_url)}
                </IndexTable.Cell>
                <IndexTable.Cell>
                    <Text fontWeight="bold" as="span">
                        {title}
                    </Text>
                </IndexTable.Cell>
                <IndexTable.Cell>
                    {price}
                </IndexTable.Cell>
                <IndexTable.Cell>
                    {quantity}
                </IndexTable.Cell>
                <IndexTable.Cell>
                    <Button
                        size="slim" variant="plain" tone="critical"
                        onClick={() => handleRemoveConfirmation(id)}
                    >
                        {t('common.remove')}
                    </Button>
                </IndexTable.Cell>
            </IndexTable.Row>
        ),
    );

    return (
        <>
            <IndexTable
                resourceName={resourceName}
                itemCount={products.length}
                headings={[
                    {title: ''},
                    {title: t('common.product')},
                    {title: t('common.price')},
                    {title: t('common.quantity')},
                    {title: t('common.action')},
                ]}
                selectable={false}
            >
                {rowMarkup}
            </IndexTable>
            
            {showConfirmationModal && (
                <ConfirmationModal
                    open={showConfirmationModal}
                    message={t('common.removeConfirmationModalDescription')}
                    title={t('common.removeConfirmationModalTitle')}
                    onConfirm={memoizedHandleDelete}
                    onCancel={memoizedHandleCancel}
                />
            )}
        </>
    )
}

