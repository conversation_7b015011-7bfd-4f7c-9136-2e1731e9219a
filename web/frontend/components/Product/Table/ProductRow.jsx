import {Badge, Box, Button, Icon, Image, IndexTable, InlineStack, Link, Text, Tooltip} from "@shopify/polaris";
import ProductVariationTable from "./ProductVariationTable.jsx";
import {ebayIcon, shopifyIcon} from "../../../assets/index.js";
import {t} from "i18next";
import ImageThumbnail from "../../Thumbnail/ImageThumbnail.jsx";
import {AlertCircleIcon, ChevronDownIcon, ChevronUpIcon, DeleteIcon, UploadIcon} from "@shopify/polaris-icons";
import UnLinkProductModal from "../../Modal/Product/UnLinkProductModal.jsx";
import LinkProductModal from "../../Modal/Product/LinkProductModal.jsx";
import DecoratedText from "../../Common/DecoratedText.jsx";
import {useState} from "react";

export default function ProductRow({item, selectedResource, activeId, setActive, allResourcesSelected, refetchProducts}) {
    const [showLinkModal, setShowLinkModal] = useState(false);
    const [showUnlinkModal, setShowUnlinkModal] = useState(false);
    
    const isActive = (activeId === item.id)
    let imageUrl = null;
    if (item.image_url && item.image_url !== 'null') {
        try {
            imageUrl = JSON.parse(item.image_url);
        } catch (error) {
            console.error("Invalid JSON in image_url:", item.image_url);
        }
    }
    const image = imageUrl && Array.isArray(imageUrl) && imageUrl.length > 0
        ? imageUrl[0]
        : null;
    const showDownArrow = (item) => {
        if (item.isVariable) {
            return <Button
                variant="plain"
                icon={isActive ? ChevronUpIcon : ChevronDownIcon}
                accessibilityLabel="Show Variations"
                onClick={() => setActive(isActive ? null : item.id)}
            />
        }
        return <div style={{'paddingLeft': '20px'}}/>
    }

    const showVariations = () => {
        if (item.isVariable && isActive) {
            return <ProductVariationTable variations={item.variations}/>
        } else {
            return (<></>)
        }
    }
    const getQuantity = () => {
        let quantity = item.variations.reduce((accumulator, object) => {
            return accumulator + (object.quantity - object.quantity_sold);
        }, 0);

        return (
            (<Text variant="bodyMd" as="span" tone={quantity > 10 ? '' : 'critical'}>
                {t('common.countInStock', {quantity: quantity})}
                {item.isVariable ? ` ${t('common.forNVariants', {count: item.variations.length})}` : ''}
            </Text>)
        );
    }
    const getSKU = () => {
        const simpleProductSku = item.variations[0]?.sku ?? ''
        if (item.isVariable) {
            let sku = item.variations.map((item) => {
                if (item.sku) {
                    return item.sku
                }
            }).join(', ')
            return (
                <>
                    <span>{sku}</span>
                </>
            )
        }
        if (simpleProductSku) {
            return <span> {simpleProductSku} </span>
        }
        return t('common.notAvailable')
    }

    const handleLinkProduct = () => {
        setShowLinkModal(true);
    }

    const handleUnlinkProduct = () => {
        setShowUnlinkModal(true);
    }

    return <>
        <IndexTable.Row id={item.id} selected={selectedResource.includes(item.id) || allResourcesSelected} key={item.id} position={"index"}
                        disabled={item.listing_status === 'Ended'}
                        onClick={() => {
                        }}
        >
            <IndexTable.Cell className={'product-image-cell cursor-default'}>
                <div className="product-item">
                    {showDownArrow(item)}
                    <ImageThumbnail source={image}/>
                </div>
            </IndexTable.Cell>

            <IndexTable.Cell className={'mw-400 cursor-default'}>
                <div className="product-short-description">
                    <div style={{'whiteSpace': 'normal'}}>
                        <Text as={'p'} variant={'bodyLg'} fontWeight={'bold'}>{item.title}</Text>
                    </div>
                    <Box maxWidth="200px" overflowX="hidden">
                        <DecoratedText as={'p'} variant={'bodyMd'}
                            className='text-ellipsis'
                        >
                            {t('common.SKU')}: {getSKU()}
                        </DecoratedText>
                    </Box>
                </div>
            </IndexTable.Cell>
            <IndexTable.Cell className={'cursor-default'}>
                <Badge>{item.site}</Badge>

            </IndexTable.Cell>
            <IndexTable.Cell className={'cursor-default'}>
                {getQuantity()}
            </IndexTable.Cell>
            <IndexTable.Cell className={'cursor-default'}>
                <InlineStack wrap={false} align={"start"}>
                    {item.view_item_url &&
                        <Link
                            url={item.view_item_url}
                            target={"_blank"}
                            onClick={() => window.open(item.view_item_url, '_blank')}
                        >
                            <Image source={ebayIcon} alt="ebay-icon" width={70}/>
                        </Link>
                    }
                    {
                        item.shopify_product_id &&
                        <Link
                            url={item.shopify_product_url}
                            target={"_blank"}
                            onClick={() => window.open(item.shopify_product_url, '_blank')}
                        >
                            <Image source={shopifyIcon} alt="shopify-icon" width={35}/>
                        </Link>
                    }
                    {
                        !item.shopify_product_id && !!item.import_remarks &&
                        <div style={{padding: '8px 0'}}>
                            <Tooltip persistOnClick content={item.import_remarks}>
                                <Icon
                                    source={AlertCircleIcon}
                                    tone="critical"
                                />
                            </Tooltip>
                        </div>

                    }
                </InlineStack>


        </IndexTable.Cell>

            <IndexTable.Cell className={'cursor-default'}>
                {
                    item.listing_status === 'Active' &&
                    (
                        !!item.shopify_product_id ?
                            <Button
                                onClick={handleUnlinkProduct}
                                icon={DeleteIcon}
                            >
                                {t('ebayProducts.deleteFromShopify')}
                        </Button> :
                        <Button
                            onClick={handleLinkProduct}
                            icon={UploadIcon}>
                            {t('ebayProducts.createInShopify')}
                        </Button>
                    )
                }
            </IndexTable.Cell>
        </IndexTable.Row>
        {showVariations()}
        
        {showLinkModal && (
            <LinkProductModal 
                open={true}
                onClose={() => setShowLinkModal(false)}
                item={item}
                refetchProducts={refetchProducts}
            />
        )}
        
        {showUnlinkModal && (
            <UnLinkProductModal 
                open={true}
                onClose={() => setShowUnlinkModal(false)}
                item={item}
                refetchProducts={refetchProducts}
            />
        )}
    </>;
}
