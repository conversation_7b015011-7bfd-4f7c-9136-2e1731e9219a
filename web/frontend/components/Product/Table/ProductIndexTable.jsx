import {IndexTable, useIndexResourceState} from "@shopify/polaris";
import ProductRow from "./ProductRow.jsx"
import {useCallback, useContext, useEffect, useMemo, useRef, useState} from "react";
import {ModalContext} from "../../../context/Modal/ModalContext.jsx";
import {useNavigate} from "react-router-dom";
import EmptyProductState from "../Empty State/EmptyProductState.jsx";
import {t} from "i18next";
import CreateAllEbayProductsModal from "../../Modal/Product/CreateAllEbayProductsModal.jsx";
import createTablePagination from "../../Common/TablePagination";

export default function ProductIndexTable({
                                              products,
                                              refetchProducts,
                                              refetchProdDetails,
                                              selectedIds,
                                              setSelectedIds,
                                              intersectedIds,
                                              selectedItemLocation,
                                              pagination,
                                              setUrl
                                          }) {

    const {
        selectedResources,
        allResourcesSelected,
        handleSelectionChange,
    } = useIndexResourceState(products);
    const navigate = useNavigate()
    const createProdCount = useRef(0)
    const deleteProdCount = useRef(0)
    const [activeId, setActive] = useState()
    const [showCreateAllEbayProductsModal, setCreateAllEbayProductsModal] = useState(false)
    const [selectedProductsData, setSelectedProductsData] = useState([])

    const filteredProducts = useMemo(() => {
        return products.filter(product => intersectedIds.includes(product.id));
    }, [products, intersectedIds]);

    useEffect(() => {
        const selectedIdsSet = new Set(selectedIds);

        const updatedSelectedProducts = previousSelectedProducts => {
            const mergedProducts = [...previousSelectedProducts, ...filteredProducts];

            const uniqueProductsById = mergedProducts.reduce((unique, product) => {
                if (selectedIdsSet.has(product.id) && !unique[product.id]) {
                    unique[product.id] = product;
                }
                return unique;
            }, {});

            return Object.values(uniqueProductsById);
        };

        setSelectedProductsData(updatedSelectedProducts);
    }, [filteredProducts, selectedIds]);

    const resourceName = {
        singular: selectedItemLocation ?  t('common.filteredProduct') : t('common.product'),
        plural: selectedItemLocation ? t('common.filteredProducts') : t('common.products'),
    };

    const getProductIds = useMemo(() => {
        return products.map(product => product.id);
    }, [products]);

    const handleSelectionChange1 = (selectionType, toggleType, selection) => {
        let updatedIds = [...selectedIds];

        if ((selectionType === 'page' && toggleType) && !allResourcesSelected) {
            const oldUpdatedIds = updatedIds.slice(); // Make a copy of the current selectedIds
            // Merge oldUpdatedIds with newUpdatedIds while keeping only unique IDs
            updatedIds = [...new Set([...oldUpdatedIds, ...(getProductIds)])];
        } else if (selectionType === 'page' && !toggleType) {
            updatedIds = updatedIds.filter(id => !getProductIds.includes(id));
        }

        if (selection) {
            if (toggleType === false && updatedIds.includes(selection)) {
                updatedIds = updatedIds.filter(id => id !== selection);
            } else if (toggleType && !updatedIds.includes(selection)) {
                updatedIds.push(selection);
            }
        }
        updatedIds = [...new Set(updatedIds)];
        setSelectedIds(updatedIds);
        handleSelectionChange(selectionType, toggleType, selection)
    };

    const promotedBulkActions = useCallback(() => {
            const buttonObject = []
            createProdCount.current = 0
            deleteProdCount.current = 0

            selectedProductsData.forEach((item) => {
                if (item.shopify_product_id !== null) {
                    deleteProdCount.current += 1
                } else if (item.listing_status === 'Active') {
                    createProdCount.current += 1
                }
            })

            if (allResourcesSelected) {
                buttonObject.push({
                    id: 'create-all',
                    content: selectedItemLocation ? t('ebayProducts.bulkCreateAll.buttonLabelWithFilter') : t('ebayProducts.bulkCreateAll.buttonLabel'),
                    onAction: () => handleCreateAllEbayProductsModal()
                })
                return buttonObject
            }

            if (createProdCount.current > 0) {
                buttonObject.push({
                    id: 'create',
                    content: t('ebayProducts.bulkCreateButtonLabel', {count: createProdCount.current}),
                    onAction: () => handleBulkCreate(),
                })
            }
            if (deleteProdCount.current > 0) {
                buttonObject.push({
                    id: 'delete',
                    content: t('ebayProducts.bulkDeleteButtonLabel', {count: deleteProdCount.current}),
                    onAction: () => handleBulkDelete(),
                })
            }
            return buttonObject
        }, [selectedProductsData]
    )
    
    const getProductsIds = (productsArray, filterCondition) => {
        return productsArray
            .filter(filterCondition)
    };
    
    const handleBulkCreate = () => {
        const selectedProductsToCreate = getProductsIds(selectedProductsData, (product) => product.shopify_product_id === null && product.listing_status === 'Active');
        navigate('/ebay/products/bulk-create', {state: selectedProductsToCreate})
    }
    
    const handleBulkDelete = () => {
        const selectedProductsToDelete = getProductsIds(selectedProductsData, (product) => product.shopify_product_id !== null);
        navigate('/ebay/products/bulk-delete', {state: selectedProductsToDelete})
    }

    const handleCreateAllEbayProductsModal = useCallback(() => {
        setCreateAllEbayProductsModal(!showCreateAllEbayProductsModal)
    }, [showCreateAllEbayProductsModal])

    const renderMarkup = products.map((item, index) => {
        return <ProductRow key={index} item={item} selectedResource={intersectedIds} activeId={activeId}
                           setActive={setActive} allResourcesSelected={allResourcesSelected} refetchProducts={refetchProducts}/>
    })
    
    function isEmpty(value) {
        if (Array.isArray(value)) {
            return value.length === 0;
        } else {
            return value === '' || value == null;
        }
    }

    const emptyStateMarkup = <EmptyProductState/>

    return (
        <>
            {showCreateAllEbayProductsModal ? <CreateAllEbayProductsModal active={showCreateAllEbayProductsModal}
                                                                          handleChange={handleCreateAllEbayProductsModal}
                                                                          selectedItemLocation={selectedItemLocation}
                                                                          refetchProdDetails={refetchProdDetails}/> : null}

            <IndexTable
                resourceName={resourceName}
                showHeader={true}
                hasMoreItems
                itemCount={products.length}
                selectedItemsCount={allResourcesSelected ? 'All' : intersectedIds.length}
                onSelectionChange={handleSelectionChange1}
                promotedBulkActions={promotedBulkActions()}
                emptyState={emptyStateMarkup}
                headings={[
                    {title: '', hidden: true},
                    {title: t('common.products')},
                    {title: t('common.site')},
                    {title: t('common.inventory')},
                    {title: t('common.viewOn')},
                    {title: t('common.action')},
                ]}
                pagination={createTablePagination({
                    pagination,
                    onPageChange: setUrl
                })}
            >
                {renderMarkup}
            </IndexTable>
        </>
    )
}
