import {
    <PERSON><PERSON>ta<PERSON>,
    <PERSON>,
    Card,

} from "@shopify/polaris";
import React, { useCallback, useContext, useEffect, useState } from "react";
import { useAppQuery } from "../../../hooks/index.js";
import ProductIndexTable from "../Table/ProductIndexTable.jsx";
import ModalState from "../../../context/Modal/ModalState.jsx";
import EbayProductFetch from "../ProductFetch/EbayProductFetch.jsx";
import EbayProductsBanner from "../../Banner/EbayProductsBanner.jsx";
import { t } from "i18next";
import FilterProductsByItemLocation from "../Filter/FilterProductsByItemLocation.jsx";
import AppRatingBanner from "../../Common/AppRatingBanner.jsx";
import { TabsSkeletonComponent } from "../../Skeleton/TabsSkeletonComponent.jsx";
import EbayConnectionContext from "../../../context/EbayConnection/EbayConnectionContext.jsx";
import RefreshTokenExpiredWarningBanner from "../../Banner/RefreshTokenExpiredWarningBanner.jsx";
import LearnMoreLinks from "../../../util/LearnMoreLinks.jsx";
import SmartIndexFilters from "../../Common/SmartIndexFilters.jsx";
import StatusFilter from "../../Common/Filter/StatusFilter.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function EbayProductListPanel({ refetchProdDetails, showAppRating = false }) {
    const ebayConnectionContext = useContext(EbayConnectionContext)
    const windowUrl = new URL(window.location.href);
    const params = new URLSearchParams(windowUrl.search);
    params.get('filter')
    const filter = params.get('filter') ?? 'all'
    const [products, setProducts] = useState([]);
    const [pagination, setPagination] = useState({});
    const [searchQuery, setSearchQuery] = useState('')
    const [paginate, setPaginate] = useState()
    const [activeTab, setActiveTab] = useState(filter)
    const [isFirstRender, setIsFirstRender] = useState(true)
    const [productCounts, setProductCounts] = useState({})
    
    // Valid sort options
    const validSortOptions = [
        'default asc',
        'default desc',
        'start_time asc',
        'start_time desc',
        'end_time asc',
        'end_time desc',
        'updated asc',
        'updated desc'
    ];

    // Helper function to load sort preference from localStorage
    const loadSortFromLocalStorage = () => {
        try {
            const savedSort = localStorage.getItem('ebay-product-list-sort');
            if (savedSort) {
                const parsedSort = JSON.parse(savedSort);
                // Validate that it's an array with at least one element and the value is valid
                if (Array.isArray(parsedSort) && parsedSort.length > 0 && validSortOptions.includes(parsedSort[0])) {
                    return parsedSort;
                }
            }
        } catch (error) {
            console.warn('Failed to load sort preference from localStorage:', error);
        }
        return ['default asc']; // fallback to default
    };

    const [sortSelected, setSortSelected] = useState(loadSortFromLocalStorage)

    // Save sort preference to localStorage whenever it changes
    useEffect(() => {
        try {
            localStorage.setItem('ebay-product-list-sort', JSON.stringify(sortSelected));
        } catch (error) {
            console.warn('Failed to save sort preference to localStorage:', error);
        }
    }, [sortSelected]);

    //item locations
    const [itemLocationOptions, setItemLocationOptions] = useState([])
    const [selectedItemLocation, setItemLocation] = useState('')

    const backendURL = window.location.origin
    const apiPath = '/api/ebay/listings'
    const fullUrl = new URL(backendURL.concat(apiPath))
    const [url, setUrl] = useState(fullUrl.href)
    const [isApiCalling, setIsApiCalling] = useState(false);

    // for index table
    const [selectedIds, setSelectedIds] = useState([])
    const [intersectedIds, setIntersectedIds] = useState([])
    useEffect(() => {
        const inPageProductsIds = products.map(product => product.id);
        const updatedIntersectedIds = selectedIds.filter(id => inPageProductsIds.includes(id));
        setIntersectedIds(updatedIntersectedIds);
    }, [selectedIds, pagination.current_page])

    // updates the url if any key is pressed
    useEffect(() => {
        if (searchQuery.length > 0) {
            fullUrl.searchParams.set('search', searchQuery)
        }
        if (activeTab && activeTab !== 'all') {
            fullUrl.searchParams.set('filter', activeTab)
        }
        if (paginate) {
            fullUrl.searchParams.set('paginate', paginate)
        }
        if (selectedItemLocation) {
            fullUrl.searchParams.set('selected_item_location', selectedItemLocation)
        }
        if (sortSelected[0] !== 'default asc') {
            fullUrl.searchParams.set('sort_by', sortSelected[0])
        }
        setUrl(fullUrl.href)
    }, [searchQuery, activeTab, paginate, selectedItemLocation, sortSelected])

    const addProductsAndPaginationToState = (res) => {
        setProducts(Object.values(res.result.products.data))
        setPagination({
            current_page: res.result.products.current_page,
            last_page: res.result.products.last_page,
            from: res.result.products.from,
            prevPageUrl: res.result.products.prev_page_url,
            nextPageUrl: res.result.products.next_page_url,
        })
        setProductCounts({
            exportedCount: res.result.exported_count ?? 0,
            notExportedCount: res.result.not_exported_count ?? 0,
            endedCount: res.result.ended_count ?? 0,
        })
    }
    const addItemLocationOptionsFromResult = (itemLocations) => {
        const itemLocationOptions = itemLocations.map(item => ({
            label: item.value, value: item.site,
        }))
        setItemLocationOptions(itemLocationOptions)
    }

    const {
        data, isFetching, isSuccess, refetch
    } = useAppQuery({
        url: `${url}`, reactQueryOptions: {
            onSuccess: (data) => {
                addProductsAndPaginationToState(data)
                addItemLocationOptionsFromResult(data.result.filter_by_locations)
            },
        },
    });

    function disambiguateLabel(key, value) {
        switch (key) {
            case 'countryFilter':
                const collection = itemLocationOptions.find(option => option.value === value[0]);
                return collection ?
                    t('ebayProducts.filterByItemSellingLocation') + getFormattedSelectedItemLocation() :
                    '';
            default:
                return value;
        }
    }
    function isEmpty(value) {
        if (Array.isArray(value)) {
            return value.length === 0;
        } else {
            return value === '' || value == null;
        }
    }
    function handleSelectedItemLocationRemove() {
        setItemLocation('')
    }
    function getFormattedSelectedItemLocation() {
        if (!selectedItemLocation.length) {
            return ''
        }
        if (selectedItemLocation.length <= 2) {
            return `: ${selectedItemLocation}`;
        }
        return `: ${selectedItemLocation[0]},${selectedItemLocation[1]},+${selectedItemLocation.length - 2} more`;
    }
    const [appliedFilters, setAppliedFilters] = useState([])
    
    const handleFiltersClearAll = useCallback(() => {
        setItemLocation('')
        setActiveTab('all')
    }, []);

    const handleCancelAction = useCallback(() => {
        setItemLocation('')
    }, []);

    const views = [{
        tabName: t('common.allActive'),
        count: (productCounts.exportedCount ?? 0) + (productCounts.notExportedCount ?? 0),
        key: 'all',
    }, {
        tabName: t('common.importedToShopify'),
         count: productCounts.exportedCount ?? 0,
          key: 'imported'
    }, {
        tabName: t('common.notImported'),
         count: productCounts.notExportedCount ?? 0,
          key: 'not_imported'
    }, {
        tabName: t('common.endedOnEbay'),
         count: productCounts.endedCount ?? 0,
          key: 'ended'
    }];

    // Build applied filters
    useEffect(() => {
        const filters = []
        
        if (selectedItemLocation && !isEmpty(selectedItemLocation)) {
            const key = 'countryFilter';
            filters.push({
                key,
                label: disambiguateLabel(key, selectedItemLocation),
                onRemove: handleSelectedItemLocationRemove,
            });
        }
        
        // Status filter - only show when a specific status is selected (not 'all')
        if (activeTab !== 'all') {
            const currentView = views.find(view => view.key === activeTab);
            if (currentView) {
                filters.push({
                    key: 'statusFilter',
                    label: t('common.statusFilter', { status: currentView.tabName }),
                    onRemove: () => setActiveTab('all'),
                    pinned: true,
                });
            }
        }

        const sortedFilters = filters.sort((a, b) => a.key.localeCompare(b.key));
        const sortedAppliedFilters = appliedFilters.sort((a, b) => a.key.localeCompare(b.key));
        // Only update appliedFilters if the new filters are different
        const areFiltersEqual = JSON.stringify(sortedFilters) === JSON.stringify(sortedAppliedFilters);
        if (!areFiltersEqual) {
            setAppliedFilters(filters);
        }
    }, [selectedItemLocation, activeTab, productCounts]);

    const filters = [
        {
            key: 'statusFilter',
            label: t('common.status'),
            filter: <StatusFilter statusOptions={views.filter(view => view.key !== 'all').map(view => ({
                label: view.tabName,
                value: view.key,
            }))}
            selectedStatus={[activeTab]}
            setStatus={setActiveTab} />,
            pinned: true,
        },
    ];

    // Only add country filter if there are multiple location options
    if (itemLocationOptions.length > 1) {
        filters.push({
            key: 'countryFilter',
            label: t('ebayProducts.filterByItemSellingLocation') + getFormattedSelectedItemLocation(),
            filter: <FilterProductsByItemLocation selectedItemLocation={selectedItemLocation}
                setItemLocation={setItemLocation}
                itemLocationOptions={itemLocationOptions} />,
            pinned: true
        });
    }

    const sortOptions =  [
        {label: t('sortOptions.productTitle'), value: 'default asc', directionLabel: t('sortOptions.AZSortDirection')},
        {label: t('sortOptions.productTitle'), value: 'default desc', directionLabel: t('sortOptions.ZASortDirection')},
        {label: t('sortOptions.startTime'), value: 'start_time asc', directionLabel: t('sortOptions.earliestFirstSortDirection')},
        {label: t('sortOptions.startTime'), value: 'start_time desc', directionLabel: t('sortOptions.latestFirstSortDirection')},
        {label: t('sortOptions.endTime'), value: 'end_time asc', directionLabel: t('sortOptions.earliestFirstSortDirection')},
        {label: t('sortOptions.endTime'), value: 'end_time desc', directionLabel: t('sortOptions.latestFirstSortDirection')},
        {label: t('sortOptions.updated'), value: 'updated asc', directionLabel: t('sortOptions.oldestFirstSortDirection')},
        {label: t('sortOptions.updated'), value: 'updated desc', directionLabel: t('sortOptions.newestFirstSortDirection')},
    ]


    if (isSuccess) {
        if (data.result.noProducts) {
            return <EbayProductFetch refetchProducts={refetchProdDetails} />
        }
    }

    const performApiCall = async () => {
        const response = await fetch("/api/ebay/fetchEbayListings");
        return response.json();
    }

    const fetchEbayProducts = () => {
        setIsApiCalling(true)
        performApiCall().then(function (res){
            if (res.success && res.result.process_completed) {
                if (res.result.refresh_token_expired){
                    ebayConnectionContext.setEbayConnection(
                        { ...ebayConnectionContext.ebayConnection, refresh_token_expired: 1 }
                    )
                }
                setIsApiCalling(false)
            } else if (res.success){
                refetchProdDetails().then(()=>{
                    setIsApiCalling(false)
                })
            }
        })
    };

    useEffect(() => {
        if (isFirstRender && !isFetching) {
            setIsFirstRender(false)
        }
        if (isFetching) {
            shopify.loading(true);
        }
        if (!isFetching) {
            shopify.loading(false);
        }
    }, [isFetching, isFirstRender])

    return (
        <>
            <TitleBar title={t("common.ebayProducts")}>
                <button loading={isApiCalling ? "" : undefined} onClick={() => fetchEbayProducts()}>
                    {t('common.syncProductsFromEbay')}
                </button>
            </TitleBar>
            <Page fullWidth={true}>
            <BlockStack gap="400">
                {showAppRating && <AppRatingBanner />}
                {<EbayProductsBanner />}
                {!!ebayConnectionContext.ebayConnection.refresh_token_expired && <RefreshTokenExpiredWarningBanner />}
                <Card padding="0">
                    <ModalState>
                        {
                            isFetching && isFirstRender ?
                                <TabsSkeletonComponent /> :
                                <>
                                    <SmartIndexFilters
                                        activeTab={activeTab}
                                        setActiveTab={setActiveTab}
                                        setSearchQuery={setSearchQuery}
                                        handleFiltersClearAll={handleFiltersClearAll}
                                        handleCancelAction={handleCancelAction}
                                        views={views}
                                        appliedFilters={appliedFilters}
                                        filters={filters}
                                        sortOptions={sortOptions}
                                        sortSelected={sortSelected}
                                        onSort={setSortSelected}
                                        loading={isFetching}
                                    />
                                    <ProductIndexTable products={products}
                                        refetchProducts={refetch}
                                        activeTab={activeTab}
                                        setActiveTab={setActiveTab}
                                        searchQuery={searchQuery}
                                        refetchProdDetails={refetchProdDetails}
                                        selectedIds={selectedIds}
                                        setSelectedIds={setSelectedIds}
                                        intersectedIds={intersectedIds}
                                        selectedItemLocation={selectedItemLocation}
                                        pagination={pagination}
                                        setUrl={setUrl}
                                    />
                                </>
                        }
                    </ModalState>
                </Card>
                <LearnMoreLinks />
            </BlockStack>
            </Page>
        </>
    )
}
