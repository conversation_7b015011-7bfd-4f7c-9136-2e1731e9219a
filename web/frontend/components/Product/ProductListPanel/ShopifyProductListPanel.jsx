import {
    <PERSON><PERSON>ta<PERSON>,
    Page,
    Card,
} from "@shopify/polaris";
import React, { useCallback, useContext, useEffect, useState } from "react";
import ShopifyProductIndexTable from "../Table/Shopify/ShopifyProductIndexTable.jsx";
import { useAppQuery } from "../../../hooks/index.js";
import ShopifyProductsBanner from "../../Banner/ShopifyProductsBanner.jsx";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";
import FilterShopifyProductsByCollection from "../Filter/FilterShopifyProductsByCollection.jsx";
import FilterShopifyProductsByLocation from "../Filter/FilterShopifyProductsByLocation.jsx";
import { TabsSkeletonComponent } from "../../Skeleton/TabsSkeletonComponent.jsx";
import RefreshTokenExpiredWarningBanner from "../../Banner/RefreshTokenExpiredWarningBanner.jsx";
import EbayConnectionContext from "../../../context/EbayConnection/EbayConnectionContext.jsx";
import ShopifyProductFetchingProgress from "../../ProgressBar/ShopifyProductFetchingProgress.jsx";
import LearnMoreLinks from "../../../util/LearnMoreLinks.jsx";
import SmartIndexFilters from "../../Common/SmartIndexFilters.jsx";
import StatusFilter from "../../Common/Filter/StatusFilter.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function ShopifyProductListPanel() {
    const fullUrl = new URL(window.location.origin + '/api/shopify/products?should_include_count=1&related_fields=variations');
    const [products, setProducts] = useState([]);
    const [pagination, setPagination] = useState({});
    const [searchQuery, setSearchQuery] = useState('')
    const [activeTab, setActiveTab] = useState('all')
    const [url, setUrl] = useState('')
    const navigate = useNavigate();
    const [isFirstRender, setIsFirstRender] = useState(true)
    const [productCounts, setProductCounts] = useState({})
    
    // Valid sort options
    const validSortOptions = [
        'default asc',
        'default desc',
        'updated asc',
        'updated desc',
        'created asc',
        'created desc'
    ];

    // Helper function to load sort preference from localStorage
    const loadSortFromLocalStorage = () => {
        try {
            const savedSort = localStorage.getItem('shopify-product-list-sort');
            if (savedSort) {
                const parsedSort = JSON.parse(savedSort);
                // Validate that it's an array with at least one element and the value is valid
                if (Array.isArray(parsedSort) && parsedSort.length > 0 && validSortOptions.includes(parsedSort[0])) {
                    return parsedSort;
                }
            }
        } catch (error) {
            console.warn('Failed to load sort preference from localStorage:', error);
        }
        return ['default asc']; // fallback to default
    };

    const [sortSelected, setSortSelected] = useState(loadSortFromLocalStorage)
    const [appliedFilters, setAppliedFilters] = useState([])
    const [selectedCollection, setSelectedCollection] = useState(undefined)
    const [shopifyCollectionOptions, setShopifyCollectionOptions] = useState([])
    const [selectedLocation, setSelectedLocation] = useState(undefined)
    const [shopifyLocationOptions, setShopifyLocationOptions] = useState([])
    const ebayConnectionContext = useContext(EbayConnectionContext)

    // Save sort preference to localStorage whenever it changes
    useEffect(() => {
        try {
            localStorage.setItem('shopify-product-list-sort', JSON.stringify(sortSelected));
        } catch (error) {
            console.warn('Failed to save sort preference to localStorage:', error);
        }
    }, [sortSelected]);

    function disambiguateLabel(key, value) {
        switch (key) {
            case 'collectionFilter':
                const collection = shopifyCollectionOptions.find(option => option.value === value[0]);
                return collection ?
                    t('shopifyProducts.filters.collectionFilter.selectionLabel', { label: collection.label }) :
                    '';
            case 'locationFilter':
                const location = shopifyLocationOptions.find(option => option.value === value[0]);
                return location ?
                    t('shopifyProducts.filters.locationFilter.selectionLabel', { label: location.label }) :
                    '';
            default:
                return value;
        }
    }

    function isEmpty(value) {
        if (Array.isArray(value)) {
            return value.length === 0;
        } else {
            return value === '' || value == null;
        }
    }

    const handleCollectionRemove = () => {
        setSelectedCollection(undefined)
    }

    const handleLocationRemove = () => {
        setSelectedLocation(undefined)
    }

    // Define views array for reuse (moved here so it's available for useEffect)
    const views = [
        {
            tabName: t('common.all'),
            count: (productCounts.exportedCount ?? 0) + (productCounts.notExportedCount ?? 0),
            key: 'all',
        },
        {
            tabName: t('common.uploaded'),
            count: productCounts.exportedCount ?? 0,
            key: 'exported'
        },
        {
            tabName: t('common.notUploaded'),
            count: productCounts.notExportedCount ?? 0,
            key: 'not_exported'
        }
    ];

    useEffect(() => {
        const filters = []
        if (selectedCollection && !isEmpty(selectedCollection)) {
            const key = 'collectionFilter';
            filters.push({
                key,
                label: disambiguateLabel(key, selectedCollection),
                onRemove: () => {
                    handleCollectionRemove()
                },
            });
        }
        if (selectedLocation && !isEmpty(selectedLocation)) {
            const key = 'locationFilter';
            filters.push({
                key,
                label: disambiguateLabel(key, selectedLocation),
                onRemove: () => {
                    handleLocationRemove()
                },
            });
        }
        // Status filter - only show when a specific status is selected (not 'all')
        if (activeTab !== 'all') {
            const currentView = views.find(view => view.key === activeTab);
            if (currentView) {
                filters.push({
                    key: 'statusFilter',
                    label: t('common.statusFilter', { status: currentView.tabName }),
                    onRemove: () => setActiveTab('all'),
                    pinned: true,
                });
            }
        }
        const sortedFilters = filters.sort((a, b) => a.key.localeCompare(b.key));
        const sortedAppliedFilters = appliedFilters.sort((a, b) => a.key.localeCompare(b.key));
        // Only update appliedFilters if the new filters are different
        const areFiltersEqual = JSON.stringify(sortedFilters) === JSON.stringify(sortedAppliedFilters);
        if (!areFiltersEqual) {
            setAppliedFilters(filters);
        }
    }, [selectedCollection, selectedLocation, activeTab, productCounts]);


    useEffect(() => {
        if (searchQuery.length > 0) {
            fullUrl.searchParams.set('search', searchQuery)
        }

        if (activeTab && activeTab !== 'all') {
            fullUrl.searchParams.set('filter', activeTab)
        }
       
        if (sortSelected[0] !== 'default asc') {
            fullUrl.searchParams.set('sort_by', sortSelected[0])

        }
        if (selectedCollection && !isEmpty(selectedCollection)) {
            fullUrl.searchParams.set('collection_id_filter_by', selectedCollection.toString())

        }
        if (selectedLocation && !isEmpty(selectedLocation)) {
            fullUrl.searchParams.set('location_id_filter_by', selectedLocation.toString())

        }
        setUrl(fullUrl.href)
    }, [searchQuery, activeTab, sortSelected, selectedCollection, selectedLocation])

    const addProductsAndPaginationToState = (res) => {
        setProducts(res.result.products.data)
        setPagination({
            current_page: res.result.products.current_page,
            last_page: res.result.products.last_page,
            from: res.result.products.from,
            prevPageUrl: res.result.products.prev_page_url,
            nextPageUrl: res.result.products.next_page_url,
        })
        setProductCounts({
            exportedCount: res.result.exported_count ?? 0,
            notExportedCount: res.result.not_exported_count ?? 0,
        })
    }

    const {
        isFetching, refetch
    } = useAppQuery({
        url: `${url}`, reactQueryOptions: {
            onSuccess: (data) => {
                addProductsAndPaginationToState(data)
            },
        },
    });


    useEffect(() => {
        if (isFirstRender && !isFetching) {
            setIsFirstRender(false);
        }
        if (isFetching) {
            shopify.loading(true);
        }
        if (!isFetching) {
            shopify.loading(false);
        }
    }, [isFetching, isFirstRender]);

    const handleFiltersClearAll = useCallback(() => {
        setSelectedCollection(undefined)
        setSelectedLocation(undefined)
        setActiveTab('all')
    }, []);

    const handleCancelAction = useCallback(() => {
        setSelectedCollection(undefined)
        setSelectedLocation(undefined)
    }, []);

    const filters = [
        {
            key: 'statusFilter',
            label: t('common.status'),
            filter: <StatusFilter statusOptions={views.filter(view => view.key !== 'all').map(view => ({
                label: view.tabName,
                value: view.key,
            }))}
            selectedStatus={[activeTab]}
            setStatus={setActiveTab} />,
            pinned: true,
        },
        {
            key: 'collectionFilter',
            label: t('shopifyProducts.filters.collectionFilter.filterName'),
            filter: <FilterShopifyProductsByCollection
                selectedCollection={selectedCollection}
                setSelectedCollection={setSelectedCollection}
                shopifyCollectionOptions={shopifyCollectionOptions}
                setShopifyCollectionOptions={setShopifyCollectionOptions}

            />,
            pinned: true,
        },
    ];

    // Only show location filter if user has selected locations configured
    if (ebayConnectionContext?.ebayUserSettings?.selected_locations) {
        filters.push({
            key: 'locationFilter',
            label: t('shopifyProducts.filters.locationFilter.filterName'),
            filter: <FilterShopifyProductsByLocation
                selectedLocation={selectedLocation}
                setSelectedLocation={setSelectedLocation}
                shopifyLocationOptions={shopifyLocationOptions}
                setShopifyLocationOptions={setShopifyLocationOptions}
            />,
            pinned: true,
        });
    }

    const sortOptions = [
        {label: t('sortOptions.productTitle'), value: 'default asc', directionLabel: t('sortOptions.AZSortDirection')},
        {label: t('sortOptions.productTitle'), value: 'default desc', directionLabel: t('sortOptions.ZASortDirection')},
        {label: t('sortOptions.updated'), value: 'updated asc', directionLabel: t('sortOptions.oldestFirstSortDirection')},
        {label: t('sortOptions.updated'), value: 'updated desc', directionLabel: t('sortOptions.newestFirstSortDirection')},
        {label: t('sortOptions.created'), value: 'created asc', directionLabel: t('sortOptions.oldestFirstSortDirection')},
        {label: t('sortOptions.created'), value: 'created desc', directionLabel: t('sortOptions.newestFirstSortDirection')},
    ]
    return (
        <>
            <TitleBar title={t("shopifyProducts.shopifyProducts")}>
                <button variant="primary" onClick={() => navigate('/shopify/products/collections')}>
                    {t("shopifyProducts.uploadShopifyCollection")}
                </button>
            </TitleBar>
            <Page fullWidth>
            <BlockStack gap="400">
                {<ShopifyProductsBanner/>}
                {!!ebayConnectionContext.ebayConnection.refresh_token_expired && <RefreshTokenExpiredWarningBanner />}
                {
                    !!ebayConnectionContext.shop.is_shopify_products_importing && <ShopifyProductFetchingProgress refetch={refetch} />
                }
                <Card padding={'0'}>
                    <SmartIndexFilters
                        activeTab={activeTab}
                        setActiveTab={setActiveTab}
                        setSearchQuery={setSearchQuery}
                        handleFiltersClearAll={handleFiltersClearAll}
                        handleCancelAction={handleCancelAction}
                        views={views}
                        appliedFilters={appliedFilters}
                        filters={filters}
                        sortOptions={sortOptions}
                        sortSelected={sortSelected}
                        onSort={setSortSelected}
                        loading={isFetching}
                    />
                    {
                        isFirstRender ?
                        <TabsSkeletonComponent /> :
                        <ShopifyProductIndexTable products={products}
                            refetchProducts={refetch}
                            activeTab={activeTab}
                            searchQuery={searchQuery}
                            setActiveTab={setActiveTab}
                            setUrl={setUrl}
                            pagination={pagination}
                        />
                    }
                </Card>
                <LearnMoreLinks />
            </BlockStack>
            </Page>
        </>
    );
}
