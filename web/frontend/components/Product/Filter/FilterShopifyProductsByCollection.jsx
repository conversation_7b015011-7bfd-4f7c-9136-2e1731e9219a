import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>line<PERSON><PERSON>ck, <PERSON><PERSON>, TextField} from "@shopify/polaris";
import {SearchIcon} from "@shopify/polaris-icons";
import React, {useCallback, useEffect, useState} from "react";
import debounce from "../../../util/Debounce.js";
import {t} from "i18next";
import {useAppQuery} from "../../../hooks/index.js";

export default function FilterShopifyProductsByCollection({
                                                              selectedCollection,
                                                              setSelectedCollection,
                                                              shopifyCollectionOptions,
                                                              setShopifyCollectionOptions
}) {
    //new one
    const collectionUrl = new URL(window.location.origin + '/api/shopify/collection-filter-list')
    const [shopifyCollectionFetchUrl, setShopifyCollectionFetchUrl] = useState(collectionUrl.href)
    const [queryValue, setQueryValue] = useState('')
    const [shopifyCollectionOptionsPagination, setShopifyCollectionOptionsPagination] = useState([])
    const [collectionSearchValue, setCollectionSearchValue] = useState('');

    useEffect(() => {
        if (collectionSearchValue.length > 0) {
            collectionUrl.searchParams.set('search', collectionSearchValue)
        }
        setShopifyCollectionFetchUrl(collectionUrl.href)
    }, [collectionSearchValue])

    const {isFetching: isFetchingCollection} = useAppQuery({
        url: `${shopifyCollectionFetchUrl}`,
        reactQueryOptions: {
            onSuccess: (data) => {
                const responseData = data.result.data || [];
                setShopifyCollectionOptionsPagination({
                    current_page: data.result.current_page,
                    last_page: data.result.last_page,
                    from: data.result.from,
                    prevPageUrl: data.result.prev_page_url,
                    nextPageUrl: data.result.next_page_url,
                });

                setShopifyCollectionOptions(prevCollections => {
                    if (collectionSearchValue.length > 0) {
                        // Clear old data and store new data if it's a search result
                        return responseData.map(item => ({
                            label: item.title,
                            value: item.id,
                        }));
                    } else {
                        // Otherwise, retain old data and add new unique objects
                        const uniqueMap = new Map(prevCollections.map(obj => [obj.value, obj]));

                        responseData.forEach(item => {
                            if (!uniqueMap.has(item.id)) {
                                uniqueMap.set(item.id, {
                                    label: item.title,
                                    value: item.id,
                                });
                            }
                        });

                        return Array.from(uniqueMap.values());
                    }
                });
            },
        },
    });

    const handleCollectionSearchChange = useCallback(
        (newValue) => setCollectionSearchValue(newValue),
        [],
    );

    const loadMoreShopifyCollections = () => {
        const nextPageUrl = shopifyCollectionOptionsPagination.nextPageUrl
        if (nextPageUrl) {
            setShopifyCollectionFetchUrl(nextPageUrl)
        }
    }

    const debouncedSearchOnChange = debounce(handleCollectionSearchChange, 300);

    const handleOnQueryChangeWrapper = useCallback(
        (value) => {
            setQueryValue(value)
            debouncedSearchOnChange(value)
        },
        [],
    );

    return <>
            <TextField
                prefix={<Icon
                    source={SearchIcon}
                    tone="base"
                />}
                onClearButtonClick={() => (handleOnQueryChangeWrapper(''))}
                clearButton={true}
                placeholder={t('shopifyProducts.filters.collectionFilter.searchPlaceHolder')}
                value={queryValue}
                onChange={handleOnQueryChangeWrapper}
                autoComplete="off"
                label={""}/>

        {
            isFetchingCollection  ?
            <InlineStack align={"center"}>
                <Spinner/>
            </InlineStack>
                :
            <ChoiceList
                titleHidden
                choices={shopifyCollectionOptions}
                selected={selectedCollection || []}
                onChange={setSelectedCollection}
                title={""}/>}
        <div>
            {shopifyCollectionOptionsPagination.nextPageUrl &&
                <Button
                    onClick={loadMoreShopifyCollections}
                    variant="plain">{t('shopifyProducts.filters.collectionFilter.showMoreCollections')}</Button>}
        </div>
    </>;
}
