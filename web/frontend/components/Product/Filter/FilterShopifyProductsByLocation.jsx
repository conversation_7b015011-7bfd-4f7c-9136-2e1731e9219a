import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>line<PERSON><PERSON>ck, <PERSON><PERSON>, TextField} from "@shopify/polaris";
import {SearchIcon} from "@shopify/polaris-icons";
import React, {useCallback, useEffect, useState} from "react";
import debounce from "../../../util/Debounce.js";
import {t} from "i18next";
import {useAppQuery} from "../../../hooks/index.js";

export default function FilterShopifyProductsByLocation({
                                                             selectedLocation,
                                                             setSelectedLocation,
                                                             shopifyLocationOptions,
                                                             setShopifyLocationOptions
}) {
    const locationUrl = new URL(window.location.origin + '/api/shopify/location-filter-list')
    const [shopifyLocationFetchUrl, setShopifyLocationFetchUrl] = useState(locationUrl.href)
    const [queryValue, setQueryValue] = useState('')
    const [shopifyLocationOptionsPagination, setShopifyLocationOptionsPagination] = useState([])
    const [locationSearchValue, setLocationSearchValue] = useState('');

    useEffect(() => {
        if (locationSearchValue.length > 0) {
            locationUrl.searchParams.set('search', locationSearchValue)
        }
        setShopifyLocationFetchUrl(locationUrl.href)
    }, [locationSearchValue])

    const {isFetching: isFetchingLocation} = useAppQuery({
        url: `${shopifyLocationFetchUrl}`,
        reactQueryOptions: {
            onSuccess: (data) => {
                const responseData = data.result.data || [];
                setShopifyLocationOptionsPagination({
                    current_page: data.result.current_page,
                    last_page: data.result.last_page,
                    from: data.result.from,
                    prevPageUrl: data.result.prev_page_url,
                    nextPageUrl: data.result.next_page_url,
                });

                setShopifyLocationOptions(prevLocations => {
                    if (locationSearchValue.length > 0) {
                        // Clear old data and store new data if it's a search result
                        return responseData.map(item => ({
                            label: item.name,
                            value: item.id,
                        }));
                    } else {
                        // Otherwise, retain old data and add new unique objects
                        const uniqueMap = new Map(prevLocations.map(obj => [obj.value, obj]));

                        responseData.forEach(item => {
                            if (!uniqueMap.has(item.id)) {
                                uniqueMap.set(item.id, {
                                    label: item.name,
                                    value: item.id,
                                });
                            }
                        });

                        return Array.from(uniqueMap.values());
                    }
                });
            },
        },
    });

    const handleLocationSearchChange = useCallback(
        (newValue) => setLocationSearchValue(newValue),
        [],
    );

    const loadMoreShopifyLocations = () => {
        const nextPageUrl = shopifyLocationOptionsPagination.nextPageUrl
        if (nextPageUrl) {
            setShopifyLocationFetchUrl(nextPageUrl)
        }
    }

    const debouncedSearchOnChange = debounce(handleLocationSearchChange, 300);

    const handleOnQueryChangeWrapper = useCallback(
        (value) => {
            setQueryValue(value)
            debouncedSearchOnChange(value)
        },
        [],
    );

    return <>
            <TextField
                prefix={<Icon
                    source={SearchIcon}
                    tone="base"
                />}
                onClearButtonClick={() => (handleOnQueryChangeWrapper(''))}
                clearButton={true}
                placeholder={t('shopifyProducts.filters.locationFilter.searchPlaceHolder')}
                value={queryValue}
                onChange={handleOnQueryChangeWrapper}
                autoComplete="off"
                label={""}/>

        {
            isFetchingLocation  ?
            <InlineStack align={"center"}>
                <Spinner/>
            </InlineStack>
                :
            <ChoiceList
                titleHidden
                choices={shopifyLocationOptions}
                selected={selectedLocation || []}
                onChange={setSelectedLocation}
                title={""}/>}
        <div>
            {shopifyLocationOptionsPagination.nextPageUrl &&
                <Button
                    onClick={loadMoreShopifyLocations}
                    variant="plain">{t('shopifyProducts.filters.locationFilter.showMoreLocations')}</Button>}
        </div>
    </>;
} 