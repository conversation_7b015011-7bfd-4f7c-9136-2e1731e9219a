import {
    Layout,
    LegacyCard,
    SkeletonBodyText,
    SkeletonDisplayText, Page,
} from '@shopify/polaris';
  import React from 'react';
import DashboardWelcomeBanner from "../Banner/DashboardWelcomeBanner.jsx";
import {t} from "i18next";
import { TitleBar } from "@shopify/app-bridge-react";

  export default function SkeletonPageComponent() {
    return (
        <>
            <TitleBar title={t("dashboard.dashboard")} />
            <Page>
              <Layout>
                  <Layout.Section>
                  <DashboardWelcomeBanner/>
                  </Layout.Section>
                <Layout.Section>
                <LegacyCard sectioned>
                  <SkeletonBodyText />
                </LegacyCard>
                <LegacyCard sectioned>
                    <SkeletonDisplayText size="small" />
                    <SkeletonBodyText />
                </LegacyCard>
                <LegacyCard sectioned>
                    <SkeletonDisplayText size="small" />
                    <SkeletonBodyText />
                </LegacyCard>
              </Layout.Section>
            </Layout>
            </Page>
        </>
    );
  }
