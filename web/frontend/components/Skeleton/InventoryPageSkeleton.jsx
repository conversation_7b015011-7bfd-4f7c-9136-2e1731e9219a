import {
    <PERSON><PERSON>tack, <PERSON>ton, ButtonGroup,
    Card, Divider, InlineGrid, Link, Page,
    SkeletonTabs, Text,
} from "@shopify/polaris";
import React from "react";
import InventoryBanner from "../Banner/InventoryBanner.jsx";
import { t } from "i18next";
import { helpScoutArticles } from "../../util/HelpscoutArticles.js";
import { TabsSkeletonComponent } from "./TabsSkeletonComponent.jsx";
import LearnMoreLinks from "../../util/LearnMoreLinks.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function InventoryPageSkeleton() {
    return (
        <>
            <TitleBar title={t("common.linkedInventory")} />
            <Page fullWidth>
                <BlockStack gap="400">
                    <InventoryBanner />
                    <Card padding="0">
                        <SkeletonTabs />
                        <TabsSkeletonComponent />
                    </Card>
                    <LearnMoreLinks />
                </BlockStack>
            </Page>
        </>
    )
}
