import {
    BlockStack,
    Button,
    ButtonGroup,
    Card,
    Grid,
    InlineGrid,
    Layout,
    Page,
    SkeletonBodyText,
    SkeletonDisplayText,
    Text
} from "@shopify/polaris";
import {t} from "i18next";
import * as DateUtility from "../../../util/DateUtility.js";
import InfoCardBanner from "../../Common/InfoCardBanner.jsx";
import React from "react";
import { TitleBar } from "@shopify/app-bridge-react";

export function PricingSkeleton() {
    const trialDays = 7;
    const trialEndedDate = DateUtility.addDays(new Date(), trialDays);

    const planSkeleton = () => {
        return (<div className={"card-bordered subscription-features"}>
                <Card>
                    <BlockStack gap="300">
                        <InlineGrid columns="1fr auto">
                            <Text as="h1" variant="headingLg">
                                <SkeletonDisplayText size="small"/>
                            </Text>
                        </InlineGrid>

                        <BlockStack gap="200">
                            <Text as="h1" variant="headingXl" fontWeight="medium">
                                <SkeletonDisplayText size="small"/>
                            </Text>

                            <div style={{marginTop: '15px', marginBottom: '15px'}}>
                                <SkeletonBodyText lines={10}/>
                            </div>
                        </BlockStack>

                        <ButtonGroup>
                            <Button
                                variant={"primary"}
                                disabled={true}
                                size={"large"}
                                loading={true}/>
                        </ButtonGroup>
                    </BlockStack>
                </Card>
            </div>
        )
    }

    return (
        <>
            <TitleBar title={t("common.pricing")} />
            <Page>
                <Layout>
                    <Layout.Section>
                        <InfoCardBanner message={t('subscription.banner.body',
                            {
                                trialEndedDate: DateUtility.humanReadableDate(trialEndedDate),
                                trialDays: trialDays
                            })
                        }/>
                    </Layout.Section>

                    <Layout.Section>
                        <Grid columns={{xs: 1, sm: 1, md: 1, lg: 2}}>
                            <Grid.Cell key={1}>
                                {planSkeleton()}
                            </Grid.Cell>
                        </Grid>
                    </Layout.Section>
                </Layout>
            </Page>
        </>
    )
}
