import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    LegacyStack,
    <PERSON>, 
    SkeletonTabs
} from "@shopify/polaris";
import React from "react";
import { t } from "i18next";
import EbayProductsBanner from "../Banner/EbayProductsBanner.jsx";
import ModalState from "../../context/Modal/ModalState.jsx";
import { TabsSkeletonComponent } from "./TabsSkeletonComponent.jsx";
import LearnMoreLinks from "../../util/LearnMoreLinks.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function ProductPageSkeleton() {
    return (
        <>
            <TitleBar title={t("common.ebayProducts")} />
            <Page fullWidth={true}>
                <BlockStack gap="400">
                    <EbayProductsBanner />
                    <Card padding="0">
                        <ModalState>
                            <LegacyStack alignment={"center"} spacing={"tight"} distribution={"equalSpacing"}
                                wrap={false}>
                                <LegacyStack.Item fill>
                                    <SkeletonTabs />
                                </LegacyStack.Item>
                            </LegacyStack>
                            <TabsSkeletonComponent />
                        </ModalState>
                    </Card>
                    <LearnMoreLinks />
                </BlockStack>
            </Page>
        </>
    )
}
