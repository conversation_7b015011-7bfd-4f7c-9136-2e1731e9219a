import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>,
    Divider,
    LegacyStack,
    Page, SkeletonTabs,
} from "@shopify/polaris";
import React from "react";
import { t } from "i18next";
import { TabsSkeletonComponent } from "./TabsSkeletonComponent.jsx";
import ShopifyProductsBanner from "../Banner/ShopifyProductsBanner.jsx";
import LearnMoreLinks from "../../util/LearnMoreLinks.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function ShopifyProductPageSkeleton() {
    return (
        <>
            <TitleBar title={t("shopifyProducts.shopifyProducts")} />
            <Page fullWidth>
                <BlockStack gap="400">
                    <ShopifyProductsBanner />
                    <Card padding={'0'}>
                        <LegacyStack alignment={"center"} spacing={"tight"} distribution={"equalSpacing"}
                            wrap={false}>
                            <LegacyStack.Item fill>
                                <SkeletonTabs />
                            </LegacyStack.Item>
                        </LegacyStack>
                        <TabsSkeletonComponent />
                    </Card>
                    <LearnMoreLinks />
                </BlockStack>
            </Page>
        </>
    )
}
