import {Card, LegacyCard, Page, SkeletonBodyText} from "@shopify/polaris";
import React from "react";
import {t} from "i18next";
import LoadingBanner from "../Banner/LoadingBanner.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function ProductListSkeleton({title, filterPlaceHolder, isShopify = false}) {
    return (
        <>
            <TitleBar title={!isShopify ? t("common.ebayProducts") : t("shopifyProducts.shopifyProducts")} />
            <Page fullWidth>
                <LoadingBanner pageName={title}/>
                <Card>
                <SkeletonBodyText/>
                </Card>
            </Page>
        </>
    )
}
