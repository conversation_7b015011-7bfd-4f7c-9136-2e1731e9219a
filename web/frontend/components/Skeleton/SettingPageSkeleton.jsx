import {
    Bleed,
    BlockStack, Box, LegacyCard,
    Card, Divider, InlineGrid, Page,
    SkeletonBodyText, Text, useBreakpoints,
} from "@shopify/polaris";
import React from "react";
import {t} from "i18next";
import { TitleBar } from "@shopify/app-bridge-react";

export default function SettingPageSkeleton() {
    const { smUp } = useBreakpoints();
    return (
        <>
            <TitleBar title={t('common.settings')} />
            <Page divider>
                <BlockStack gap={{ xs: "800", sm: "400" }}>
                    <InlineGrid columns={{ xs: "1fr", md: "2fr 5fr" }} gap="400">
                        <Box
                            as="section"
                            paddingInlineStart={{ xs: 400, sm: 0 }}
                            paddingInlineEnd={{ xs: 400, sm: 0 }}
                        >
                            <BlockStack gap="400">
                                <Text as="h3" variant="headingMd">
                                    Order & Inventory Sync
                                </Text>
                                <Text as="p" variant="bodyMd">
                                    Enable or disable order, inventory, & tax sync and set VAT settings.
                                </Text>
                            </BlockStack>
                        </Box>
                        <Card roundedAbove="sm">
                            <Bleed marginInlineStart="300">
                                <LegacyCard.Section title={t('settings.inventoryAndOrderSettings.orderAndInventorySync.label')}>
                                <SkeletonBodyText lines={5}/>
                                </LegacyCard.Section>
                                <LegacyCard.Section title={t('settings.inventoryAndOrderSettings.sharedSku.label')}>
                                <SkeletonBodyText lines={4}/>
                                </LegacyCard.Section>
                                <LegacyCard.Section title={t('settings.shopifyOrderIdConfig.title')}>
                                <SkeletonBodyText lines={4}/>
                                </LegacyCard.Section>
                                <LegacyCard.Section  title={t('settings.shopifyOrderTags.title')}>
                                <SkeletonBodyText lines={4}/>
                                </LegacyCard.Section>
                                <LegacyCard.Section title={t('settings.taxAndVatConfig.title')}>
                                <SkeletonBodyText lines={12}/>
                                </LegacyCard.Section>

                            </Bleed>
                        </Card>
                    </InlineGrid>
                    {smUp ? <Divider /> : null}
                    <InlineGrid columns={{ xs: "1fr", md: "2fr 5fr" }} gap="400">
                        <Box
                            as="section"
                            paddingInlineStart={{ xs: 400, sm: 0 }}
                            paddingInlineEnd={{ xs: 400, sm: 0 }}
                        >
                            <BlockStack gap="400">
                                <Text as="h3" variant="headingMd">
                                    Product Sync
                                </Text>
                                <Text as="p" variant="bodyMd">
                                    Configure product tag synchronization and automatic SKU linking.
                                </Text>
                            </BlockStack>
                        </Box>
                        <Card roundedAbove="sm">
                            <Bleed marginInlineStart="300">
                                <SkeletonBodyText lines={5}/>
                            </Bleed>
                        </Card>
                    </InlineGrid>
                    {smUp ? <Divider /> : null}
                    <InlineGrid columns={{ xs: "1fr", md: "2fr 5fr" }} gap="400">
                        <Box
                            as="section"
                            paddingInlineStart={{ xs: 400, sm: 0 }}
                            paddingInlineEnd={{ xs: 400, sm: 0 }}
                        >
                            <BlockStack gap="400">
                                <Text as="h3" variant="headingMd">
                                    General
                                </Text>
                                <Text as="p" variant="bodyMd">
                                    Manage automatic SKU generation and end product listings on status change.
                                </Text>
                            </BlockStack>
                        </Box>
                        <Card roundedAbove="sm">
                            <Bleed marginInlineStart="300">
                                <SkeletonBodyText lines={5}/>
                            </Bleed>
                        </Card>
                    </InlineGrid>
                    {smUp ? <Divider /> : null}
                    <InlineGrid columns={{ xs: "1fr", md: "2fr 5fr" }} gap="400">
                        <Box
                            as="section"
                            paddingInlineStart={{ xs: 400, sm: 0 }}
                            paddingInlineEnd={{ xs: 400, sm: 0 }}
                        >
                            <BlockStack gap="400">
                                <Text as="h3" variant="headingMd">
                                    Currency
                                </Text>
                                <Text as="p" variant="bodyMd">
                                    Specify the currency settings sync for your eBay store.
                                </Text>
                            </BlockStack>
                        </Box>
                        <Card roundedAbove="sm">
                            <Bleed marginInlineStart="600">
                                <SkeletonBodyText lines={5}/>
                            </Bleed>
                        </Card>
                    </InlineGrid>
                </BlockStack>
                <div className="spacer-bottom-2x"></div>
            </Page>
        </>
    )
}
