import {
    <PERSON>Stack,
    <PERSON>ton,
    ButtonGroup, Card,
    InlineGrid,
    Link,
    Page,
    Text
} from "@shopify/polaris";
import React from "react";
import { t } from "i18next";
import { TabsSkeletonComponent } from "./TabsSkeletonComponent.jsx";
import EbayProfileBanner from "../Banner/EbayProfileBanner.jsx";
import LearnMoreLinks from "../../util/LearnMoreLinks.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function ProfileListSkeleton() {
    return (
        <>
            <TitleBar title={t("common.ebayProfiles")}>
                <button variant="primary" onClick={() => navigate('/shopify/products')}>
                    {t("common.uploadToEbay")}
                </button>
            </TitleBar>
            <Page>
                <BlockStack gap="400">
                    <EbayProfileBanner />
                    <Card padding={"0"}>
                        <TabsSkeletonComponent />
                    </Card>
                    <LearnMoreLinks />
                </BlockStack>
            </Page>
        </>
    )
}
