import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON>, 
    Divider, 
    <PERSON>,
    SkeletonTabs,
} from "@shopify/polaris";
import React from "react";
import OrdersBanner from "../Banner/OrdersBanner.jsx";
import { t } from "i18next";
import { TabsSkeletonComponent } from "./TabsSkeletonComponent.jsx";
import LearnMoreLinks from "../../util/LearnMoreLinks.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function OrderPageSkeleton() {
    return (
        <>
            <TitleBar title={t("common.orders")} />
            <Page>
                <BlockStack gap="400">
                    <OrdersBanner />
                    <Card padding="0">
                        <SkeletonTabs />
                        <TabsSkeletonComponent />
                    </Card>
                    <LearnMoreLinks />
                </BlockStack>
            </Page>
        </>
    )
}
