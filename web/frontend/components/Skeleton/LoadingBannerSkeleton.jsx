import {Card, Page, SkeletonBodyText} from "@shopify/polaris";
import React from "react";
import LoadingBanner from "../Banner/LoadingBanner.jsx";
import {t} from "i18next";
import { TitleBar } from "@shopify/app-bridge-react";

export default function LoadingBannerSkeleton({path}) {

    const nonFullWidthPages = ['/orders','/settings']
    const getTitle = ()=> {
        if(path === '/ebay/products') {
            return t('common.ebayProducts')
        }
        if(path === '/shopify/products') {
            return t('common.shopifyProducts')
        }
        if(path === '/profiles') {
            return t('common.ebayProfiles')
        }
        if(path === '/orders') {
            return t('common.orders')
        }
        if(path === '/inventory') {
            return t("inventory.linkedSkuAndInventory")
        }
        if(path === '/settings') {
            return t("common.settings")
        }
        return ""
    }
    return (
        <>
            <TitleBar title={getTitle()} />
            <Page fullWidth={!nonFullWidthPages.includes(path)}>
                <LoadingBanner/>
                <Card>
                <SkeletonBodyText/>
                </Card>
            </Page>
        </>
    )
}
