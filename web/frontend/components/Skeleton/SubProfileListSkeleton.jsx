import {LegacyCard, Filter<PERSON>, <PERSON>, SkeletonBodyText} from "@shopify/polaris";
import React from "react";
import { TitleBar } from "@shopify/app-bridge-react";

export default function SubProfileListSkeleton() {
    return (
        <>
            <TitleBar title={"Profile Products"} />
            <Page fullWidth={true}>
                <LegacyCard>
                    <LegacyCard.Section>
                        <Filters
                            queryPlaceholder={"Search by Product SKU or Tile"}
                            filters={[]}
                        />
                    </LegacyCard.Section>
                    <LegacyCard.Section>
                        <SkeletonBodyText/>
                    </LegacyCard.Section>
                </LegacyCard>
            </Page>
        </>
    )
}
