import {BlockStack, Card, Checkbox, Text, TextField} from "@shopify/polaris";
import {t} from "i18next";
import OrderAndInventorySettingsComponent from "../../Common/OrderAndInventorySettingsComponent.jsx";
import ShopifyOrderIdComponent from "./ShopifyOrderIdComponent.jsx";
import EnableEbayOrderIdSyncing from "./EnableEbayOrderIdSyncing.jsx";
import SyncEbayOrderIdConsent from "../../Modal/Setting/SyncEbayOrderIdConsent.jsx";
import React, {useCallback, useState} from "react";
import TaxAndVatConfig from "./TaxAndVatConfig.jsx";
import VatSyncComponent from "./VatSyncComponent.jsx";
import ShopifyOrderTagsComponent from "./ShopifyOrderTagsComponent.jsx";
import SharedSKUInventorySync from "./SharedSKUInventorySync.jsx";
import {Trans} from "react-i18next";

export default function OrderAndInventoryPanel({setOrderAndInventoryData, orderAndInventoryData}) {
    const [isOpen, setIsOpen] = useState(false);

    const handleOverrideUntrackedOrContinueSellingQtyChange =  useCallback(
        (newValue) => {
            setOrderAndInventoryData((oldData) => ({
                ...oldData,
                overrideUntrackedOrContinueSellingQty: newValue,
            }))
        },
        [],
    );

    function isOverridingQtyValueInvalid(content) {

        return content && content  <= 0;
    }

    const overridingQtyValueInvalid = isOverridingQtyValueInvalid(orderAndInventoryData.overrideUntrackedOrContinueSellingQty);
    const overridingQtyValueInvalidMessage = overridingQtyValueInvalid
        ? 'Invalid Quantity Value'
        : '';

    const handleSyncEbayOrderEmailChange = useCallback((newValue) => {
        setOrderAndInventoryData((oldData) => ({
            ...oldData,
            syncEbayOrderEmail: newValue,
        }))
    }, [])

    const handleOrderFailNotificationChange = useCallback((newValue) => {
        setOrderAndInventoryData((oldData) => ({
            ...oldData,
            orderFailNotification: newValue,
        }))
    }, [])

    const handleSyncShippingAsBillingAddressChange = useCallback((newValue) => {
        setOrderAndInventoryData((oldData) => ({
            ...oldData,
            syncShippingAsBillingAddress: newValue,
        }))
    }, [])

    return (
        <BlockStack gap="400">
            {isOpen && <SyncEbayOrderIdConsent setIsOpen={setIsOpen} updateParentData={setOrderAndInventoryData}/>}
            <Card>
                <BlockStack gap="400">
                    <BlockStack gap="200">
                        <Text variant="headingSm" as="h3">{t('settings.inventoryAndOrderSettings.orderAndInventorySync.label')}</Text>
                        <OrderAndInventorySettingsComponent
                            updateParentData={setOrderAndInventoryData}
                            orderAndInventoryData={orderAndInventoryData} />
                    </BlockStack>
                    {!orderAndInventoryData.orderAndInventorySync.includes('no-order-and-inventory') &&
                        <BlockStack gap="200">
                            <Text variant="headingSm" as="h3">{t('settings.inventoryAndOrderSettings.sharedSku.label')}</Text>
                            <SharedSKUInventorySync
                                orderAndInventoryData={orderAndInventoryData}
                                updateParentData={setOrderAndInventoryData} />
                        </BlockStack>
                    }
                </BlockStack>
            </Card>
            <Card>
                <BlockStack gap="400">
                    <BlockStack gap="200">
                        <Text variant="headingSm" as="h3">{t('settings.shopifyOrderIdConfig.title')}</Text>
                        <EnableEbayOrderIdSyncing
                            setIsOpen={setIsOpen}
                            orderAndInventoryData={orderAndInventoryData}
                            updateParentData={setOrderAndInventoryData} />
                        {!!orderAndInventoryData.syncEbayOrderId &&
                            <BlockStack gap="200">
                                <ShopifyOrderIdComponent
                                    orderAndInventoryData={orderAndInventoryData}
                                    updateParentData={setOrderAndInventoryData} />
                            </BlockStack>
                        }
                    </BlockStack>
                    <BlockStack gap="200">
                        <Text variant="headingSm" as="h3">{t('settings.shopifyOrderTags.title')}</Text>
                        <ShopifyOrderTagsComponent
                            orderAndInventoryData={orderAndInventoryData}
                            updateParentData={setOrderAndInventoryData}
                    />
                    </BlockStack>
                   
                </BlockStack>
            </Card>
            <Card>
                <BlockStack gap="400">
                    <BlockStack gap="200">
                        <Text variant="headingSm" as="h3">{t('settings.inventoryAndOrderSettings.orderEmailSync.title')}</Text>
                        <Checkbox
                        label={t('settings.inventoryAndOrderSettings.orderEmailSync.label')}
                        checked={orderAndInventoryData.syncEbayOrderEmail}
                        onChange={handleSyncEbayOrderEmailChange}
                        helpText={t('settings.inventoryAndOrderSettings.orderEmailSync.helpText')}
                    />
                    </BlockStack>
                    <BlockStack gap="200">
                        <Text variant="headingSm" as="h3">{t('settings.inventoryAndOrderSettings.syncShippingAsBillingAddress.title')}</Text>
                        <Checkbox
                            label={t('settings.inventoryAndOrderSettings.syncShippingAsBillingAddress.label')}
                            checked={orderAndInventoryData.syncShippingAsBillingAddress}
                            onChange={handleSyncShippingAsBillingAddressChange}
                            helpText={t('settings.inventoryAndOrderSettings.syncShippingAsBillingAddress.helpText')}
                        />
                    </BlockStack>
                    <BlockStack gap="200">
                        <Text variant="headingSm" as="h3">{t('settings.taxAndVatConfig.title')}</Text>
                        <TaxAndVatConfig
                            updateParentData={setOrderAndInventoryData}
                            orderAndInventoryData={orderAndInventoryData}
                        />
                        <VatSyncComponent
                            orderAndInventoryData={orderAndInventoryData}
                            updateParentData={setOrderAndInventoryData} />
                </BlockStack>
                </BlockStack>
            </Card>
            <Card>
                <BlockStack gap="400">
                    <BlockStack gap="200">
                        <Text variant="bodyMd" as="span" fontWeight="semibold" style={{ 'display': 'inline' }}>
                            {t('settings.inventoryAndOrderSettings.overrideUntrackedOrContinueSellingQty.label')}
                        </Text>
                        <TextField
                            type="number"
                            error={overridingQtyValueInvalidMessage}
                            id="overrideUntrackedOrContinueSellingQtyID"
                            value={orderAndInventoryData.overrideUntrackedOrContinueSellingQty}
                            onChange={handleOverrideUntrackedOrContinueSellingQtyChange}
                            min={1}
                            autoComplete="off"
                            label={''}
                            helpText={<Trans i18nKey={"settings.inventoryAndOrderSettings.overrideUntrackedOrContinueSellingQty.description"}  values={{ overrideTypeName:  t('settings.inventoryAndOrderSettings.overrideUntrackedOrContinueSellingQty.typeName')}}>
                            <b>{t("settings.inventoryAndOrderSettings.overrideUntrackedOrContinueSellingQty.description")}</b>
                        </Trans>} />
                    </BlockStack>
                    <BlockStack gap="200">
                        <Text variant="headingSm" as="h3">{t('settings.inventoryAndOrderSettings.orderFailNotification.title')}</Text>
                        <Checkbox
                            label={t('settings.inventoryAndOrderSettings.orderFailNotification.label')}
                            checked={orderAndInventoryData.orderFailNotification}
                            onChange={handleOrderFailNotificationChange}
                            helpText={t('settings.inventoryAndOrderSettings.orderFailNotification.helpText')}
                        />
                    </BlockStack>
                </BlockStack>
            </Card>
        </BlockStack>
    )
}
