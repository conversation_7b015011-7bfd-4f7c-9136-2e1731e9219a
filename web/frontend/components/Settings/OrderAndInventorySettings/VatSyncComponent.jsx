import {Checkbox, TextField, InlineStack, Box} from "@shopify/polaris";
import React, {useCallback} from "react";
import {t} from "i18next";

export default function VatSyncComponent({updateParentData,orderAndInventoryData =null}) {
    const handleChange = useCallback((newValue) => {
        updateParentData((oldData) => ({
            ...oldData,
            vatPercentageChecked: newValue,
        }))
    }, [])


    const handleVatPercentageChange = useCallback((newValue) => {
        updateParentData((oldData) => ({
            ...oldData,
            vatPercentage: newValue,
        }))
    }, [])

    const handleCustomTaxTitleChange = useCallback((newValue) => {
        updateParentData((oldData) => ({
            ...oldData,
            customTaxTitle: newValue,
        }))
    }, [])

    return (
        <>
            <Checkbox
                label={t('settings.inventoryAndOrderSettings.vatSync.CheckboxLabel')}
                checked={orderAndInventoryData.vatPercentageChecked}
                onChange={handleChange}
            />

            {orderAndInventoryData.vatPercentageChecked &&
                <InlineStack gap="400">
                    <Box style={{flex: 1}}>
                        <TextField
                            type={"text"}
                            label={t('settings.inventoryAndOrderSettings.vatSync.customTaxTitleLabel')}
                            value={orderAndInventoryData.customTaxTitle ?? 'VAT'}
                            onChange={handleCustomTaxTitleChange}
                            placeholder={t('settings.inventoryAndOrderSettings.vatSync.customTaxTitlePlaceholder')}
                            autoComplete="off"
                        />
                    </Box>
                    <Box style={{flex: 1}}>
                        <TextField
                            type={"number"}
                            label={t('settings.inventoryAndOrderSettings.vatSync.vatPercentageLabel')}
                            value={orderAndInventoryData.vatPercentage}
                            onChange={handleVatPercentageChange}
                            autoComplete="off"
                        />
                    </Box>
                </InlineStack>
            }
        </>
    )
}
