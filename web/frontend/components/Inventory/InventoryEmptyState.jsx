import {EmptyState, Legacy<PERSON><PERSON>, <PERSON>} from "@shopify/polaris";
import React from "react";
import {useNavigate} from "react-router-dom";
import {t} from "i18next";
import InventoryBanner from "../Banner/InventoryBanner.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function InventoryEmptyState() {
    const navigate = useNavigate()
    return (
        <>
            <TitleBar title={t("inventory.inventory")} />
            <Page fullWidth>
                <InventoryBanner />
                <LegacyCard>
                    <EmptyState
                        heading={t('inventory.emptyStateHeading')}
                        action={{
                            content: t('inventory.emptyStatePrimaryButtonLabel'),
                            onAction() {
                                navigate('/settings?active_tab=0')
                            }
                        }}
                        image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                    >
                        <p>{t('inventory.emptyStateDescription')}</p>
                    </EmptyState>
                </LegacyCard>
            </Page>
        </>
    )
}
