import { useCallback, useEffect, useState } from "react";
import { useAppQuery } from "../../hooks";
import { BlockStack, Page, Card } from "@shopify/polaris";
import InventoryBanner from "../Banner/InventoryBanner";
import { SkeletonTabs } from "@shopify/polaris";
import { TabsSkeletonComponent } from "../Skeleton/TabsSkeletonComponent";
import SmartIndexFilters from "../Common/SmartIndexFilters";
import InventoryIndexTable from "./InventoryIndexTable";
import LearnMoreLinks from "../../util/LearnMoreLinks";
import StatusFilter from "../Common/Filter/StatusFilter";
import { t } from "i18next";
import { TitleBar } from "@shopify/app-bridge-react";

export default function InventoryListPanel() {
    const [isFirstRender, setIsFirstRender] = useState(true)
    const backendURL = window.location.origin
    const apiPath = '/api/ebay/inventory';
    const [products, setProducts] = useState({});
    const [search, setSearchQuery] = useState('')
    const [activeTab, setActiveTab] = useState('all')
    const [pagination, setPagination] = useState({});
    const [appliedFilters, setAppliedFilters] = useState([])
   

    // Centralized URL building function that preserves all parameters
    const buildUrl = useCallback((params = {}) => {
        const fullUrl = new URL(backendURL.concat(apiPath))
        
        // Apply search parameter
        const searchValue = params.search !== undefined ? params.search : search
        if (searchValue && searchValue.length > 0) {
            fullUrl.searchParams.set('search', searchValue)
        }
        
        // Apply product status parameter
        const statusValue = params.productStatus !== undefined ? params.productStatus : activeTab
        if (statusValue === 'linked') {
            fullUrl.searchParams.set('has_shopify_variant', 'true')
        }
        
        // Apply any additional parameters (like pagination)
        if (params.page) {
            fullUrl.searchParams.set('page', params.page)
        }
        
        return fullUrl.href
    }, [backendURL, apiPath, search, activeTab])

    const [url, setUrl] = useState(() => buildUrl())

    // Update URL when search changes
    useEffect(() => {
        setUrl(buildUrl())
    }, [search, buildUrl])

    // Update URL when product status changes
    useEffect(() => {
        setUrl(buildUrl())
    }, [activeTab, buildUrl])

    // Custom pagination handler that preserves all filter parameters
    const handlePaginationChange = useCallback((paginationUrl) => {
        const urlObj = new URL(paginationUrl)
        const pageParam = urlObj.searchParams.get('page')
        
        if (pageParam) {
            setUrl(buildUrl({ page: pageParam }))
        }
    }, [buildUrl])


    const { isFetching } = useAppQuery({
        url: `${url}`, reactQueryOptions: {
            onSuccess: (data) => {
                setProducts(data.result.data)
                setPagination({
                    current_page: data.result.current_page,
                    last_page: data.result.last_page,
                    prevPageUrl: data.result.prev_page_url,
                    nextPageUrl: data.result.next_page_url,
                })
            }
        }
    });

    useEffect(() => {
        if (isFirstRender && !isFetching) {
            setIsFirstRender(false)
        }
        if (isFetching) {
            shopify.loading(true);
        }
        if (!isFetching) {
            shopify.loading(false);
        }
    }, [isFetching, isFirstRender])

    
    

    const handleFiltersClearAll = useCallback(() => {
        setActiveTab('all')
    }, []);

    // Define views array for reuse
    const views = [
        {
            tabName: t('common.all'),
            key: 'all',
        },
        {
            tabName: t('common.linked'),
            key: 'linked',
        },
    ];

    // Build applied filters
    useEffect(() => {
        const filters = []
        
        // Status filter - only show when a specific status is selected (not 'all')
        if (activeTab !== 'all') {
            const currentView = views.find(view => view.key === activeTab);
            if (currentView) {
                filters.push({
                    key: 'statusFilter',
                    label: t('common.statusFilter', { status: currentView.tabName }),
                    onRemove: () => setActiveTab('all'),
                    pinned: true,
                });
            }
        }
        
        setAppliedFilters(filters)
    }, [activeTab]);

    const filters = [
        {
            key: 'statusFilter',
            label: t('common.status'),
            filter: <StatusFilter statusOptions={views.filter(view => view.key !== 'all').map(view => ({
                label: view.tabName,
                value: view.key,
            }))}
            selectedStatus={[activeTab]}
            setStatus={setActiveTab} />,
            pinned: true,
        },
    ];
    return (
        <>
            <TitleBar title={t("common.linkedInventory")} />
            <Page fullWidth>
                <BlockStack gap="400">
                    <InventoryBanner />
                    <Card padding="0">
                        {
                            isFirstRender ?
                                <>
                                    <SkeletonTabs />
                                    <TabsSkeletonComponent />
                                </>
                                :
                                <>
                                    <SmartIndexFilters
                                        activeTab={activeTab}
                                        setActiveTab={setActiveTab}
                                        searchPlaceHolder={t('common.searchPlaceholder')}
                                        setSearchQuery={setSearchQuery}
                                        loading={isFetching}
                                        views={views}
                                        filters={filters}
                                        appliedFilters={appliedFilters}
                                        handleFiltersClearAll={handleFiltersClearAll}

                                    />
                                    <InventoryIndexTable products={products} setUrl={handlePaginationChange} pagination={pagination} />
                                </>
                        }

                    </Card>
                    <LearnMoreLinks />
                </BlockStack>
            </Page>
        </>
    )

}