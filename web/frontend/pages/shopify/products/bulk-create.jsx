import {useAuthenticatedFetch} from "../../../hooks/index.js";
import {useLocation, useNavigate} from "react-router-dom";
import {useEffect, useState} from "react";
import {<PERSON><PERSON>, Card, Divider, Page} from "@shopify/polaris";
import BulkActionTable from "../../../components/Product/Table/BulkActionTable.jsx";
import ExportProductsBanner from "../../../components/Banner/ExportProductsBanner.jsx";
import {t} from "i18next";

export default function ShopifyBulkCreate() {
    const fetch = useAuthenticatedFetch()
    const location = useLocation();
    const navigate = useNavigate()
    const [products, setProducts] = useState(location.state ?? []);
    const [isApiCall, setIsApiCall] = useState(false)
    const [showBanner, setShowBanner] = useState(true)

    useEffect(() => {
        if (!products.length) {
            navigate('/shopify/products')
        }
    }, [products])

    const createProductsData = products.filter(function (item) {
        return item.ebay_product_id === null
    });

    const handleDelete = (itemId) => {
        const finalArray = createProductsData.filter(function (item) {
            return item.id !== itemId;
        });
        setProducts(finalArray)
    }

    const handleBulkCreate = () => {
        navigate('/shopify/products/select-profile', {state: products})
    }

    const handleBackAction = () => {
        navigate('/shopify/products')
    };

    return (
        <>
            <Page
                title={t('shopifyProducts.uploadShopifyProductsToEbay')}
                backAction={{
                    content: t('common.goBack'),
                    onAction: handleBackAction
                }}
            >
                {showBanner ? <ExportProductsBanner setShowBanner={setShowBanner}/> : null}
                <div style={{'marginTop': '15px'}}>
                    <Card padding={'0'}>
                            <BulkActionTable products={createProductsData} handleDelete={handleDelete} isShopify={true}/>
                            <Divider></Divider>
                            <div className={'confirmation-page-action-bar'}>
                                <Button loading={isApiCall} onClick={handleBulkCreate}  variant="primary">
                                        {t('common.proceedToUpload')}
                                </Button>
                            </div>
                    </Card>
                </div>
            </Page>
        </>
    );

}
