import React, { useContext, useEffect, useState } from 'react';
import { useAppQuery } from "../../../../hooks/index.js";
import { BlockStack, Card, Page } from "@shopify/polaris";
import CollectionTable from "../../../../components/Collection/CollectionTable/CollectionTable.jsx";
import CollectionPageBanner from "../../../../components/Banner/CollectionPageBanner.jsx";
import { t } from "i18next";
import { useNavigate } from 'react-router-dom';
import RefreshTokenExpiredWarningBanner from "../../../../components/Banner/RefreshTokenExpiredWarningBanner.jsx";
import EbayConnectionContext from "../../../../context/EbayConnection/EbayConnectionContext.jsx";
import LearnMoreLinks from '../../../../util/LearnMoreLinks.jsx';
import { TabsSkeletonComponent } from '../../../../components/Skeleton/TabsSkeletonComponent.jsx';
import SmartIndexFilters from '../../../../components/Common/SmartIndexFilters.jsx';

export default function Collections() {
    const backendURL = window.location.origin
    const apiPath = '/api/shopify/collection'
    const fullUrl = new URL(backendURL.concat(apiPath))
    const [url, setUrl] = useState(fullUrl.href)
    const [searchQuery, setSearchQuery] = useState('')
    const [collections, setCollections] = useState([]);
    const [pagination, setPagination] = useState({});
    const [activeTab, setActiveTab] = useState('all')
    const [isFirstRender, setIsFirstRender] = useState(true)
    const ebayConnectionContext = useContext(EbayConnectionContext)

    const navigate = useNavigate()
    const handleBackAction = () => {
        navigate('/shopify/products')
    };

    useEffect(() => {
        if (searchQuery.length > 0) {
            fullUrl.searchParams.append('search', searchQuery)
        }
        setUrl(fullUrl.href)
    }, [searchQuery])
    const {
        isFetching
    } = useAppQuery({
        url: url, reactQueryOptions: {
            onSuccess: (data) => {
                addCollectionsAndPaginationToState(data.result)
            }
        }
    });
    const addCollectionsAndPaginationToState = (res) => {
        setCollections(res.data)
        setPagination({
            current_page: res.current_page,
            last_page: res.last_page,
            from: res.from,
            prevPageUrl: res.prev_page_url,
            nextPageUrl: res.next_page_url,
        })
    }
    useEffect(() => {
        if (isFirstRender && !isFetching) {
            setIsFirstRender(false)
        }
        if (isFetching) {
            shopify.loading(true);
        }
        if (!isFetching) {
            shopify.loading(false);
        }
    }, [isFetching, isFirstRender])

    const views = [
        {
            tabName: t('common.all'),
            key: 'all',
        },
    ]

    return (
        <>
            <Page
                title={t("shopifyCollections.shopifyCollections")}
                backAction={{
                    content: t('common.goBack'),
                    onAction: handleBackAction
                }}
            >
                <BlockStack gap="400">
                    <CollectionPageBanner />
                    {!!ebayConnectionContext.ebayConnection.refresh_token_expired && <RefreshTokenExpiredWarningBanner />}
                    <Card padding={'0'}>
                        {
                            isFirstRender ?
                                <TabsSkeletonComponent /> :
                                <>
                                    <SmartIndexFilters
                                        activeTab={activeTab}
                                        setSearchQuery={setSearchQuery}
                                        loading={isFetching}
                                        setActiveTab={setActiveTab}
                                        views={views}
                                        searchPlaceHolder={t('collections.searchPlaceholder')}
                                    />
                                    <CollectionTable
                                        collections={collections}
                                        setUrl={setUrl}
                                        pagination={pagination}
                                    />
                                </>
                        }
                    </Card>
                    <LearnMoreLinks />
                </BlockStack>
            </Page>
        </>
    )
}

