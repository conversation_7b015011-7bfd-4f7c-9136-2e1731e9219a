import {Page} from "@shopify/polaris";
import EbaySelectProfile from "../../../../components/Profile/EbaySelectProfile.jsx";
import {t} from "i18next";
import { useNavigate } from "react-router-dom";

export default function ProfileSelect() {
    const navigate = useNavigate()

    const handleBackAction = () => {
        navigate('/shopify/products/collections')
    };
    return (
        <>
            <Page
                title={t('shopifyProducts.uploadShopifyProductsToEbay')}
                backAction={{
                    content: t('common.goBack'),
                    onAction: handleBackAction
                }}
            >
                <EbaySelectProfile profileType={'collection'}/>
            </Page>
        </>
    )
}
