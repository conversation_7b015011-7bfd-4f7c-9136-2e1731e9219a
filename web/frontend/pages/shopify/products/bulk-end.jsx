import { <PERSON><PERSON>tack, <PERSON>ton, ButtonGroup, Card, Divider, Page } from "@shopify/polaris";
import { useLocation, useNavigate } from "react-router-dom";
import React, { useCallback, useEffect, useState } from "react";
import BulkActionTable from "../../../components/Product/Table/BulkActionTable.jsx";
import EndListingFromEbayBanner from "../../../components/Banner/EndListingFromEbayBanner.jsx";
import EbayEndListingCancellation from "../../../components/Product/EbayEndListingCancellation.jsx";
import { useAuthenticatedFetch } from "../../../hooks/index.js";
import EndListingInEbayModal from "../../../components/Modal/Product/EndListingInEbayModal.jsx";
import { t } from "i18next";

export default function BulkEnd() {
    const fetch = useAuthenticatedFetch();
    const location = useLocation();
    const navigate = useNavigate()
    const [products, setProducts] = useState(location.state ?? []);
    const [reasonSelected, setReason] = useState(['NotAvailable']);
    const [isLoading, setIsLoading] = useState(false)
    const [showEndModal, setShowEndModal] = useState(false);

    useEffect(() => {
        if (!products.length) {
            navigate('/shopify/products')
        }
    }, [products])

    const endListingsData = products.filter(function (item) {
        return item.ebay_product_id !== null
    });

    const handleEndListings = useCallback(() => {
        setIsLoading(true)
        const formData = {
            products: endListingsData,
            cancellation_reason: reasonSelected
        }
        performCreateApiCall(formData)
            .then((res) => {
                setIsLoading(false)
                setShowEndModal(false)
                navigate('/shopify/products')
            })
    }, [endListingsData, reasonSelected]);

    const handleShowConfirmModal = useCallback(() => {
        setShowEndModal(true)
    }, []);

    const handleCloseModal = () => {
        setShowEndModal(false);
    };

    const handleRemoveProduct = useCallback((itemId) => {
        const finalArray = endListingsData.filter(item => item.id !== itemId);
        setProducts(finalArray)
    }, [endListingsData]);

    const performCreateApiCall = async (data) => {
        const response = await fetch('/api/ebay/end-listings', {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        return response.json()
    }

    const handleBackAction = () => {
       setProducts([])
    };

    return (
        <>
            <Page
                title="End listings in eBay"
                backAction={{
                    content: t('common.goBack'),
                    onAction: handleBackAction
                }}
            >
                {showEndModal && (
                    <EndListingInEbayModal
                        open={true}
                        onClose={handleCloseModal}
                        onDelete={handleEndListings}
                        loading={isLoading}
                    />
                )}
                <BlockStack gap={'400'}>
                    <EndListingFromEbayBanner />
                    <Card padding={'0'}>
                        <BulkActionTable products={endListingsData} handleDelete={handleRemoveProduct} isShopify={true} />
                        <Divider></Divider>
                        <div className="spacer-bottom-.5x"></div>
                        <div style={{ marginLeft: '15px' }}>
                            <EbayEndListingCancellation reasonSelected={reasonSelected} setReason={setReason} />
                        </div>
                        <div className="spacer-bottom-.5x"></div>
                        <Divider />
                        <div className={'confirmation-page-action-bar'}>
                            <ButtonGroup>
                                <Button
                                    onClick={handleShowConfirmModal}
                                    loading={isLoading}
                                    variant="primary"
                                >
                                    Continue
                                </Button>
                            </ButtonGroup>
                        </div>
                    </Card>
                </BlockStack>
            </Page>
        </>
    );
}
