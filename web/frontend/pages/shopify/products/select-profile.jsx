import {Page} from "@shopify/polaris";
import EbaySelectProfile from "../../../components/Profile/EbaySelectProfile.jsx";
import {t} from "i18next";
import { useLocation, useNavigate } from "react-router-dom";

export default function ProfileSelect() {
    const navigate = useNavigate()
    const location = useLocation();

    const handleBackAction = () => {
        navigate('/shopify/products/bulk-create', { state: location.state })
    };

    return (
        <>
            <Page
                title={t('shopifyProducts.uploadShopifyProductsToEbay')}
                backAction={{
                    content: t('common.goBack'),
                    onAction: handleBackAction
                }}
            >
                <EbaySelectProfile profileType={'product'} />
            </Page>
        </>
    )
}
