import {IndexTable, Text, Page, <PERSON>tonGroup, <PERSON><PERSON>, Card, Divider} from '@shopify/polaris';
import {useLocation, useNavigate} from "react-router-dom";
import {useEffect, useState} from "react";
import {useAuthenticatedFetch} from "../../../hooks/index.js";
import DeleteEbayProductsBanner from "../../../components/Banner/DeleteProductsBanner.jsx";
import {t} from "i18next";
import ImageThumbnail from '../../../components/Thumbnail/ImageThumbnail.jsx';

export default function BulkDelete() {

    const fetch = useAuthenticatedFetch()
    const location = useLocation();
    const navigate = useNavigate()

    const [products, setProducts] = useState(location.state ?? []);
    const [isApiCall, setIsApiCall] = useState(false)

    useEffect(() => {
        if (!products.length) {
            navigate('/ebay/products')
        }
    }, [products])

    const resourceName = {
        singular: t('common.product'),
        plural: t('common.products'),
    };

    const deleteProductData = products.filter(function (item) {
        return item.shopify_product_id !== null
    });

    const handleDelete = (itemId) => {
        const finalArray = deleteProductData.filter(function (item) {
            return item.id !== itemId;
        });
        setProducts(finalArray)
    }

    const infoBanner = () => {
        return <DeleteEbayProductsBanner/>
    }

    const rowMarkup = deleteProductData.map(
            ({id, image_url, title}, index) => (
                <IndexTable.Row
                    id={id}
                    key={id}
                    position={index}
                >
                    <IndexTable.Cell className={'product-image-cell'}>
                        <div className="product-item">
                        <ImageThumbnail source={image_url === null ? null : JSON.parse(image_url)[0]} size="small"/>
                        </div>
                    </IndexTable.Cell>

                    <IndexTable.Cell>
                        <Text variant="bodyMd" fontWeight="bold" as="span">
                            {title}
                        </Text>
                    </IndexTable.Cell>
                    <IndexTable.Cell>
                        <Button
                            onClick={() => handleDelete(id)}
                            size="slim"
                            variant="plain"
                            tone="critical">{t('common.remove')}</Button>
                    </IndexTable.Cell>
                </IndexTable.Row>
            ),
        )
    ;

    const handleBulkDelete = async () => {
        setIsApiCall(true)
        let ids = deleteProductData.map((item) => item.id);
        const data = {productIds: ids}
        const response = await fetch(`/api/ebay/product/bulk-delete`, {
            method: "POST",
            body: JSON.stringify({data: data}),
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        if (response.ok) {
            setIsApiCall(false)
        }
        setProducts([])
    }

    const handleBackAction = () => {
        setProducts([])
     };

    return (
        <>
            <Page
                title={t('ebayProducts.bulkDeletePageTitle')}
                backAction={{
                    content: t('common.goBack'),
                    onAction: handleBackAction
                }}
            >
                {infoBanner()}
                <div style={{'marginTop': '15px'}}>
                    <Card padding={'0'}>
                            <IndexTable
                                resourceName={resourceName}
                                itemCount={products.length}
                                selectable={false}
                                headings={[
                                    {title: ''},
                                    {title: t('common.title')},
                                    {title: t('common.action')},
                                ]}
                            >
                                {rowMarkup}
                            </IndexTable>
                            <Divider></Divider>
                            <div className={'confirmation-page-action-bar'}>
                                <ButtonGroup>
                                    <Button
                                        loading={isApiCall}
                                        onClick={handleBulkDelete}
                                        variant="primary"
                                        >
                                        {t('common.proceedToDelete')}
                                    </Button>
                                </ButtonGroup>

                            </div>
                    </Card>
                </div>
            </Page>
        </>
    );
}



