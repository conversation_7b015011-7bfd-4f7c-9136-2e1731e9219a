import {<PERSON>, Divider, Page} from '@shopify/polaris';
import {useLocation, useNavigate} from "react-router-dom";
import {useEffect, useState} from "react";
import {useAuthenticatedFetch} from "../../../hooks/index.js";
import BulkActionTable from "../../../components/Product/Table/BulkActionTable.jsx";
import BulkCreateButtonGroup from "../../../components/Product/Button/BulkCreateButtonGroup.jsx";
import ExportProductsBanner from "../../../components/Banner/ExportProductsBanner.jsx";
import {t} from "i18next";

export default function BulkCreate() {
    const fetch = useAuthenticatedFetch()
    const location = useLocation();
    const navigate = useNavigate()

    const [products, setProducts] = useState(location.state ?? []);
    const [isApiCall, setIsApiCall] = useState(false)
    const [showBanner, setShowBanner] = useState(true)


    useEffect(() => {
        if (!products.length) {
            navigate('/ebay/products')
        }
    }, [products])

    const createProductsData = products.filter(function (item) {
        return item.shopify_product_id === null
    });

    const handleDelete = (itemId) => {
        const finalArray = createProductsData.filter(function (item) {
            return item.id !== itemId;
        });
        setProducts(finalArray)
    }
    const handleBulkCreate = async () => {
        setIsApiCall(true)
        let ids = products.map((item) => item.id);
        const data = {productIds: ids}
        const response = await fetch(`/api/ebay/product/bulk-create`, {
            method: "POST",
            body: JSON.stringify({data: data}),
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        if (response.ok) {
            setIsApiCall(false)
        }
        setProducts([])
    }

    const handleBackAction = () => {
        navigate('/ebay/products')
    };

    return (
        <>
            <Page
                title={t("ebayProducts.bulkCreatePageTitle")}
                backAction={{
                    content: t('common.goBack'),
                    onAction: handleBackAction
                }}
            >
                {showBanner ? <ExportProductsBanner setShowBanner={setShowBanner}/> : null}
                <div style={{'marginTop': '15px'}}>
                    <Card padding={'0'}>
                    
                            <BulkActionTable products={createProductsData} handleDelete={handleDelete}/>
                            <Divider></Divider>
                            <div className={'confirmation-page-action-bar'}>
                                <BulkCreateButtonGroup isApiCall={isApiCall} setProducts={setProducts}
                                                       handleBulkCreate={handleBulkCreate}/>
                            </div>
                    </Card>
                </div>
            </Page>
        </>
    )
}



