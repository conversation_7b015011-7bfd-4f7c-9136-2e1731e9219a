import {
    BlockS<PERSON>ck,
    Box,
    <PERSON>ton,
    ButtonGroup,
    Card,
    Form,
    InlineGrid,
    InlineStack,
    Page,
    Text,
    Banner
} from "@shopify/polaris";
import { t } from "i18next";
import OrderAndInventoryPanel from "../components/Settings/OrderAndInventorySettings/index.jsx";
import ProductSyncSettings from "../components/Settings/ImportSettings/index.jsx";
import CurrencySettings from "../components/Settings/CurrencySettings/index.jsx";
import GeneralSettings from "../components/Settings/GeneralSettings/index.jsx";
import LocationSettings from "../components/Settings/LocationSettings/index.jsx";
import { useAuthenticatedFetch } from "../hooks/index.js";
import EbayConnectionContext from "../context/EbayConnection/EbayConnectionContext.jsx";
import { useContext, useEffect, useState } from "react";
import { Save<PERSON><PERSON>, TitleBar } from "@shopify/app-bridge-react";
import { useLocations } from "../hooks/useLocations";
import LearnMoreLinks from "../util/LearnMoreLinks.jsx";


export default function Settings() {
    const fetch = useAuthenticatedFetch()
    const ebayConnectionContext = useContext(EbayConnectionContext)

    // Use the custom locations hook
    const { 
        locations,
        isLoading: isLoadingLocations,
        hasMultipleLocations
    } = useLocations();

    const { order_sync, inventory_sync } = ebayConnectionContext.ebayUserSettings
    let orderAndInventorySyncValue
    if (order_sync && inventory_sync) {
        orderAndInventorySyncValue = ["order-and-inventory"]
    } else if (inventory_sync) {
        orderAndInventorySyncValue = ["inventory-only"]
    } else {
        orderAndInventorySyncValue = ["no-order-and-inventory"]
    }
    const initialOrderAndInventoryState = {
        'orderAndInventorySync': orderAndInventorySyncValue,
        'syncEbayOrderId': !!ebayConnectionContext.ebayUserSettings.sync_ebay_order_id,
        'sharedSKUInventorySync': !!ebayConnectionContext.ebayUserSettings.shared_sku_inventory_sync,
        'syncEbayCollectedTax': !!ebayConnectionContext.ebayUserSettings.sync_ebay_collected_tax,
        'orderIdPrefixValue': ebayConnectionContext.ebayUserSettings.shopify_order_prefix,
        'orderIdPrefix': !!ebayConnectionContext.ebayUserSettings.shopify_order_prefix,
        'shopifyOrderTags': ebayConnectionContext.ebayUserSettings.shopify_order_tags,
        'vatPercentageChecked': !!ebayConnectionContext.ebayUserSettings.vat_percentage,
        'vatPercentage': ebayConnectionContext.ebayUserSettings.vat_percentage,
        'customTaxTitle': ebayConnectionContext.ebayUserSettings.custom_tax_title,
        'overrideUntrackedOrContinueSellingQty': ebayConnectionContext.ebayUserSettings.override_untracked_or_continue_selling_qty,
        'syncEbayOrderEmail': !!ebayConnectionContext.ebayUserSettings.sync_ebay_order_email,
        'orderFailNotification': !!ebayConnectionContext.ebayUserSettings.order_fail_notification,
        'syncShippingAsBillingAddress': !!ebayConnectionContext.ebayUserSettings.sync_shipping_as_billing_address,
    }

    const [saving, setSaving] = useState(false)
    const [orderAndInventoryData, setOrderAndInventoryData] = useState(initialOrderAndInventoryState)
    const [generalSettingsData, setGeneralSettingsData] = useState({
        autoSkuGeneration: Boolean(ebayConnectionContext.shop.auto_sku_generation),
        autoEndListing: Boolean(ebayConnectionContext.ebayUserSettings.end_linked_ebay_item_when_deleted)
    })
    const [currencyData, setCurrencyData] = useState({
        currency_conversion_rate: String(ebayConnectionContext.ebayUserSettings.currency_conversion_rate)
    })
    const [importSettingData, setImportSettingData] = useState({
        importShippingProfileNameAsTag: Boolean(ebayConnectionContext.ebayUserSettings.import_shipping_profile_name_as_tag),
        linkBasedOnSKUOnly: Boolean(ebayConnectionContext.ebayUserSettings.link_based_on_sku_only)
    })
    const [locationData, setLocationData] = useState({
        selectedLocations: ebayConnectionContext.ebayUserSettings.selected_locations
    })

    const [initial, setInitial] = useState({
        orderAndInventoryData, currencyData, generalSettingsData, importSettingData, locationData
    })
    const [currentState, setCurrentState] = useState({
        orderAndInventoryData, currencyData, generalSettingsData, importSettingData, locationData
    })

    useEffect(() => {
        setCurrentState({
            orderAndInventoryData, currencyData, generalSettingsData, importSettingData, locationData
        })

    }, [
        orderAndInventoryData,
        currencyData,
        generalSettingsData,
        importSettingData,
        locationData
    ])
    useEffect(() => {
        if (JSON.stringify(initial) !== JSON.stringify(currentState)) {
            shopify.saveBar.show('settings-save-bar')
        } else {
            shopify.saveBar.hide('settings-save-bar')
        }
    }, [currentState, initial]);

    const orderAndInventorySettingsComponent = <OrderAndInventoryPanel
        orderAndInventoryData={orderAndInventoryData}
        setOrderAndInventoryData={setOrderAndInventoryData} />

    const productSyncSettingsComponent = <ProductSyncSettings
        setImportSettingData={setImportSettingData}
        importSettingData={importSettingData}
    />

    const currencySettingsComponent = <CurrencySettings currencyData={currencyData}
        setCurrencyData={setCurrencyData} />

    const generalSettingsComponent = <GeneralSettings
        generalSettingsData={generalSettingsData}
        updateParentData={setGeneralSettingsData}
    />
    
    const locationSettingsComponent = <LocationSettings
        locationData={locationData}
        setLocationData={setLocationData}
        locations={locations}
        isLoading={isLoadingLocations}
    />

    const performCreateApiCall = async () => {
        const response = await fetch('api/update-settings', {
            method: "POST", body: JSON.stringify({
                orderAndInventoryData, currencyData, generalSettingsData, importSettingData, locationData
            }), headers: {
                'Accept': 'application/json', 'Content-Type': 'application/json'
            }
        })
        return response.json()
    }
    const reset = () => {
        setCurrentState(initial)
        setOrderAndInventoryData(initial.orderAndInventoryData)
        setGeneralSettingsData(initial.generalSettingsData)
        setCurrencyData(initial.currencyData)
        setImportSettingData(initial.importSettingData)
        setLocationData(initial.locationData)
    }

    const handleSave = () => {
        setSaving(true)
        performCreateApiCall().then((res) => {
            shopify.toast.show(t('settings.settingsUpdatedSuccessfully'),{duration:1000})
            setInitial({
                orderAndInventoryData, currencyData, generalSettingsData, importSettingData, locationData
            })
            // Refresh connection data to get the latest user settings from server
            ebayConnectionContext.refreshConnection().then(() => {
                setSaving(false);
            }).catch((error) => {
                console.error('Failed to refresh connection after settings save:', error);
                setSaving(false);
            });
        })
    }

    return (
        <>
            {(
                <SaveBar id="settings-save-bar">
                    <button variant="primary" loading={saving ? "" : undefined} onClick={handleSave}></button>
                    <button onClick={reset}></button>
                </SaveBar>
            )}

            <TitleBar title={t('common.settings')} />
            <Page divider>
                <Box paddingBlockEnd="200">
                    <BlockStack gap="400">
                        <InlineGrid columns={{ xs: "1fr", md: "2fr 5fr" }} gap="400">
                            <Box
                                as="section"
                                paddingInlineStart={{ xs: 400, sm: 0 }}
                                paddingInlineEnd={{ xs: 400, sm: 0 }}
                            >
                                <BlockStack gap="400">
                                    <Text as="h3" variant="headingMd">
                                        {t('settings.inventoryAndOrderSettings.title')}
                                    </Text>
                                    <Text as="p" variant="bodyMd">
                                        {t('settings.inventoryAndOrderSettings.description')}
                                    </Text>
                                </BlockStack>
                            </Box>
                            {orderAndInventorySettingsComponent}
                        </InlineGrid>
                        
                        {hasMultipleLocations  && (
                            <InlineGrid columns={{ xs: "1fr", md: "2fr 5fr" }} gap="400">
                                <Box
                                    as="section"
                                    paddingInlineStart={{ xs: 400, sm: 0 }}
                                    paddingInlineEnd={{ xs: 400, sm: 0 }}
                                >
                                    <BlockStack gap="400">
                                        <Text as="h3" variant="headingMd">
                                            {t('settings.locationSettings.title')}
                                        </Text>
                                        <Text as="p" variant="bodyMd">
                                            {t('settings.locationSettings.annotatedDescription')}
                                        </Text>
                                    </BlockStack>
                                </Box>
                                <Card>
                                    {!!ebayConnectionContext.shop.is_shopify_products_importing ? (
                                        <Banner status="info">
                                            <Text as="p" variant="bodyMd">
                                                {t('settings.locationSettings.disabledDuringImport', 'Location settings are disabled while Shopify products are being imported.')}
                                            </Text>
                                        </Banner>
                                    ):
                                    <LocationSettings
                                        locationData={locationData}
                                        setLocationData={setLocationData}
                                        locations={locations}
                                        isLoading={isLoadingLocations}
                                        disabled={!!ebayConnectionContext.shop.is_shopify_products_importing}
                                    />
                                }
                                </Card>
                            </InlineGrid>
                        )}
                        
                        <InlineGrid columns={{ xs: "1fr", md: "2fr 5fr" }} gap="400">
                            <Box
                                as="section"
                                paddingInlineStart={{ xs: 400, sm: 0 }}
                                paddingInlineEnd={{ xs: 400, sm: 0 }}
                            >
                                <BlockStack gap="400">
                                    <Text as="h3" variant="headingMd">
                                        {t('settings.productSyncSetting.title')}
                                    </Text>
                                    <Text as="p" variant="bodyMd">
                                        {t('settings.productSyncSetting.description')}
                                    </Text>
                                </BlockStack>
                            </Box>
                            <Card>
                                {productSyncSettingsComponent}
                            </Card>
                        </InlineGrid>
                        <InlineGrid columns={{ xs: "1fr", md: "2fr 5fr" }} gap="400">
                            <Box
                                as="section"
                                paddingInlineStart={{ xs: 400, sm: 0 }}
                                paddingInlineEnd={{ xs: 400, sm: 0 }}
                            >
                                <BlockStack gap="400">
                                    <Text as="h3" variant="headingMd">
                                        {t('settings.generalSettings.title')}
                                    </Text>
                                    <Text as="p" variant="bodyMd">
                                        {t('settings.generalSettings.description')}
                                    </Text>
                                </BlockStack>
                            </Box>
                            <Card>
                                {generalSettingsComponent}
                            </Card>
                        </InlineGrid>
                        <InlineGrid columns={{ xs: "1fr", md: "2fr 5fr" }} gap="400">
                            <Box
                                as="section"
                                paddingInlineStart={{ xs: 400, sm: 0 }}
                                paddingInlineEnd={{ xs: 400, sm: 0 }}
                            >
                                <BlockStack gap="400">
                                    <Text as="h3" variant="headingMd">
                                        {t('settings.currencySettings.title')}
                                    </Text>
                                    <Text as="p" variant="bodyMd">
                                        {t('settings.currencySettings.description')}
                                    </Text>
                                </BlockStack>
                            </Box>
                            <Card >
                                {currencySettingsComponent}
                            </Card>
                        </InlineGrid>
                        <LearnMoreLinks />
                    </BlockStack>
                </Box>
            </Page>
        </>)
}
