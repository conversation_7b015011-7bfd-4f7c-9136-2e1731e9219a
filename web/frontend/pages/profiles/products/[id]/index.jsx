import React, {useCallback, useContext, useEffect, useState, useMemo} from 'react';
import {
    BlockStack,
    Card,
    InlineStack,
    Page,
    Tag,
    Text,
} from "@shopify/polaris";
import {useLocation, useNavigate, useParams, useSearchParams} from "react-router-dom";
import {useAppQuery} from "../../../../hooks";
import ViewProfileSettingModal from "../../../../components/Modal/Profile/ViewProfileSettingModal.jsx";
import RemoveProductFromProfileModal from "../../../../components/Modal/Profile/RemoveProductFromProfileModal.jsx";
import SubProfileIndexTable from "../../../../components/Profile/SubProfile/SubProfileIndexTable.jsx";
import {t} from "i18next";
import InfoCardBanner from '../../../../components/Common/InfoCardBanner.jsx';
import ShopifyProductModal from "../../../../components/Modal/Profile/ShopifyProductModal.jsx";
import AppRatingBanner from "../../../../components/Common/AppRatingBanner.jsx";
import { TabsSkeletonComponent } from "../../../../components/Skeleton/TabsSkeletonComponent.jsx";
import {PusherInstance as Pusher} from "../../../../util/Pusher/index.jsx";
import EbayConnectionContext from "../../../../context/EbayConnection/EbayConnectionContext.jsx";
import RefreshTokenExpiredWarningBanner from "../../../../components/Banner/RefreshTokenExpiredWarningBanner.jsx";
import SmartIndexFilters from '../../../../components/Common/SmartIndexFilters.jsx';
import StatusFilter from '../../../../components/Common/Filter/StatusFilter.jsx';

export default function ProfileProducts() {
    const location = useLocation();
    const params = useParams()
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const [queryValue, setSearchQuery] = useState('');
    const [products, setProducts] = useState([]);
    const [pagination, setPagination] = useState({});
    const [profileName, setProfileName] = useState('')
    const [activeModal, setActiveModal] = useState(false);
    const [removeProductModal, setRemoveProductModal] = useState(false)
    const [removeProductModalItem, setRemoveProductModalItem] = useState(null)
    const [activeTab, setActiveTab] = useState('all')
    const [profileCollections, setProfileCollections] = useState([])
    const [isFirstRender, setIsFirstRender] = useState(true)
    const [addProductModalOpen, setAddProductModalOpen] = useState(false)
    const [appliedFilters, setAppliedFilters] = useState([])

    const[showAppRating,setShowAppRating] =  useState(false);
    const ebayConnectionContext = useContext(EbayConnectionContext)

    const backendURL = window.location.origin
    const profileProductsPath = '/api/ebay/profile/' + params.id
    const searchProductsPath = '/api/shopify/products?related_fields=variations,errors&profile=' + params.id
    const [url, setUrl] = useState('')

    //counts
    const [profileCounts, setProfileCounts] = useState({})
    const { data: ProfileData, isFetching: isProfileFetching, refetch: refetchProfile } = useAppQuery({
        url: profileProductsPath,
        reactQueryOptions: {
            onSuccess: (data) => {
                setProfileName(data.result.profile_name)
                setProfileCounts({
                    uploaded_products_count: data.result.uploaded_products,
                    failed_products_count: data.result.failed_products
                })
                setProfileCollections(data.result.collections)
            },
        },
    });


    useEffect(() => {
        const sessionId = ebayConnectionContext.shop.session_id
        const channel = Pusher.subscribe(sessionId)
        channel.bind('upload_to_ebay_completed-'+params.id,function (){
            setShowAppRating(true)
        })

        return (() => {
            Pusher.unsubscribe(sessionId)
        })
    }, [])

    useEffect(() => {
        if (location.state && location.state.removedProdsCount) {
            shopify.toast.show(`Removed ${location.state.removedProdsCount} product from profile`)
        }
        if (location.state && location.state.error === "refresh_token_expired"){
            ebayConnectionContext.setEbayConnection(
                { ...ebayConnectionContext.ebayConnection, refresh_token_expired: 1 }
            )
        }
    }, [])

    // Read URL parameters on initial load to set the correct tab
    useEffect(() => {
        const filterParam = searchParams.get('filter');
        if (filterParam && ['uploaded', 'failed'].includes(filterParam)) {
            setActiveTab(filterParam);
        }
    }, [searchParams])

    // updates the url if any key is pressed
    useEffect(() => {
        const newUrl = new URL(backendURL.concat(searchProductsPath))

        if (queryValue.length > 0) {
            newUrl.searchParams.set('search', queryValue)
        }
        if (activeTab && activeTab !== 'all') {
            refetchProfile()
            newUrl.searchParams.set('filter', activeTab)
        }
        setUrl(newUrl.href)
    }, [queryValue, activeTab])


    const {
        refetch, isFetching
    } = useAppQuery({
        url: `${url}`, reactQueryOptions: {
            onSuccess: (data) => {
                setProducts(data.result.products.data)
                setPagination({
                    current_page: data.result.products.current_page,
                    last_page: data.result.products.last_page,
                    from: data.result.products.from,
                    prevPageUrl: data.result.products.prev_page_url,
                    nextPageUrl: data.result.products.next_page_url,
                })
            },
            enabled: url.length !== 0
        },
    });

    const handleSettingsModal = useCallback(() => {
        setActiveModal(!activeModal)
    }, [activeModal])

    const handleRemoveProductModalChange = useCallback((item) => {
        setRemoveProductModalItem(item)
        setRemoveProductModal(!removeProductModal)
    }, [removeProductModal])


    const handleFiltersClearAll = useCallback(() => {
        setActiveTab('all')
    }, []);

    // Define views array for reuse with useMemo to prevent infinite loops
    const views = useMemo(() => [
     {
         tabName: t('common.all'),
         key: 'all',
     },
     {
         tabName: t('common.uploaded'),
         count: profileCounts.uploaded_products_count,
         key: 'uploaded',
     },
     {
         tabName: t('common.failed'),
         count: profileCounts.failed_products_count,
         key: 'failed',
     },
], [profileCounts.uploaded_products_count, profileCounts.failed_products_count]);

    // Build applied filters
    useEffect(() => {
        const filters = []
        // Status filter - only show when a specific status is selected (not 'all')
        if (activeTab !== 'all') {
            const currentView = views.find(view => view.key === activeTab);
            if (currentView) {
                const key = 'statusFilter';
                filters.push({
                    key,
                    label: t('common.statusFilter', { status: currentView.tabName }),
                    onRemove: () => setActiveTab('all'),
                });
            }
        }

        const sortedFilters = filters.sort((a, b) => a.key.localeCompare(b.key));
        const sortedAppliedFilters = appliedFilters.sort((a, b) => a.key.localeCompare(b.key));
        // Only update appliedFilters if the new filters are different
        const areFiltersEqual = JSON.stringify(sortedFilters) === JSON.stringify(sortedAppliedFilters);
        if (!areFiltersEqual) {
            setAppliedFilters(filters);
        }
    }, [activeTab]);

    useEffect(() => {
        if (isFirstRender && !isFetching && !isProfileFetching) {
            setIsFirstRender(false)
        }
        if(isFetching) {
            shopify.loading(true);
        }
        if(!isFetching) {
            shopify.loading(false);
        }
    }, [isFetching, isFirstRender, isProfileFetching])

    const filters =  [
        {
            key: 'statusFilter',
            label: t('common.status'),
            filter: (
                <StatusFilter
                    statusOptions={views.filter(view => view.key !== 'all').map(view => ({
                        label: view.tabName,
                        value: view.key,
                    }))}
                    selectedStatus={[activeTab]}
                    setStatus={setActiveTab}
                />
            ),
            pinned: true,
        },
    ];



    const profileCollectionsTagMarkup = profileCollections.map((collection) => (
        <Tag>
            {collection.title}
        </Tag>
    ));

    const handleBackAction = () => {
        navigate('/profiles')
    }

    return (
        <>
            
            <Page
                fullWidth
                title={t('profiles.viewProfilePageTitle', { profileName: profileName })}
                backAction={{
                    content: t('common.goBack'),
                    onAction: handleBackAction
                }}
                primaryAction={{
                    content: t('common.addProducts'),
                    onAction: () => setAddProductModalOpen(true)
                }}
                secondaryActions={[{
                    content: t('profiles.editProfile'),
                    onAction: () => navigate(`/profiles/${params.id}`)
                }]}
            >
            {addProductModalOpen &&
                <ShopifyProductModal profileId={params.id} setAddProductModalOpen={setAddProductModalOpen}/>}

            {showAppRating  && <div style={{"marginBottom": "15px"}}><AppRatingBanner/></div>}

                <div style={{"marginBottom": "15px"}}>
                <InfoCardBanner
                    message={t('profiles.productDetails')}
                />
            </div>

            {ebayConnectionContext.ebayConnection.refresh_token_expired ?
                <div className='spacer-bottom-1x'><RefreshTokenExpiredWarningBanner/></div>
                : ''
            }

            {activeModal ? <ViewProfileSettingModal
                open={activeModal}
                data={ProfileData.result}
                onClose={handleSettingsModal}
                id={params.id}/> : null}

            {removeProductModal ? <RemoveProductFromProfileModal
                open={removeProductModal}
                onClose={handleRemoveProductModalChange}
                item={removeProductModalItem}
                setRemoveProductModal={setRemoveProductModal}
                refetchProfile={refetch}
            /> : null
            }


            {profileCollections.length !== 0
                && <BlockStack gap="400">
                    <Card>
                        <Text as="h2" variant="headingMd">
                            {t("profiles.collectionsInThisProfile")}
                        </Text>
                        <div className='spacer-bottom-.5x'></div>
                        <InlineStack gap={'300'} blockAlign="center">{profileCollectionsTagMarkup}</InlineStack>
                    </Card>
                </BlockStack>
            }

            <div className='spacer-bottom-1x'></div>
            <Card padding={'0'}>
                {
                    isFirstRender ?
                    <TabsSkeletonComponent /> :
                        <>
                            <SmartIndexFilters
                                setSearchQuery={setSearchQuery}
                                loading={isFetching}
                                activeTab={activeTab}
                                setActiveTab={setActiveTab}
                                views={views}
                                filters={filters}
                                appliedFilters={appliedFilters}
                                searchPlaceHolder={t('common.searchPlaceholder')}
                                handleFiltersClearAll={handleFiltersClearAll}
                            />
                            <SubProfileIndexTable products={products}
                                handleRemoveProductModalChange={handleRemoveProductModalChange}
                                profile={ProfileData.result}
                                refetch={refetch}
                                activeTab={activeTab}
                                setActiveTab={setActiveTab}
                                queryValue={queryValue}
                                pagination={pagination}
                                setUrl={setUrl}
                            />
                        </>
                    
                }
            </Card>
            <div className='spacer-bottom-.5x'/>
            </Page>
        </>
    );
}

