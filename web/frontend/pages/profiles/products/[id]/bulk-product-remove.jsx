import { <PERSON>ton, ButtonGroup, Card, Divider, LegacyCard, Page } from "@shopify/polaris";
import BulkActionTable from "../../../../components/Product/Table/BulkActionTable.jsx";
import React, { useCallback, useEffect, useState } from "react";
import { useAuthenticatedFetch } from "../../../../hooks/index.js";
import { useLocation, useNavigate } from "react-router-dom";
import RemoveAndEndSelectedProducts from "../../../../components/Modal/Profile/RemoveAndEndSelectedProducts.jsx";
import { t } from "i18next";

export default function BulkProductRemove() {
    const fetch = useAuthenticatedFetch();
    const location = useLocation();
    const navigate = useNavigate()
    const [products, setProducts] = useState(location.state ? location.state.prods : []);
    const [toBeRemovedProductIds, setToBeRemovedProductIds] = useState([])
    const [loading, setLoading] = useState(false)
    const [removeSelectedProductsModal, setRemoveSelectedProductsModal] = useState(false)
    const profileId = location.state.profileId

    useEffect(() => {
        if (!products.length) {
            navigate(`/profiles/products/${profileId}`)
        }
    }, [products])

    // this is for accurate counting of product ids
    useEffect(() => {
        setToBeRemovedProductIds(products.map(a => a.id))
    }, [products])

    const performRemoveApiCall = async () => {
        const response = await fetch('/api/ebay/profile/remove-product', {
            method: 'POST',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 'shopify_product_ids': toBeRemovedProductIds })
        })
        return response.json()
    }

    const removeProductFromProfile = () => {
        setRemoveSelectedProductsModal(true)
        setLoading(true)
    }

    const handleDelete = (itemId) => {
        const finalArray = products.filter(function (item) {
            return item.id !== itemId;
        });
        setProducts(finalArray)
    }
    const handleRemoveProductModalChange = useCallback(() => {
        setLoading(false)
        setRemoveSelectedProductsModal(!removeSelectedProductsModal)
    }, [removeSelectedProductsModal])

    const handleBackAction = () => {
        navigate(`/profiles/products/${profileId}`)
    }

    return (
        <>
            <Page
                title={"Bulk remove products from profile"}
                backAction={{
                    content: t('common.goBack'),
                    onAction: handleBackAction
                }}
            >
                {removeSelectedProductsModal ? <RemoveAndEndSelectedProducts
                    open={removeSelectedProductsModal}
                    onClose={handleRemoveProductModalChange}
                    toBeRemovedProductIds={toBeRemovedProductIds}
                    profileId={location.state.profileId}
                /> : null
                }
                <Card padding={'0'}>
                    <BulkActionTable products={products} handleDelete={handleDelete} isShopify={true} />
                    <Divider></Divider>
                    <div className={'confirmation-page-action-bar'}>
                        <ButtonGroup>
                            <Button
                                onClick={removeProductFromProfile}
                                loading={loading}
                                size={"slim"}
                                variant="primary"
                                >Remove Selection</Button>
                            <Button onClick={() => setProducts([])} size={"slim"}>Cancel</Button>
                        </ButtonGroup>
                    </div>
                </Card>
            </Page>
        </>
    );
}
