import EbayProfileEdit from "../../../components/Profile/EbayProfileEdit.jsx";
import { Page } from "@shopify/polaris";
import { t } from "i18next";
import { useNavigate } from "react-router-dom";

export default function () {
    const navigate = useNavigate()
    const handleBackAction = () => {
        navigate('/profiles')
    };

    return (
        <>
            <Page
                title={t("profiles.editProfile")}
                backAction={{
                    content: t('common.goBack'),
                    onAction: handleBackAction
                }}
            >
                <EbayProfileEdit />
            </Page>
        </>
    )
}
