import { <PERSON><PERSON>, Bleed, <PERSON>Stack, Box, Button, Card, DataTable, Grid, Layout, Link, Page, Text } from "@shopify/polaris";
import '../assets/Custom.css';
import { useAppQuery } from "../hooks";
import { useNavigate } from "react-router-dom";
import React, { lazy, Suspense, useCallback, useContext, useState } from "react";
import EbayConnectionContext from "../context/EbayConnection/EbayConnectionContext.jsx";
import { t } from "i18next";
import ExtendTrialWarningBanner from "../components/Banner/ExtendTrialWarningBanner.jsx";
import RefreshTokenExpiredWarningBanner from "../components/Banner/RefreshTokenExpiredWarningBanner.jsx";
import DashboardWelcomeBanner from "../components/Banner/DashboardWelcomeBanner.jsx";
import SkeletonPageComponent from "../components/Skeleton/SkeletonPageComponent.jsx";
import SharedSkuCard from "../components/Dashboard/SharedSkuCard.jsx";
import NotOptimized from "../components/Dashboard/NotOptimized.jsx";
import ThingsToConsider from "../components/Dashboard/ThingsToConsider.jsx";
import WhatsNew from "../components/Dashboard/WhatsNew.jsx";
import AppRatingBanner from "../components/Common/AppRatingBanner.jsx";
import OrderSyncLimitReachedBanner from "../components/Banner/OrderSyncLimitReachedBanner.jsx";
import ChangeEbayStoreModal from "../components/Modal/Dashboard/ChangeEbayStoreModal.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function Index() {
    const showRatingBanner = sessionStorage.getItem('showRatingBanner') === 'true'
    const ebayConnectionContext = useContext(EbayConnectionContext)
    const [showChangeEbayStoreModal, setShowChangeEbayStoreModal] = useState(false);

    const subscribedPackagePrice = ebayConnectionContext.shop.subscribed_package_price ? t('subscription.pricePerMonth', { price: ebayConnectionContext.shop.subscribed_package_price }) :
        t('subscription.pricePerMonth', { price: 19.99 });

    const sessionLimit = ebayConnectionContext?.shop?.limit;
    const planName = ebayConnectionContext?.shop?.subscribed_package;
    const usedOrderSyncLimit = sessionLimit?.used_order_sync_limit ?? 0;
    const totalOrderSyncLimit = sessionLimit?.order_sync_limit ?? 0;

    const getLimitMessage = (limit) => {
        if (limit === -1 || limit === undefined || limit === null) {
            return t('subscription.unlimited');
        }
        return t('subscription.productLimitPerMonth', { count: limit });
    };
    const getOrderSyncLimitMessage = (limit) => {
        if (limit === -1 || limit === undefined || limit === null) {
            return t('subscription.unlimited');
        }
        return t('dashboard.orderSyncLimit.message', {
            used: usedOrderSyncLimit,
            total: totalOrderSyncLimit,
        });
    }

    const productImportLimit = getLimitMessage(sessionLimit?.product_import_limit);
    const productUploadLimit = getLimitMessage(sessionLimit?.product_upload_limit);

    let shopifySubscription = [
        [t('dashboard.planName'), planName],
        [t('dashboard.subscriptionPrice'), subscribedPackagePrice],
        [t('dashboard.productImportLimit'), productImportLimit],
        [t('dashboard.productUploadLimit'), productUploadLimit],
    ];

    if (sessionLimit) {
        shopifySubscription.push([
            t('dashboard.orderSyncLimit.title'),
            <Text as="p" variant="bodyMd" tone={
                (totalOrderSyncLimit !== -1 && usedOrderSyncLimit >= totalOrderSyncLimit) ? "critical" : "inherit"}>
                {getOrderSyncLimitMessage(totalOrderSyncLimit)}
            </Text>
        ]);
    }
    if (ebayConnectionContext.shop.trial_remaining_days && ebayConnectionContext.shop.trial_remaining_days > 0) {
        shopifySubscription.push([t('dashboard.trialEndsIn'), t('dashboard.remainingDay', { count: ebayConnectionContext.shop.trial_remaining_days })]);
    }

    const navigate = useNavigate();
    const { data, isLoading } = useAppQuery({
        url: `/api/account_info`,
        reactQueryOptions: {
            onSuccess: (data) => {
                if (!data.success) {
                    ebayConnectionContext.setEbayConnection(null)
                    ebayConnectionContext.setEbayUserSettings(null)
                    navigate('/welcome')
                }
                if (data.result.trial_ends_on > 0) {
                    setShopifySubscription((prevArray) => [...prevArray, [t('dashboard.trialEndsIn'), t('dashboard.remainingDay', { count: data.result.trial_ends_on })]])
                }
            },
        }
    });

    if (isLoading) {
        return <SkeletonPageComponent />
    }
    const rows3 = [
        [t('dashboard.eBayStore'), data.result.ebay_user],
        [t('dashboard.eBayStoreStatus'), <Badge tone="success">{t("dashboard.connected")}</Badge>],
        [t('dashboard.eBaySite'), data.result.site],
        [t('common.action'), <Button
            onClick={() => setShowChangeEbayStoreModal(true)}
            size={"slim"}
            variant="plain">{t('dashboard.changeYourEbayStore')}</Button>],
    ];

    const renderBanner = () => {
        const isRefreshTokenExpired = !!ebayConnectionContext.ebayConnection.refresh_token_expired
        const orderSyncLimitReached = usedOrderSyncLimit > 0 && totalOrderSyncLimit > 0 && usedOrderSyncLimit >= totalOrderSyncLimit
        if (
            !isRefreshTokenExpired &&
            !orderSyncLimitReached &&
            !ebayConnectionContext.shop.optimize_status &&
            ebayConnectionContext.shop.show_not_optimized_banner
        ) {
            return <NotOptimized />
        }
        return (
            <>
                {isRefreshTokenExpired && <RefreshTokenExpiredWarningBanner />}
                {orderSyncLimitReached && (
                    <OrderSyncLimitReachedBanner
                        currentLimit={usedOrderSyncLimit}
                        totalLimit={totalOrderSyncLimit}
                    />
                )}
            </>
        )

    }

    return (
        <Page>
            <TitleBar title={t("dashboard.dashboard")}/>
            {showChangeEbayStoreModal && <ChangeEbayStoreModal onClose={() => setShowChangeEbayStoreModal(false)} />}
            <Layout>
                <Layout.Section>
                    {showRatingBanner && <div className='spacer-bottom-1x'><AppRatingBanner /></div>}
                    <DashboardWelcomeBanner shop={data.result.name}></DashboardWelcomeBanner>
                    {ebayConnectionContext.shop.show_extend_trial ? <ExtendTrialWarningBanner /> : <></>}
                    {renderBanner()}
                </Layout.Section>
                <SharedSkuCard />
                <Layout.Section>

                    <Grid>
                        <Grid.Cell columnSpan={{ xs: 6, sm: 12, md: 12, lg: 6, xl: 6 }}>

                            <BlockStack gap="500">
                                <Card>
                                    <Text as="h2" variant="headingMd">
                                        {t("dashboard.productsOverview")}
                                    </Text>
                                    <div className="flex">
                                        <div className="col">
                                            <div className="dash-overview-item">
                                                <div className="dash-overview-number text-grey">
                                                    {data.result.total_products}</div>
                                                <div
                                                    className="dash-overview-label">
                                                    <Link removeUnderline monochrome
                                                        onClick={() => navigate('/ebay/products')}>
                                                        <b>
                                                            {t("dashboard.totalEbayProducts")}
                                                        </b>
                                                    </Link>

                                                </div>
                                            </div>
                                        </div>
                                        <div className="col">
                                            <div className="dash-overview-item">
                                                <div className="dash-overview-number text-red">
                                                    {data.result.unlinked_products}</div>
                                                <div className="dash-overview-label">
                                                    <Link removeUnderline monochrome
                                                        onClick={() => navigate('/ebay/products?filter=not_imported')}>
                                                        <b>
                                                            {t("common.notImported")}
                                                        </b>
                                                    </Link>
                                                </div>
                                            </div>
                                        </div>
                                        <div className="col">
                                            <div className="dash-overview-item">
                                                <div className="dash-overview-number text-green">
                                                    {data.result.linked_products}</div>
                                                <div
                                                    className="dash-overview-label">
                                                    <Link removeUnderline monochrome
                                                        onClick={() => navigate('/ebay/products?filter=imported')}>
                                                        <b>
                                                            {t("common.importedToShopify")}
                                                        </b>
                                                    </Link>
                                                </div>
                                            </div>
                                        </div>

                                    </div>
                                </Card>
                                <WhatsNew />
                                <ThingsToConsider data={data} />

                            </BlockStack>

                        </Grid.Cell>
                        <Grid.Cell columnSpan={{ xs: 6, sm: 12, md: 12, lg: 6, xl: 6 }}>
                            <Box paddingBlockEnd={'400'}>
                                <BlockStack gap="500">
                                    <Card>
                                        <Text as="h2" variant="headingMd">
                                            {t("dashboard.subscriptionInfo")}
                                        </Text>
                                        <Bleed marginInlineStart="300">
                                            <DataTable
                                                increasedTableDensity="yes"
                                                columnContentTypes={[
                                                    'text',
                                                    'numeric',
                                                ]}
                                                headings={[
                                                    '',
                                                    '',
                                                ]}
                                                rows={shopifySubscription}
                                            />
                                        </Bleed>
                                    </Card>
                                    <Card>
                                        <Text as="h2" variant="headingMd">
                                            {t("dashboard.eBayInfo")}
                                        </Text>
                                        <Bleed marginInlineStart="300">
                                            <DataTable
                                                increasedTableDensity="yes"
                                                columnContentTypes={[
                                                    'text',
                                                    'numeric',
                                                ]}
                                                headings={[
                                                    '',
                                                    '',
                                                ]}
                                                rows={rows3}
                                            />
                                        </Bleed>
                                    </Card>
                                </BlockStack>
                            </Box>
                        </Grid.Cell>
                    </Grid>
                </Layout.Section>
            </Layout>
        </Page>
    );
}
