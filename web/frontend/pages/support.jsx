import React, { useEffect } from "react";
import {
    Page,
    Layout,
    Card,
    Text,
    BlockStack,
    InlineStack,
    Button,
    Icon,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import {
    QuestionCircleIcon,
    EmailIcon,
    ChatIcon,
} from "@shopify/polaris-icons";
import { t } from "i18next";
import { Crisp } from "crisp-sdk-web";
import { useSupportChat } from "../components/Support";

/**
 * Support Page Component
 *
 * This page serves as the landing page for support requests.
 * It automatically opens Crisp chat when accessed and provides fallback options.
 */
export default function SupportPage() {
    const { openSupport } = useSupportChat();

    // Automatically open Crisp chat when the page loads
    useEffect(() => {
        // Small delay to ensure the page has loaded
        const timer = setTimeout(() => {
            openSupport();
        }, 500);

        return () => clearTimeout(timer);
    }, [openSupport]);

    const handleEmailSupport = () => {
        window.location.href =
            "mailto:<EMAIL>?subject=Support Request - eBay Integration & Sync";
    };

    const handleOpenChat = () => {
        openSupport();
    };

    return (
        <>
            <TitleBar title={t("support.title", "Get Support")} />
            <Page>
                <Layout>
                    <Layout.Section>
                        <Card>
                            <BlockStack gap="400">
                                <InlineStack align="center">
                                    <Icon source={QuestionCircleIcon} tone="base" />
                                    <Text variant="headingLg" as="h1">
                                        {t("support.heading", "How can we help you?")}
                                    </Text>
                                </InlineStack>

                                <Text variant="bodyMd" as="p" tone="subdued">
                                    {t(
                                        "support.description",
                                        "We're here to help! Our support team is ready to assist you with any questions or issues you may have with the eBay Integration & Sync app."
                                    )}
                                </Text>

                                <BlockStack gap="300">
                                    <Text variant="headingMd" as="h2">
                                        {t("support.contactOptions", "Contact Options")}
                                    </Text>

                                    <InlineStack gap="300" wrap={false}>
                                        <Button
                                            variant="primary"
                                            size="large"
                                            icon={ChatIcon}
                                            onClick={handleOpenChat}
                                        >
                                            {t("support.openChat", "Open Live Chat")}
                                        </Button>

                                        <Button
                                            variant="secondary"
                                            size="large"
                                            icon={EmailIcon}
                                            onClick={handleEmailSupport}
                                        >
                                            {t("support.emailSupport", "Email Support")}
                                        </Button>
                                    </InlineStack>
                                </BlockStack>

                                <Card background="bg-surface-secondary">
                                    <BlockStack gap="200">
                                        <Text variant="headingMd" as="h3">
                                            {t("support.chatInfo.title", "Live Chat Support")}
                                        </Text>
                                        <Text variant="bodyMd" as="p">
                                            {t(
                                                "support.chatInfo.description",
                                                "Get instant help through our live chat. Our support team is available to answer your questions and help resolve any issues quickly."
                                            )}
                                        </Text>
                                        <Text variant="bodySm" as="p" tone="subdued">
                                            {t(
                                                "support.chatInfo.availability",
                                                "Chat support is available during business hours (9 AM - 6 PM EST, Monday - Friday)"
                                            )}
                                        </Text>
                                    </BlockStack>
                                </Card>

                                <Card background="bg-surface-secondary">
                                    <BlockStack gap="200">
                                        <Text variant="headingMd" as="h3">
                                            {t('support.emailInfo.title', 'Email Support')}
                                        </Text>
                                        <Text variant="bodyMd" as="p">
                                            {t('support.emailInfo.description', 
                                                'Send us an <NAME_EMAIL> and we\'ll get back to you within 24 hours.'
                                            )}
                                        </Text>
                                        <Text variant="bodySm" as="p" tone="subdued">
                                            {t('support.emailInfo.responseTime', 
                                                'We typically respond to emails within 2-4 hours during business hours.'
                                            )}
                                        </Text>
                                    </BlockStack>
                                </Card>

                                <Text variant="bodySm" as="p" tone="subdued" alignment="center">
                                    {t('support.footer', 
                                        'For urgent issues, please use the live chat option for the fastest response.'
                                    )}
                                </Text>
                            </BlockStack>
                        </Card>
                    </Layout.Section>
                </Layout>
            </Page>
        </>
    );
}
