import React, { useContext } from "react";
import EbayConnectionContext from "../../context/EbayConnection/EbayConnectionContext.jsx";
import InventoryEmptyState from "../../components/Inventory/InventoryEmptyState.jsx";
import InventoryListPanel from "../../components/Inventory/InventoryListPanel.jsx";

export default function Inventory() {
    const ebayConnectionContext = useContext(EbayConnectionContext)
    
    const inventorySettingDisabled = ebayConnectionContext.ebayUserSettings && !Boolean(ebayConnectionContext.ebayUserSettings.inventory_sync);

    if (inventorySettingDisabled) {
        return <InventoryEmptyState />
    }

    return <InventoryListPanel />
}
