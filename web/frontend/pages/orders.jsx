import { BlockStack, <PERSON> } from "@shopify/polaris";
import OrdersBanner from "../components/Banner/OrdersBanner.jsx";
import React, { useContext } from "react";
import { t } from "i18next";
import EbayConnectionContext from "../context/EbayConnection/EbayConnectionContext.jsx";
import OrderSyncDisabledEmptyState from "../components/Orders/OrderSyncDisabledEmptyState.jsx";
import LearnMoreLinks from "../util/LearnMoreLinks.jsx";
import OrderListPanel from "../components/Orders/OrderListPanel.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function Orders() {
    const ebayConnectionContext = useContext(EbayConnectionContext)
    const orderSyncDisabled = !ebayConnectionContext.ebayUserSettings.order_sync
    return (
        <>
            <TitleBar title={t("common.orders")} />
            <Page>
                <BlockStack gap="400">
                    <OrdersBanner />
                    {
                        orderSyncDisabled ?
                            <OrderSyncDisabledEmptyState /> :
                            <OrderListPanel />
                    }
                    <LearnMoreLinks />
                </BlockStack>
            </Page>
        </>
    )
}
