import {
    Legacy<PERSON><PERSON>,
    Page,
    Layout,
    Icon,
    Text
} from "@shopify/polaris";
import '../assets/Custom.css';
import {
    UploadIcon, StoreImportIcon, OrderIcon, InventoryIcon
} from '@shopify/polaris-icons';
import {t} from "i18next";
import {useNavigate} from "react-router-dom";
import {useContext} from "react";
import EbayConnectionContext from "../context/EbayConnection/EbayConnectionContext.jsx";
import {useAuthenticatedFetch} from "../hooks/index.js";
import { TitleBar } from "@shopify/app-bridge-react";

export default function Navigator() {
    const navigate = useNavigate();
    const fetch = useAuthenticatedFetch()
    const ebayConnectionContext = useContext(EbayConnectionContext)
    const handleRedirect = async (location) => {
        await fetch("/api/shopify/update-navigator", {
            method: "PUT",
            body: JSON.stringify({}),
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        navigate(location)
        ebayConnectionContext.setNavigatorCompletedStatus(true)
    }

    return (
        <>
            <TitleBar title={t('common.welcome')} />
            <Page>
                <div className="spacer-bottom-2x">
                    <Text variant="headingLg" as="p">{t('navigator.heading')}</Text>
                </div>
            <Layout>
                <Layout.Section variant="oneHalf">
                        <LegacyCard sectioned>
                            <div className="flex">
                                <div className="col-auto icon-aqua icon-2x">
                                    <Icon source={StoreImportIcon}/>
                                </div>
                                <div className="col">
                                    <div className="spacer-bottom-.5x">
                                        <a href="#" onClick={() => {
                                            handleRedirect('/ebay/products')
                                        }}
                                           style={{"textDecoration": "none"}}
                                        >
                                            <Text variant="headingMd" as={"h2"}
                                                  tone="base">{t('navigator.exportProductToShopify')}</Text>
                                        </a>
                                    </div>
                                    <p>{t('navigator.exportProductToShopifyDescription')}</p>
                                </div>
                            </div>
                        </LegacyCard>
                </Layout.Section>

                <Layout.Section variant="oneHalf">
                        <LegacyCard sectioned>
                            <div className="flex">
                                <div className="col-auto icon-light-green icon-2x">
                                    <Icon source={UploadIcon}/>
                                </div>
                                <div className="col">
                                    <div className="spacer-bottom-.5x">
                                        <a href="#" onClick={() => {
                                            handleRedirect('/shopify/products')
                                        }}
                                           style={{"textDecoration": "none"}}
                                        >
                                            <Text variant="headingMd" as={'h2'}
                                                  tone="base">{t('navigator.createProductToEbay')}</Text>
                                        </a>
                                    </div>
                                    <p>{t('navigator.createProductToEbayDescription')}</p>
                                </div>
                            </div>
                        </LegacyCard>
                </Layout.Section>
                <Layout.Section variant="oneHalf">
                        <LegacyCard sectioned>
                            <div className="flex">
                                <div className="col-auto icon-blue icon-2x">
                                    <Icon source={InventoryIcon}/>
                                </div>
                                <div className="col">
                                    <div className="spacer-bottom-.5x">
                                        <a href="#" onClick={() => {
                                            handleRedirect('/inventory')
                                        }}
                                           style={{"textDecoration": "none"}}
                                        >
                                            <Text variant="headingMd" as={"h2"}
                                                  tone="base">{t('navigator.manageInventories')}</Text>
                                        </a>
                                    </div>
                                    <p>{t('navigator.manageInventoriesDescription')}</p>
                                </div>
                            </div>
                        </LegacyCard>
                </Layout.Section>
                <Layout.Section variant="oneHalf">
                        <LegacyCard sectioned>
                            <div className="flex">
                                <div className="col-auto icon-blue icon-2x">
                                    <Icon source={OrderIcon}/>
                                </div>
                                <div className="col">
                                    <div className="spacer-bottom-.5x">
                                        <a href="#" onClick={() => {
                                            handleRedirect('/orders')
                                        }}
                                           style={{"textDecoration": "none"}}
                                        >
                                            <Text variant="headingMd" as={"h2"}
                                                  tone="base">{t('navigator.manageOrders')}</Text>
                                        </a>
                                    </div>
                                    <p>{t('navigator.manageOrdersDescription')}</p>
                                </div>
                            </div>
                        </LegacyCard>
                </Layout.Section>
            </Layout>
            </Page>
        </>
    );
}
