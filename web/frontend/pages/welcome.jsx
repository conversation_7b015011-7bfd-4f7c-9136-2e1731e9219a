import {
    Page,
    Text,
    ProgressBar,
    But<PERSON>,
    Form,
    FormLayout,
    Divider,
    LegacyCard
} from "@shopify/polaris";
import {useNavigate} from 'react-router-dom';
import '../assets/Custom.css';

import React, {useCallback, useContext, useMemo, useRef, useState} from 'react';
import {useAppQuery, useAuthenticatedFetch} from "../hooks";
import EbayConnectionContext from "../context/EbayConnection/EbayConnectionContext";
import OnboardingOrderAndInventory from "../components/Onboarding/OnboardingOrderAndInventory.jsx";
import OnboardingEbayConnection from "../components/Onboarding/OnboardingEbayConnection.jsx";
import OnBoardingCurrencySettings from "../components/Onboarding/OnBoardingCurrencySettings/index.jsx";
import {t} from "i18next";
import ExtendTrialWarningBanner from "../components/Banner/ExtendTrialWarningBanner.jsx";
import { TitleBar } from "@shopify/app-bridge-react";

export default function Welcome() {
    const fetch = useAuthenticatedFetch()
    const navigate = useNavigate();
    const [isEbayConnected, updateEbayConnectionStatus] = useState(false);
    const [ebayConnectionLoading, updateEbayConnectionLoadingStatus] = useState(true);
    const ebayConnectionContext = useContext(EbayConnectionContext)
    const stepsRef = useRef(null);
    const [loading, setLoading] = useState(false)

    //configurations
    const [ebaySettings, setEbaySettings] = useState({orderAndInventorySync:['order-and-inventory']})
    const [currencyConversion, setCurrencyConversion] = useState({
        ebay_currency_value: '0'
    })

    //collapsible from the parent level
    const [activeCollapsibleId, setActiveCollapsibleId] = useState(null)

    const [ebaySettingsCollapsibleStatus, setEbaySettingsCollapsibleStatus] = useState(false)
    const [currencyCollapsibleStatus, setCurrencyCollapsibleStatus] = useState(false)

    //tasks
    const [completedTasks, setCompletedTasks] = useState({});
    const tasks = [
        {id: 1, name: 'eBay Connection'},
        {id: 2, name: 'eBay Configurations'},
        {id: 3, name: 'Currency Conversion'}
    ];


    useAppQuery({
        url: "/api/ebay/connection",
        reactQueryOptions: {
            onSuccess: (data) => {
                if (data.result && data.result.shop && data.result.shop.ebay_user) {
                    ebayConnectionContext.setEbayConnection(data.result.shop.ebay_user)
                    updateEbayConnectionLoadingStatus(false)
                    updateEbayConnectionStatus(true);
                    ebayConnectionContext.setShop(data.result.shop)

                    if (data.result.shop.ebay_user.user_setting) {
                        ebayConnectionContext.setEbayUserSettings(data.result.shop.ebay_user.user_setting)
                        navigate('/')
                    }
                } else if (data.result) {
                    ebayConnectionContext.setEbayConnection(null)
                    ebayConnectionContext.setShop(data.result.shop)
                }
                updateEbayConnectionLoadingStatus(false);
            },
        }
    });

    const compareCurrencyDifference = useMemo(() => {
        if (!ebayConnectionContext || !ebayConnectionContext.ebayConnection || !ebayConnectionContext.ebayConnection.currency || !ebayConnectionContext.shop.currency) {
            return false
        }
        const compareValue = ebayConnectionContext.ebayConnection.currency.localeCompare(ebayConnectionContext.shop.currency)
        return compareValue !== 0;
    }, [ebayConnectionContext])

    const totalStepsCount = stepsRef.current &&
        Object.values(stepsRef.current.children).filter(
            li => li.innerText
        ).length || 3

    const increaseCompletedStepCount = (taskId) => {
        const task = tasks.find((t) => t.id === taskId); // find the task with the given taskId

        if (task && !completedTasks[taskId]) { // check if task exists and has not been completed already
            setCompletedTasks((prevCompletedTasks) => ({
                ...prevCompletedTasks,
                [taskId]: true,
            }));
        }
    }

    const tasksCompletedNum = useMemo(() => {
        return Object.values(completedTasks).filter((completed) => completed).length
    }, [completedTasks])

    const performCreateApiCall = async () => {

        const response = await fetch('api/create-settings', {
            method: "POST",
            body: JSON.stringify({
                ebaySettings, currencyConversion
            }),
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        setLoading(false)

        return response.json()
    }

    const handleOnboardingSubmit = useCallback(() => {
        setLoading(true)
        performCreateApiCall().then(res => {
            ebayConnectionContext.setEbayUserSettings(res.result)
            navigate('/navigator')
        })

    }, [ebaySettings, currencyConversion])

    return (
        <>
            <TitleBar title={t('common.welcomeToDpl')} />
            <Page>
                {(ebayConnectionContext.shop && ebayConnectionContext.shop.show_extend_trial) ? <ExtendTrialWarningBanner/> : <></>}
            <LegacyCard>
                <LegacyCard.Section>
                    <Text variant="headingMd" as="h2">{t('onboarding.title')}</Text>
                    <div style={{
                        display: 'flex',
                        gap: '20px',
                        alignItems: 'center',
                        whiteSpace: 'nowrap',
                        marginTop: '10px'
                    }}>
                        <p>
                            {t('common.progressCompletedCount', {
                                completedCount: tasksCompletedNum,
                                totalCount: totalStepsCount
                            })}
                        </p>
                        <ProgressBar progress={tasksCompletedNum / totalStepsCount * 100} size="small"/>
                    </div>
                </LegacyCard.Section>

                <LegacyCard.Section>
                    <Form onSubmit={handleOnboardingSubmit}>
                        <FormLayout>
                            <ul className="steps-vertical" ref={stepsRef}>
                                <OnboardingEbayConnection isEbayConnected={isEbayConnected}
                                                          ebayConnectionLoading={ebayConnectionLoading}
                                                          updateEbayConnectionLoadingStatus={updateEbayConnectionLoadingStatus}
                                                          onCompleteStep={increaseCompletedStepCount}
                                                          setEbaySettingsCollapsibleStatus={setEbaySettingsCollapsibleStatus}
                                                          setActiveCollapsibleId={setActiveCollapsibleId}
                                />

                                <OnboardingOrderAndInventory isEbayConnected={isEbayConnected}
                                                             ebaySettingsCollapsibleStatus={ebaySettingsCollapsibleStatus}
                                                             setEbaySettingsCollapsibleStatus={setEbaySettingsCollapsibleStatus}
                                                             onCompleteStep={increaseCompletedStepCount}
                                                             ebaySettings={ebaySettings}
                                                             setEbaySettings={setEbaySettings}
                                                             setCurrencyCollapsibleStatus={setCurrencyCollapsibleStatus}
                                                             activeCollapsibleId={activeCollapsibleId}
                                                             setActiveCollapsibleId={setActiveCollapsibleId}
                                />

                                {compareCurrencyDifference && <OnBoardingCurrencySettings
                                    currencyConversion={currencyConversion}
                                    setCurrencyConversion={setCurrencyConversion}
                                    onCompleteStep={increaseCompletedStepCount}
                                    currencyCollapsibleStatus={currencyCollapsibleStatus}
                                    setCurrencyCollapsibleStatus={setCurrencyCollapsibleStatus}
                                    activeCollapsibleId={activeCollapsibleId}
                                    setActiveCollapsibleId={setActiveCollapsibleId}
                                />}
                            </ul>

                            <Divider/>

                            <div style={{marginTop: '20px'}}>
                                <Button
                                    submit
                                    loading={loading}
                                    disabled={tasksCompletedNum !== totalStepsCount}
                                    variant="primary">{t('common.proceed')}</Button>
                            </div>
                        </FormLayout>
                    </Form>
                </LegacyCard.Section>
            </LegacyCard>
            </Page>
        </>
    );
}
