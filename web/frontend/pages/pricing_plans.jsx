import {Grid, Layout, Page} from "@shopify/polaris";
import {t} from 'i18next';
import {useAppQuery} from "../hooks/index.js";
import React, {useState} from "react";
import {PlanOptionCard} from "../components/Subscription/PlanOptionCard.jsx";
import {PricingSkeleton} from "../components/Skeleton/Subscription/PricingSkeleton.jsx";
import InfoCardBanner from "../components/Common/InfoCardBanner.jsx";
import * as DateUtility from "../util/DateUtility.js";
import { TitleBar } from "@shopify/app-bridge-react";

export default function Pricing() {
    const [plans, setPlans] = useState([]);
    const [hasPayment, setHasPayment] = useState(false);
    const [activePlanId, setActivePlanId] = useState(null);
    const [selectedPlanId, setSelectedPlanId] = useState(null);
    const trialDays = 7;
    const trialEndedDate = DateUtility.addDays(new Date(), trialDays);

    const {isLoading: isBillingGettingChecked, data} = useAppQuery({
        url: `/api/billing/check`,
        reactQueryOptions: {
            onSuccess: (res) => {
                const activePlans = res?.result?.plans;
                if (plans) {
                    setPlans(activePlans)
                }
                if (res?.result?.subscribedPlanId) {
                    setActivePlanId(res.result.subscribedPlanId)
                }

                if (res?.result?.hasPayment) {
                    setHasPayment(true);
                }
            }
        }
    });

    if (isBillingGettingChecked) {
        return <PricingSkeleton/>
    }

    const renderPlans = () => {
        if (!plans) {
            return <></>
        }

        return plans.map((plan) => (
            <Grid.Cell key={plan.id}>
                <PlanOptionCard hasPayment={hasPayment}
                                plan={plan}
                                activePlanId={activePlanId}
                                selectedPlanId={selectedPlanId}
                                setSelectedPlanId={setSelectedPlanId}
                />
            </Grid.Cell>
        ));
    };


    return (
        <>
            <TitleBar title={t("common.pricing")} />
            <Page>
                <Layout>
                    <Layout.Section>

                        {
                            !hasPayment ?
                            <InfoCardBanner message={t('subscription.banner.body',
                                {
                                    trialEndedDate: DateUtility.humanReadableDate(trialEndedDate),
                                    trialDays: trialDays
                                })
                            }/> :
                            <InfoCardBanner message= {t('subscription.planDetail', {
                                plan_name: data?.result?.subscribedPackage,
                                plan_price: data?.result?.subscribedPackagePrice
                            })}/>
                        }
                    </Layout.Section>

                    <Layout.Section>
                        <Grid columns={{xs: 1, sm: 1, md: 1, lg: 2}}>
                            {renderPlans()}
                        </Grid>
                    </Layout.Section>
                </Layout>
            </Page>
        </>
    );
}

