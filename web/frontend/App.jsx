import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { NavMenu } from "@shopify/app-bridge-react";
import Routes from "./Routes";

import { PolarisProvider, QueryProvider } from "./components";
import { Suspense } from "react";
import { Loading } from "@shopify/polaris";
import { t } from "i18next";
import { Crisp } from "crisp-sdk-web";
import { SupportManager } from "./components/Support";

export default function App() {
    // Initialize Crisp chat in both development and production
    if (import.meta.env.VITE_CRISP_ID) {
        Crisp.configure(import.meta.env.VITE_CRISP_ID);
        Crisp.load();
    }

    // Any .tsx or .jsx files in /pages will become a route
    // See documentation for <Routes /> for more info

    const pages = import.meta.glob("./pages/**/!(*.test.[jt]sx)*.([jt]sx)");

    return (
        <PolarisProvider>
            <BrowserRouter>
                <QueryProvider>
                    <SupportManager />
                    <NavMenu>
                        <a href="/" rel="home"></a>
                        <a href="/ebay/products">{t("common.ebayProducts")}</a>
                        <a href="/shopify/products">
                            {t("common.shopifyProducts")}
                        </a>
                        <a href="/orders">{t("common.orders")}</a>
                        <a href="/inventory">{t("common.linkedInventory")}</a>
                        <a href="/profiles">{t("common.profiles")}</a>
                        <a href="/pricing_plans">{t("common.pricing")}</a>
                        <a href="/settings">{t("common.settings")}</a>
                        <a href="/faq">{t("common.FAQ")}</a>
                        <a href="/support">
                            {t("common.getSupport", "Get Support")}
                        </a>
                    </NavMenu>

                    <Suspense fallback={<Loading />}>
                        <Routes pages={pages} />
                    </Suspense>
                </QueryProvider>
            </BrowserRouter>
        </PolarisProvider>
    );
}
