import React from "react";
import { Box, Text, Link } from "@shopify/polaris";
import { helpScoutArticles } from "./HelpscoutArticles.js";
import { t } from "i18next";
import { Trans } from "react-i18next";
import { useLocation } from "react-router-dom";

/**
 * LearnMoreLinks Component
 * 
 * This component displays context-appropriate "Learn More" links based on the current route.
 * It supports both static routes (e.g., /profiles) and dynamic routes (e.g., /profiles/123).
 * 
 * The component uses pattern matching with regular expressions to identify the current
 * route and display the appropriate help documentation link.
 */
export default function LearnMoreLinks() {
    const location = useLocation();
    const currentPath = location.pathname;

    // Route patterns configuration with support for dynamic segments
    const routePatterns = [
        {
            pattern: /^\/ebay\/products$/,
            learnMoreUrl: helpScoutArticles.products.ebay.ebayMainPageLearnMoreURL,
            titleKey: "common.ebayProducts"
        },
        {
            pattern: /^\/shopify\/products$/,
            learnMoreUrl: helpScoutArticles.products.shopify.shopifyMainPageLearnMoreURL,
            titleKey: "common.shopifyProducts"
        },
        {
            pattern: /^\/shopify\/products\/collections$/,
            learnMoreUrl: helpScoutArticles.products.shopify.shopifyCollectionLearnMoreURL,
            titleKey: "shopifyCollections.shopifyCollections"
        },
        {
            pattern: /^\/shopify\/products\/select-profile$/,
            learnMoreUrl: helpScoutArticles.products.shopify.shopifyCollectionLearnMoreURL,
            titleKey: "shopifyCollections.shopifyCollections"
        },
        {
            pattern: /^\/orders$/,
            learnMoreUrl: helpScoutArticles.orders.orderMainPageLearnMoreURL,
            titleKey: "common.orders"
        },
        {
            pattern: /^\/inventory$/,
            learnMoreUrl: helpScoutArticles.inventory.inventoryMainPageLearnMoreURL,
            titleKey: "common.linkedInventory"
        },
        {
            pattern: /^\/profiles$/,
            learnMoreUrl: helpScoutArticles.profile.profileMainPageLearnMoreURL,
            titleKey: "common.profiles"
        },
        {
            // Profile edit pages: /profiles/123
            pattern: /^\/profiles\/\d+$/,
            learnMoreUrl: helpScoutArticles.profile.updateProfileURL,
            titleKey: "common.profiles"
        },
        {
            // Profile products pages: /profiles/products/123
            pattern: /^\/profiles\/products\/\d+$/,
            learnMoreUrl: helpScoutArticles.profile.profileMainPageLearnMoreURL,
            titleKey: "common.profiles"
        },
        {
            pattern: /^\/settings$/,
            learnMoreUrl: helpScoutArticles.settings.settingsMainPageLearnMoreURL,
            titleKey: "common.settings"
        }
    ];

    /**
     * Find the matching route pattern for the current path
     * @param {string} path - Current pathname
     * @returns {object|null} - Matching route configuration or null
     */
    const findMatchingRoute = (path) => {
        return routePatterns.find(route => route.pattern.test(path));
    };

    /**
     * Get learn more link configuration for the current path
     * @returns {object|null} - Link configuration or null if no match
     */
    const getLearnMoreConfig = () => {
        const matchingRoute = findMatchingRoute(currentPath);
        
        if (!matchingRoute) {
            return null;
        }

        return {
            url: matchingRoute.learnMoreUrl,
            title: t(matchingRoute.titleKey)
        };
    };

    const learnMoreConfig = getLearnMoreConfig();

    // Don't render if no matching route found
    if (!learnMoreConfig) {
        return null;
    }

    return (
        <Box paddingBlockEnd="200">
            <Text as="p" variant="bodyMd" alignment="center">
                <Trans
                    i18nKey="common.learnMoreWithLink"
                    values={{ linkTitle: learnMoreConfig.title }}
                    components={[
                        <Link 
                            target="_blank"
                            url={learnMoreConfig.url} 
                        />
                    ]}
                >
                </Trans>
            </Text>
        </Box>
    );
}
