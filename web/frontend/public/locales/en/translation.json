{"translation": {"activities": {"bulkProductUploadToShopify": {"one": "Creating {{count}} eBay Product To Shopify", "other": "Creating {{count}} eBay Products To Shopify"}, "bulkProductUploadToEbay": {"one": "Uploading {{count}} shopify product To eBay", "other": "Uploading {{count}} shopify products To eBay"}, "bulkProductDeleteToShopify": {"one": "Deleting {{count}} shopify product", "other": "Deleting {{count}} shopify products"}, "bulkProductEndOnEbay": {"one": "Ending {{count}} eBay product", "other": "Ending {{count}} eBay products"}}, "sortOptions": {"productTitle": "Product Title", "updated": "Updated", "created": "Created", "startTime": "Start Time", "endTime": "End Time", "AZSortDirection": "A-Z", "ZASortDirection": "Z-A", "oldestFirstSortDirection": "Oldest First", "newestFirstSortDirection": "Newest First", "earliestFirstSortDirection": "Earliest First", "latestFirstSortDirection": "Latest First"}, "banner": {"extendTrialWarningBannerActionContent": "Extend Trial", "extendTrialWarningBannerDescription": "We have applied a free trial for you. Please access it by clicking the button below.", "extendTrialWarningBannerTitle": "One step left to extend your free trial:", "refreshTokenWarningBannerTitle": "Action Required", "refreshTokenWarningBannerActionContent": "Grant Access", "refreshTokenWarningBannerDescription": "For the app to sync seamlessly with eBay, we need your permission to renew the connection. Please make sure you're logged into the same eBay account to which the app is connected.", "loadingBannerInformation": "We kindly request your patience as the page is loading. We appreciate your understanding during this process."}, "common": {"action": "Action", "apply": "Apply", "addProducts": "Add Products", "askForHelp": "Ask for help", "confirm": "Confirm", "condition": "Condition", "linkNow": "Link Now", "IConsentTheRequest": "I consent the request", "somethingWentWrong": "Something went wrong. Please contact support", "add": "Add", "all": "All", "allActive": "All Active", "attributeMapping": "Attribute Mapping", "applyThisToAllProducts": "Apply this for all products", "applyThisToAllProductsInProfile": "Apply this for all products in the profile", "goBack": "Go Back", "businessPolicies": "Business Policies", "cancel": "Cancel", "category": "Category", "categoryName": "Category Name", "contactUs": "Contact Us", "continue": "Continue", "countInStock": "{{quantity}} in stock", "connectedCountInStock": "{{connected}} connected ({{total}} total)", "dashboard": "Dashboard", "writeToConfirm": "Write {{text}} to confirm", "decrease": "Decrease", "eBayError": "eBay Error: ", "ebayProducts": "eBay Products", "ebayProfiles": "eBay Profiles", "email": "Email", "endedOnEbay": "Ended On eBay", "error": "Error", "exportEbayProducts": "Import eBay Products", "FAQ": "FAQ", "getSupport": "Get Support", "filteredProduct": "filtered product", "filteredProducts": "filtered products", "fixedAmount": "Fixed Amount", "fixedPercentage": "Fixed Percentage", "forNVariants": "for {{count}} variant", "importedToShopify": "Imported To Shopify", "increase": "Increase", "inventory": "Inventory", "linkedInventory": "Linked Inventory", "linked": "Linked", "inventoryDescription_withoutVariants": "{{quantity}} in stock", "inventoryDescription_withVariants": "{{quantity}} in stock for {{count}} variants", "itemId": "Item ID", "learnMore": "Learn More", "learnMoreWithLink": "Learn more about <0>{{linkTitle}}</0>", "learnMoreAbout": "Learn more about {{about}}", "noOfProducts": "No. of Products", "noProductsFound": "No Products Found", "notAvailable": "Not Available", "notImported": "Not Imported", "notSynced": "Not Synced", "notUploaded": "Not Uploaded", "uploaded": "Uploaded", "failed": "Failed", "paymentPolicy": "Payment Policy", "perPage": "Per Page", "prefix": "Prefix", "price": "Price", "proceed": "Proceed", "proceedToDelete": "Proceed To Delete", "proceedToExport": "Proceed To Import", "proceedToUpload": "Proceed To Upload", "product": "Product", "products": "Products", "optionalAttributeMapping": "Optional Attributes Mapping", "orders": "Orders", "pleaseWait": "Please Wait...", "profile": "Profile", "profileName": "Profile Name", "profiles": "Profiles", "progressCompletedCount": "{{completedCount}} of {{totalCount}} tasks completed", "readMore": "Read More", "remarks": "Remarks", "remove": "Remove", "resolve": "Resolve", "removeConfirmationModalTitle": "Are you sure you want to remove this product?", "removeConfirmationModalDescription": "You won't be able to upload this product if you remove it from the list.", "returnPolicy": "Return Policy", "save": "Save", "skip": "<PERSON><PERSON>", "search": "Search", "searchPlaceholder": "Search by Product SKU or Title", "settings": "Settings", "shippingPolicy": "Shipping Policy", "shopifyProducts": "Shopify Products", "site": "Site", "SKU": "SKU", "status": "Status", "stock": "Stock", "subject": "Subject", "pricing": "Subscription", "submit": "Submit", "success": "Success", "suffix": "Suffix", "synced": "Synced", "syncInventory": "Sync Inventory", "syncProductsFromEbay": "Sync products from eBay", "title": "Title", "productsStatus": "Products Status", "uploadToEbay": "Upload to eBay", "marketplace": "Marketplace", "viewOn": "View On", "unexpectedBadThingsMessage": "Unexpected bad things may happen if you don't read this.", "iConsentRequest": "I consent the request", "processedCountMessage": "{{upload}}/{{total}} products processed", "welcomeToDpl": "Welcome to DPL eBay Integration & Import", "welcome": "Welcome", "country": "Country", "postCode": "Post Code", "city": "City", "clickHere": "Click here", "storeCategory": "Store Category", "collection": {"one": "collection", "other": "collections"}, "upgradeNow": "Upgrade Now", "resolving": "Resolving", "productTitle": "Product Title", "productRemoved": "Product removed successfully", "quantity": "Quantity", "location": "Location", "loading": "Loading...", "noLocationsAvailable": "No locations available", "selectLocation": "Select a location", "statusFilter": "Status: {{status}}"}, "errors": {"quantityInvalid": "Quantity should be greater than zero", "quantityMustBeWholeNumber": "Quantity cannot contain decimal points", "quantityRequired": "Quantity is required", "locationRequired": "Please select a location", "quantityInvalidDescription": "The Quantity of this product is not valid. It should be greater than zero. Please enter a valid quantity and select the location where you want to update the inventory.", "quantityLocationUpdateNote": "This will also update the quantity in Shopify for the selected location."}, "collections": {"bannerDescription": "In this page you can select Shopify collections and upload the products within the selected collection to eBay.", "searchPlaceholder": "Search by Collection Name"}, "contactUs": {"emailRequired": "Email is required", "emailTextFieldHelpText": "We'll use this email address to get back to you", "messageRequired": "Message is required", "nameRequired": "Name is required", "nameTextFieldLabel": "Your Name", "pageHeader": "Let's have a chat", "subjectRequired": "Subject is required", "yourMessage": "Subject is required"}, "dashboard": {"changeEbayStoreModal": {"title": "Change your eBay store?", "message": "If you switch to a different store, your connected eBay store products, profiles, orders, linked inventory will be removed from our app. They'll be replaced by the new store you want to use", "helpText": "Write CONFIRM to continue"}, "sharedSkuMessage": "You have a shared SKU on eBay. Please carefully review this setting.", "changeYourEbayStore": " Change your eBay store", "welcome": "Welcome!", "welcomeBannerDescription": "Get an overview of your products, their upload status, and recent app activities. Manage and track your product imports and exports effortlessly.", "connected": "Connected", "customer": "customer", "customers": "customers", "dashboard": "Dashboard", "eBayInfo": "eBay store info", "exportedToShopify": "Imported to Shopify", "notExported": "Not Imported", "productImportLimit": "Product import limit", "productsOverview": "Products overview", "productUploadLimit": "Product upload limit", "planName": "Plan Name", "eBayStore": "Store name", "eBaySite": "Site", "eBayStoreStatus": "Store status", "notOptimized": {"badgeText": "Not Optimized", "bannerDescription": "Schedule a call with us to optimize your store connection and seamlessly automate the sync between Shopify and eBay!", "bannerTitle": "Optimize your store sync", "buttonText": "Book a Call Now"}, "subscriptionInfo": "Subscription info", "subscriptionPrice": "Subscription price", "todo": "Things to consider", "nothingToConsider": "There is nothing to consider as of now.", "todoUnlinkedProductCount": {"one": "{{count}} Product is available to be imported to Shopify", "other": "{{count}} products are available to be imported to Shopify"}, "todoInactiveLocations": {"one": "{{count}} shopify location can be connected for inventory sync", "other": "{{count}} shopify locations can be connected for inventory sync"}, "totalEbayProducts": "Total eBay products", "trialEndsIn": "Trial ends in", "orderSyncLimit": {"title": "Order sync limit", "message": "{total} / month ({used} Used)"}, "unlimited": "Unlimited", "viewDetailsFor": "View details for {{name}}", "recentAppActivities": "Recent App Activities", "remainingDay": {"one": "{{count}} day", "other": "{{count}} days"}, "DashboardPage": {"AppUpdate": {"whatsNew": "What's New?", "comingSoon": "Coming Soon", "newRead": "New Read", "updated": "Updated", "new": "New"}}}, "ebayProducts": {"bulkCreateModal": {"title": "Import all products in Shopify", "message": "Are you sure you want to create all your eBay products in Shopify?", "confirmText": "Create in Shopify"}, "bulkCreateButtonLabel": {"one": "Import {{count}} product on Shopify", "other": "Import {{count}} products on Shopify"}, "bulkCreateInfoBannerDescription": "Before starting the import process, you can easily remove any products from the list by clicking \"Remove\".", "bulkCreatePageTitle": "Import eBay Products to Shopify", "bulkDeleteButtonLabel": {"one": "Delete {{count}} product from Shopify", "other": "Delete {{count}} products from Shopify"}, "bulkDeletePageTitle": "Delete Products from Shopify", "createInShopify": "Import in Shopify", "deleteFromShopify": "Delete from Shopify", "deleteProductBannerInfo": "Before starting the deletion process, you can deselect any products by clicking on \"Remove\" to remove them from the delete list.", "deletingEbayProducts": "Deleting Shopify Products ...", "deletingEbayProductsMessage": "Your selected shopify products imported from eBay will be deleted. Wait for the process to complete.", "emptyStateDescription": "Looks like you haven't imported your eBay products to Shopify yet. Please import products from eBay.", "ebayProductBannerInfo": "This page displays the products from your eBay store and you can import these products to your Shopify store.", "exportingEbayProducts": "Importing eBay products to shopify", "exportingEbayProductsMessage": "Your eBay products are being imported to Shopify. Please wait for the process to complete.", "importingEbayProducts": "Importing eBay Products...", "importingEbayProductsMessage": "Your eBay products are importing. Please wait for completion.", "importEbayProductModal": {"title": "Import {{title}} in Shopify", "description": " The product will be imported. Please wait until it completes."}, "tryChangingFilters": "Try changing the filters or search term", "filterByItemSellingLocation": "<PERSON><PERSON> Location", "bulkCreateAll": {"buttonLabel": "Import all active products on Shopify", "buttonLabelWithFilter": "Import all filtered products on Shopify", "modalTitle": "Do you want to import all active eBay products to Shopify?", "modalTitleWithFilter": "Do you want to import all filtered eBay products to Shopify?", "modalProceedToImport": "Proceed to Import", "modalMessageIfRemaining": "You currently have <0>{{count}}</0> active eBay products remaining to be imported. Once you proceed, all the {{count}} products will be imported to your Shopify store.", "modalMessageIfRemainingWithFilter": "You currently have <0>{{count}}</0> filtered eBay products remaining to be imported. Once you proceed, all the {{count}} products will be imported to your Shopify store.", "modalMessageIfZero": "You have already imported all your remaining active eBay products to your Shopify store", "modalMessageIfZeroWithFilter": "You have already imported all your remaining filtered eBay products to your Shopify store"}, "endEbayListingBannerDescription": "Before starting the end listing process, you can deselect any products by clicking on \"Remove\" to remove them from the list."}, "endListingsLabels": {"NotAvailable": "The item is no longer available for sale.", "LostOrBroken": "The item was lost or broken", "Incorrect": "The item start price or reserve price is incorrect"}, "faq": {"topic": {"commonUploadErrors": {"title": "Common Upload Errors", "listingWouldCauseYouToExceedTheNumberOfItemsAndAmountYouCanList": "This listing would cause you to exceed the number of items (X) and amount (X) you can list.", "listingWouldCauseYouToExceedTheNumberOfItemsAndAmountYouCanListAnswer": "eBay has set a monthly selling limit for every user to protect buyers and foster a safe environment for everyone. While our application cannot increase your eBay-imposed selling limit, it offers a Custom Inventory feature to help you maximize your product uploads from Shopify to eBay. Learn more about the error from <0>here</0>.", "listingWouldCauseYouToExceedTheNumberOfItemsAndAmountYouCanListAnswerLink": "https://ebayintegration-dpl.crisp.help/en/article/this-listing-would-cause-you-to-exceed-the-number-of-items-x-and-amount-x-you-can-list-lgvx05/", "itemCannotBeListedOrModified": "Items cannot be listed or modified.", "itemCannotBeListedOrModifiedAnswer": "This is a generic eBay error caused by either a violation of the eBay selling policy or a restriction on your eBay account. When this happens, you will see a 'Read More' button next to the error message, where you can view the detailed explanation provided by eBay. Learn more about the error from <0>here</0>.", "itemCannotBeListedOrModifiedAnswerLink": "https://ebayintegration-dpl.crisp.help/en/article/items-cannot-be-listed-or-modified-1gy9z8q/", "yourEbayAccountRequiresAdditionalInformationBeforeYouCanSellOnEbay": "Your eBay account requires additional information before you can sell on eBay.", "yourEbayAccountRequiresAdditionalInformationBeforeYouCanSellOnEbayAnswer": "This error message is generated directly by eBay, indicating that your seller account requires additional setup before listing products. Your eBay seller account is not fully configured and must have a valid payment method for eBay seller fees. . Learn more about the error from <0>here</0>.", "yourEbayAccountRequiresAdditionalInformationBeforeYouCanSellOnEbayAnswerLink": "https://ebayintegration-dpl.crisp.help/en/article/your-ebay-account-requires-additional-information-before-you-can-sell-on-ebay-1wv43u3/", "variationsNotEnabledInTheCategory": "Variations not enabled in the category.", "variationsNotEnabledInTheCategoryAnswer": "The error you are encountering occurs because the eBay category you selected does not support variation products. Learn more about the error and its solution from <0>here</0>.", "variationsNotEnabledInTheCategoryAnswerLink": "https://ebayintegration-dpl.crisp.help/en/article/variations-not-enabled-in-the-category-1i6pdla/", "newlyCreatedEbayProductsDidNotSyncToOurApp": "Newly created eBay products did not sync to our app", "newlyCreatedEbayProductsDidNotSyncToOurAppAnswer": "Our application automatically fetches newly created eBay products every 30 minutes due to the eBay API rate. However, if you want to sync your newly added products immediately, you can do so with just one click. Learn more about the error from <0>here</0>.", "newlyCreatedEbayProductsDidNotSyncToOurAppAnswerLink": "https://ebayintegration-dpl.crisp.help/en/article/newly-added-ebay-products-did-not-sync-to-our-app-1gehrh/", "theItemSpecificIsMissing": "The item specific (xxx) is missing.", "theItemSpecificIsMissingAnswer": "In most cases, this error occurs because the mapped attribute does not have a value assigned in Shopify. Refer to the screenshot below for guidance. Learn more about the error and its solution from <0>here</0>.", "theItemSpecificIsMissingAnswerLink": "https://ebayintegration-dpl.crisp.help/en/article/the-item-specific-xxx-is-missing-kwvsh9/", "pleaseEnterValidDimensionsForYourPackage": "Please enter valid dimensions for your package.", "pleaseEnterValidDimensionsForYourPackageAnswer": "If you have selected Calculated Shipping in your eBay Business Policy, you must ensure that your Shopify products have both weight and dimensions (height, length, and width) specified. Learn more about the error and its solution from <0>here</0>. ", "pleaseEnterValidDimensionsForYourPackageAnswerLink": "https://ebayintegration-dpl.crisp.help/en/article/please-enter-valid-dimensions-for-your-package-eqwy07/", "ebayMotorsCategoriesNotAvailableInTheApp": "eBay motors categories not available in the App.", "ebayMotorsCategoriesNotAvailableInTheAppAnswer": "You might have selected 'United States' as your eBay Selling Region during the onboarding process. However, to access eBay Motors categories, you must select 'United States - eBay Motors.' Learn more about the error and its solution from <0>here</0>.", "ebayMotorsCategoriesNotAvailableInTheAppAnswerLink": "https://ebayintegration-dpl.crisp.help/en/article/ebay-motors-categories-not-available-in-the-app-1c33yaw/", "skuExceedsTheMaximumLengthOf80": "SKU exceeds the maximum length of 80.", "skuExceedsTheMaximumLengthOf80Answer": "eBay allows a maximum of 80 characters for SKUs. If your Shopify product SKUs exceed this limit, you will encounter an error when attempting to upload them to eBay. Learn more about the error and its solution from <0>here</0>.", "skuExceedsTheMaximumLengthOf80AnswerLink": "https://ebayintegration-dpl.crisp.help/en/article/sku-exceeds-maximum-length-80-4zwjpk/"}}, "titles": {"general": "General", "productUploads": "Product Uploads", "orderInventoryManagement": "Order & Inventory management", "support": "Support"}, "bannerMessage": "Here you can find answers to frequently asked questions by our users. If you cannot find answers for your confusion please feel free to contact us.", "howLongDoesItTakes": "How long does it take to set up the app?", "howLongDoesItTakesAnswer": "The app installation process only takes a short time. After installing the app, the setup process begins immediately. You must connect your account by logging in to eBay to complete the setup. Once you have done that, you can start using the app.", "canIFilterProducts": "Can I filter the products and select only the ones I want to upload rather than uploading all the products?", "canIFilterProductsAnswer": "Yes, you can filter and choose only the products you wish to Upload/Import into Shopify/eBay from the app.", "howDoesAutoGenerationWork": "How does Auto SKU Generation work?", "howDoesAutoGenerationWorkAnswer": "The SKU is the core identifier for synchronizing inventory and orders. Problems can occur when merchants do not provide an SKU for a product, leading to synchronization issues.our application includes an automated SKU generation feature, ensuring that the necessary SKUs are created and linked within the application without any concern on your part. <0>Learn more about how SKUs generation works</0>", "howDoesAutoGenerationWorkAnswerLink": "https://help.exportfeed.com/article/502-how-does-auto-sku-generation-work", "automaticEndListing": "How automatic End Listing works?", "automaticEndListingAnswer": "As a seller, you might have to change the Shopify product to \"draft,\" \"delete,\" or \"archive\" when the stock is depleted. This change may be made by you or any other third-party apps to maintain the inventory. In such cases, if you have uploaded the Shopify product to eBay using our app or have the product linked, then such immediate action in your Shopify product should be reflected in your linked eBay product as well. <0>Learn more about Automatic End listing.</0>", "automaticEndListingAnswerLink": "https://help.exportfeed.com/article/523-automatic-end-listing", "ebayCategoriesAllowedVariations": "Which eBay categories allow variations?", "ebayCategoriesAllowedVariationsAnswer": "I use product variations on my site, but eBay doesn't allow variations in the selected category. How can I find out which categories allow variations. <0>Learn more about variation allowed eBay Categories</0>", "ebayCategoriesAllowedVariationsAnswerLink": "https://help.exportfeed.com/article/506-which-ebay-categories-allow-variations", "uploadIndividualShopifyProductsToEbay": "How to upload Individual Shopify products to eBay?", "uploadIndividualShopifyProductsToEbayAnswer": "Our app makes it incredibly easy for you to upload your Shopify products. Whether you prefer to add them individually or choose multiple products at once, the choice is entirely yours!  <0>Learn more about upload individual products upload to eBay.</0>", "uploadIndividualShopifyProductsToEbayAnswerLink": "https://help.exportfeed.com/article/481-individual-shopify-products-upload-to-ebay", "uploadShopifyCollectionsToEbay": "How to upload Shopify Collections to eBay?", "uploadShopifyCollectionsToEbayAnswer": "Our app makes it simple to upload your Shopify collection on eBay. The main advantage of uploading as a collection is that if you add any new products to this already uploaded collection, you won't have to manually upload those products. The app will automatically sync those products on eBay. <0>Learn more about upload Shopify collection upload to eBay.</0>", "uploadShopifyCollectionsToEbayAnswerLink": "https://help.exportfeed.com/article/480-shopify-collections-upload-to-ebay", "importYourEbayProductsToShopify": "How to import your eBay Products to Shopify?", "importYourEbayProductsToShopifyAnswer": "Our app also helps you import your eBay products to Shopify with just a few clicks. You can choose to import products individually or select all at once and import them to Shopify. <0>Learn more  about import eBay products to Shopify.</0>", "importYourEbayProductsToShopifyAnswerLink": "https://help.exportfeed.com/article/479-export-ebay-products-to-shopify", "orderSyncWorkingMechanism": "How does Order Sync Works on eBay Integration - DPL?", "orderSyncWorkingMechanismAnswer": "If you upload your products from Shopify to eBay using our app or link the existing products, the eBay orders will automatically start synchronizing with Shopify for fulfillment. You can conveniently handle your eBay orders directly from Shopify itself. <0>Learn more about How does Order Sync Works on eBay Integration - DPL</0>", "orderSyncWorkingMechanismAnswerLink": "https://help.exportfeed.com/article/492-how-order-sync-works", "ifEbayOrderSyncFails": "What happens if eBay order sync fails to Shopify?", "ifEbayOrderSyncFailsAnswer": "If you notice any order syncs have failed, check the order details. The main reason for the failed status is usually the SKU not being found on the Shopify product.In this case, click on the down arrow, and you will see a \" Link Product \" button on the order line item to manually link it. <0>Learn more about What happens if eBay order sync fails to Shopify?</0>", "ifEbayOrderSyncFailsAnswerLink": "https://help.exportfeed.com/article/602-how-to-manually-sync-your-ebay-orders-to-shopify", "customInventory": "How do I set the custom inventory when uploading from Shopify to eBay?", "customInventoryAnswer": "eBay limits the seller on the listing that can be listed and sold. You can easily view your seller limits by visiting your eBay seller hub. So for this case, you can either request eBay support to increase the limit or limit the inventory sent to eBay while uploading products from our app. <0>Learn more about  setting the custom inventory when uploading from Shopify to eBay.</0>", "customInventoryAnswerLink": "https://help.exportfeed.com/article/522-what-is-inventory-settings", "syncInventory": "How can I sync the inventory of my products between Shopify and eBay?", "syncInventoryAnswer": "First, your product should be linked to synchronize your inventory between Shopify and eBay. Once you connect the products between the two stores, the system will synchronize inventory. <0>Learn more about how inventory sync works between Shopify and eBay.</0>", "syncInventoryAnswerLink": "https://help.exportfeed.com/article/604-how-can-i-sync-the-inventory-of-my-products-between-shopify-and-ebay", "realTimeInventory": "How does real-time inventory sync work in eBay Integration - DPL?", "realTimeInventoryAnswer": "You can link or upload products from one platform to another, and the system handles the rest. To synchronize inventory in real time, we collaborate with eBay, Our Application, and Shopify. Here's how it works: first, we import data by calling the API from Shopify or eBay. Next, we process the data in our system. Finally, we sync everything between Shopify and eBay by calling the API again and sending the processed data to the next platform. Please note that the process is usually completed in real time, although it may take a few minutes. <0>Learn more about Real time inventory Sync</0>", "realTimeInventoryAnswerLink": "https://help.exportfeed.com/article/603-how-does-real-time-inventory-sync-work-in-ebay-integration-dpl", "customerSupportAvailable": "Is customer support available for any issues or questions?", "customerSupportAvailableAnswer": "We offer 24/7 customer support to assist you with any questions or concerns regarding the use of our application.", "newFeatures": "Are there plans for any new features in the future for eBay Integration - DPL?", "newFeaturesAnswer": "Yes, our goal is to assist as many online sellers as possible, and we plan to add new features and functionality to our app to help achieve this goal. We are always looking for ways to improve and expand our app to serve our users better. We welcome any suggestions or ideas that could help improve the app or make it more useful. If you have any ideas or feedback, we would be happy to hear them."}, "framework": {"filter": {"allResultsSelected": "All results selected", "selectCount": "{{count}} selected", "showingProductsCount": "Showing {{count}} products", "undo": "Undo", "selectAllResults": "Select all results"}}, "inventory": {"inventoryBannerInfo": "This page displays eBay and Shopify inventory by SKU, with Shopify as the primary source of inventory. eBay inventory syncs to Shopify through eBay orders.", "emptyStateHeading": "Inventory Setting Disabled", "emptyStatePrimaryButtonLabel": "Enable in setting", "emptyStateDescription": "Inventory won't be synced across Shopify and eBay products.", "infoBannerDescription": "The eBay and Shopify products listed below will have their inventory synchronized based on the matching SKU.", "inventory": "Inventory", "linkedSkuAndInventory": "Linked SKU & Inventory", "product": "Product", "sharedCount": "Shared count", "sharedCountEbayTooltip": {"one": "This sku is shared with {{count}} eBay product", "other": "This sku is shared with {{count}} eBay products"}, "sharedCountShopifyTooltip": {"one": "This sku is shared with {{count}} Shopify product", "other": "This sku is shared with {{count}} Shopify products"}, "products": "Products", "searchPlaceholder": "Search by Product SKU or Title", "SKU": "SKU", "status": "Status", "stock": "Stock", "zeroSharedCountEbayTooltip": "This sku is not shared with any eBay product", "zeroSharedCountShopifyTooltip": "This sku is not shared with any Shopify product", "loadingStatePlaceholder": "Loading variants...", "inventoryOverriddenMessage": "You might see difference in stock for this variant as it was uploaded overriding the normal quantity", "viewByLocation": "View inventory by location", "inventoryByLocation": "Inventory by Location", "location": "Location", "unavailable": "Unavailable", "committed": "Committed", "available": "Available", "onHand": "On hand", "connected": "Connected", "noInventoryData": "No inventory data", "waitingForInventoryData": "Please wait while we fetch the inventory data"}, "orders": {"ordersBanner": "This page displays the status and details of the orders that were placed in eBay after installing the app.", "subtotal": "Subtotal", "total": "Total", "tax": "Tax", "shipping": "Shipping", "handlingCost": "Handling Cost", "discounts": "Discounts", "orderTransactions": "Order Transactions", "customerDetails": "Customer Details", "loadingStatePlaceholder": "Loading details...", "status": {"synced": "Synced", "notSynced": "Not Synced", "failed": "Failed"}, "emptyStates": {"noOrdersFound": "No Orders Found", "noOrdersFoundMessage": "Orders will be displayed as soon as you receive them. Stay tuned for real-time updates on your order status.", "orderSyncDisabled": {"heading": "Order Sync Setting Disabled", "body": "Order won't be synced across Shopify and eBay", "button": "Enable in setting"}}, "toasts": {"orderItemLinked": "Order Item Linked Successfully", "somethingWentWrong": "Something went wrong"}, "resources": {"singular": "order", "plural": "orders"}, "tableHeadings": {"orderID": "Order ID", "orderSync": "Order Sync Status", "orderDate": "Order Date", "paymentStatus": "Payment Status", "customer": "Customer", "orderFulfillmentStatus": "Fulfillment Status", "total": "Total"}, "orderPaymentStatus": {"received": "Received", "incomplete": "Incomplete", "pending": "Pending"}, "linkModal": {"title": "Link to Shopify Variant", "searchPlaceholder": "Search by shopify variant SKU or product title"}, "tableTabs": {"all": "All", "new": "New", "fulfilled": "Fulfilled", "cancelled": "Cancelled", "failedOrders": "Failed Orders", "searchPlaceHolder": "Search by Order ID"}, "toolTips": {"viewOrderDetails": "View order details", "orderSyncSuccess": "Congrats! This order has been sent to Shopify", "orderSyncFailed": "This order failed to  be sent to Shopify", "orderSyncFailedDueToLimitReach": "This order failed to sync to Shopify because your monthly order sync limit has been reached.", "orderSyncInProgress": "This order is trying to be sent to Shopify", "pendingPaymentStatus": "Order is considered pending when payment from the buyer has been initiated but has yet to be fully processed", "completePaymentStatus": "An order or order line item is complete when payment from the buyer has been initiated and has been processed", "incompletePaymentStatus": "An order or order line item is considered incomplete when payment from the buyer has yet to be initiated"}}, "navigator": {"createProductToEbay": "Upload products to eBay", "createProductToEbayDescription": "Boost your sales by seamlessly uploading Shopify products to eBay", "exportProductToShopify": "Import products to Shopify", "exportProductToShopifyDescription": "Easily upload eBay products to Shopify and expand your online store", "heading": "Choose your next step!", "manageInventories": "Manage inventories", "manageInventoriesDescription": "Simplify inventory management for both stores from a single dashboard", "manageOrders": "Manage Orders", "manageOrdersDescription": "Streamline order management by syncing your eBay orders to Shopify and fulfilling them."}, "onboarding": {"connect": "Connect", "connectYourEbayAccount": "Connect your eBay account", "connectYourEbayAccountDescription": "Connect your eBay Account to start importing eBay products.", "currencyConversion": "Currency Conversion", "currencyConversionBannerDescription": "We noticed difference in currency in your Shopify and eBay store. Please set a conversion rate which will be used to upload your products. You can modify these rate later on the settings", "currencyConversionBannerTitle": "Before you proceed ahead!", "currencyConversionShopifyCurrencyTextFieldHelpText": "This is the currency used in your Shopify Store", "currencyConversionEbayCurrencyTextFieldHelpText": "This is the currency used in your eBay Store", "ebayConfiguration": "eBay Configurations", "title": "eBay Shop Connection and Configuration", "syncInventoryDescription": "Syncs the Shopify and eBay inventory if SKU is matched"}, "subscription": {"productLimitPerMonth": "{count}", "pricePerMonth": "${price} / month", "unlimited": "Unlimited", "errorMessage": "Something went wrong. Please try again in a bit or contact support", "banner": {"title": "Trial information", "body": "It's absolutely free to use our app for the first {trialDays} days, and you can cancel anytime. After the trial period ends, billing will begin automatically."}, "price": "$ {amount} / Month", "planDetail": "You are subscribed to {plan_name} recurring at ${plan_price} / month", "trialDetail": "Your trial ends on {trial_ends_on}. If you uninstall the app {trial_ends_on}, you will not get charged.", "planLimit": "Plan Limit", "planFeatures": {"uploadToEbay": "Publish {count} Products on eBay", "importToShopify": "Import {count} Products from eBay to Shopify", "inventoryAndPrice": "Real-time Inventory & Price Sync", "orderFulfillment": "Automatic Order Management & Fulfillment", "orderLimit": "Manage {count} orders monthly", "liveChatPeriod": "24/7 Live Chat on Weekdays & Email Support on Weekends", "aiSuggestions": "AI Product Upload Resolver"}, "currentTrialBadge": "Current Trial", "currentPlanBadge": "Current Plan", "popular": "Popular", "subscribeButton": {"startTrial": "Start {{trial_days}} days free trial", "selectPlan": "Select Plan"}}, "pagination": {"currentPage": "{{current_page}} of {{last_page}}", "next": "Next(K)", "previous": "Previous(J)"}, "profiles": {"itemLocationCard": {"title": "Item Location", "country": {"label": "Country or Region", "placeholder": "Select Country"}, "postCode": {"label": "Post Code"}, "city": {"label": "City,County"}}, "condition": {"description": "You can choose from one of several preset item condition options, which vary depending on the category you list your item in."}, "productStatusCount": "<uploadedLink>{{uploaded}} uploaded</uploadedLink>, <failedLink> <failedText>{{failed}} failed</failedText></failedLink>", "automaticProductUploadTitle": "Automatic product upload setting", "automaticProductUploadCheckboxTitle": "Upload Products automatically when added to selected collection using this profile.", "shopifyVariationStateChangedModal": {"bannerMessage": "You have changed your Shopify product from single to variable or vice versa. If the products once successfully uploaded to eBay, cannot be updated with the variation state change. It needs to be ended and re-uploaded again with the new variation details. Your product link will remain intact ", "proceedCheckBoxMessage": "Proceed with ending the listing and re-upload again"}, "duplicateSkuModal": {"bannerMessage": "eBay requires different SKU within the product to upload irrespective of the selected category.You will need to either update the SKU differently in each product or use the below auto SKU generate and upload feature. ", "body": "This will generate a unique SKU and upload to the eBay. However, your SKU won't be changed in Shopify but it will linked in the app. Your order and inventory will be synced.", "generateForThisProdOnly": "Generate a unique SKU and upload for this product only", "replaceAndUpload": "Replace and Upload", "iWillUpdateOnShopify": "I will update on Shopify"}, "errorResolution": {"auctionEnded": {"bannerMessage": "If the listing has already been manually relisted on eBay, this resolve process will link your listing to the relisted item; otherwise, it will relist the item.", "checkboxLabel": "Please confirm to proceed further."}}, "uploadButtonDisabledMessages": {"title": "Upload button is disabled", "body": "Please fix the following errors to enable it", "shippingPolicy": "Shipping policy is required", "returnPolicy": "Return policy is required", "paymentPolicy": "Payment policy is required", "category": "Please select a category", "attributes": "Please fill all the required attributes", "condition": "Please select the appropriate condition", "profile_name": "Please update profile name"}, "removeOrEndSelectedProdsModal": {"removeSelectedProds": "Remove selected products?", "endEbayLabel": "End selected products on eBay as well", "IConsentTheRequest": "I consent the request", "writeDeleteToConfirm": "Write DELETE to confirm"}, "calculatedShippingSelectedBanner": {"title": "Information regarding shipping policy", "description": "Since you have selected a calculated shipping. eBay requires the package dimensions to be sent along while upload to eBay. So we will fetch the dimensions and weights for your Shopify products. "}, "storeCategory": {"banner": {"title": "Information regarding store category", "description": "eBay Store Categories allow sellers on eBay to organize the listings into different customized categories within the store and help buyers navigate through the seller's inventory more efficiently, making it easier for them to find the desired products. "}, "similarCategorySelection": "Primary and secondary categories should be different.", "primaryCategorySelectionMissing": "Selection of primary store category is required before assigning a secondary category in eBay.", "categoryNotExisting": "Seems like the previously assigned {{categoryType}} category does not exist in eBay.", "refresh": "Refresh eBay store categories", "refreshTooltip": "Selected store category options become empty if not present after refresh", "refreshing": "Refreshing eBay store categories ...", "refreshSuccessMessage": "Your eBay store categories synced successfully. ", "refreshErrorMessage": "Could not fetch eBay store categories.Please try again . ", "collapsibleButtonText": "Could not fetch eBay store categories.Please try again . ", "primaryStoreCategory": "Primary Store Category", "secondaryStoreCategory": "Secondary Store Category", "manageText": "<0>Click here </0> to manage store categories."}, "additionalSettings": "Additional Settings", "additionalSettingsDescription": "<0>Click here </0> to explore additional options for pricing, title, VAT and sync settings", "attributeSelection": {"bannerDescription": "Selected category does not have any attributes to map.", "bizPolicyLink": "<0> Click here </0> to create business policy on eBay seller hub."}, "optionalAttributeMappingDescription": "<0> Click here </0> to add optional attributes mappings.", "bizPolicies": {"fetchBusinessPolicesFromEbay": "Fetch Business policies from eBay", "bizPoliciesNotFoundBanner": {"title": "Business policies not found", "message": "You don't have any business policies in your eBay seller account. eBay.com requires business policies to upload products. Once you have created them on your eBay seller account, please fetch them using the \"{{buttonName}}\" link above", "actionContent": "Create business policies on eBay"}}, "singlePolicyNotFoundErrorMessage": "You don't have {{policyName}} policy, {{policyName}} policy is a part of business policy. <0> Click here </0> to learn how to create business polices", "collectionsInThisProfile": "Collections in this profile", "bannerDescription": "Please fill all the form fields to upload the products to eBay.", "couldNotFindAnyResults": "Could not find any results", "customAttribute": "Custom Attribute", "categoryRequiredError": "Please select an item.", "deleteProfile": "Delete Profile", "deleteProfileConfirmationDescription": "All your Shopify products that are in this profile will stop syncing to eBay if you delete this profile.", "deleteProfileConfirmationPrimaryButton": "Yes, Delete Profile", "deleteProfileConfirmationTitle": "Are you sure you want to delete this profile?", "ebayCategory": "eBay Category", "editProfile": "Edit Profile", "editProfiles": {"attributeRefetchBannerDescription": "Please wait while we retrieve the attributes of the chosen category. If the process takes a little longer, please contact our support team."}, "editProfileBanner": "This page allows you to make changes to your existing profile and the products will update automatically.", "emptyProfileDescription": "Profiles are created while uploading products to eBay", "emptyProfileTitle": "No profiles yet", "emptyProfileActionLabel": "Upload products to eBay", "existingProfiles": "Existing Profiles", "infoBannerDescription": "This page displays and allows you to manage the profile listings of the eBay products that are uploaded from your Shopify store to eBay.", "priceBy": "Price by", "productDetails": "This page displays the list of products included in the profile with their upload status.", "priceSettingModifyPriceCheckboxLabel": "Modify price while upload to eBay", "priceSettings": "Price Settings", "profileNameTextFieldLabel": "Save the selected settings for later use and assign them a profile name to make them easily accessible in the future", "profileDescription": "Profile Description", "profileDeselectedMessage": "Profile has been deselected due to some change appeared in previously selected attribute.", "profileTitle": "Profile settings", "refetchAttributes": "Refetch Attributes", "refetchPolicies": "Refetch Policies", "refetchPoliciesSuccessToast": "Business Policies refetched Successfully.", "removeProductFromProfile": "Remove Product From Profile", "searchCategory": "Search Category", "selectAttribute": "Select Attribute", "searchPlaceholder": "Search by Profile Name", "selectPreferredCategory": "Select your preferred eBay category", "shippingPolicy": {"placeholder": "Select a shipping policy", "emptyErrorMessage": "Please select a shipping policy."}, "returnPolicy": {"placeholder": "Select a return policy", "emptyErrorMessage": "Please select a return policy."}, "paymentPolicy": {"placeholder": "Select a payment policy", "emptyErrorMessage": "Please select a payment policy."}, "shopifyHandle": "Shopify Handle", "shopifyProductType": "Shopify Product Type", "shopifyTags": "Shopify Tags", "shopifyTitle": "Shopify Title", "shopifyVendor": "Shopify Vendor", "shopifyBarcode": "Shopify Barcode", "shopifyProductMetafields": "Shopify Product Metafields", "selectAttributeType": "Select Attribute Type", "shopifyProductModal": {"confirmationCheckboxLabel": "I confirm to add selected products or collection to this profile.", "confirmationCheckboxHelpText": "Note that your products attributes will be updated according to the settings of this profile.", "selectAllResultInfo": "Selecting all results will move your collection and all it's products to this profile."}, "shopifySKU": "Shopify SKU", "inventorySettings": {"title": "Inventory Settings", "overrideInventoryCheckboxMessage": "Override inventory with fixed inventory for eBay products?", "overrideInventoryDescription": "Set a fixed inventory value. If current inventory is greater than this value, this overridden value will be used. Please note: If the product quantity is configured as Not tracked or Continue selling when out of stock on Shopify, the inventory value specified here will be synchronized with eBay. This will also override the inventory value if you have set it on the settings page.", "applyToAllHelpText": "This setting will automatically apply to all products in this profile.", "overrideInventoryTextField": {"label": "Enter your quantity", "error": "Quantity cannot be 0", "description": "<0>Please note:</0> If the product quantity is configured as <1>{{overrideTypeName}}</1> on Shopify, the inventory value specified here will be synchronized with eBay. This will also override the inventory value if you have set it on the settings page."}}, "suggestedShopifyAttribute": "Suggested Shopify Attribute", "eBaySuggested": "eBay Suggested ", "syncSettingDescriptionSyncCheckboxLabel": "Sync product description if changed in Shopify ", "syncSettingPriceSyncCheckboxLabel": "Sync product price if changed in Shopify ", "syncSettingVariationSyncCheckboxLabel": "Sync variation if changed in Shopify ", "syncSettingImageSyncCheckboxLabel": "Sync images if changed in Shopify ", "syncSettings": "Shopify to eBay Sync Settings", "syncSettingTitleSyncCheckboxLabel": "Sync product title if changed in Shopify ", "syncSettingAutoTruncateTitleCheckboxLabel": "Auto truncate title if it exceeds eBay character limit", "titleAutoTruncateCheckboxLabel": "Auto Truncate Title to maximum limit available", "titleAutoTruncateDescription": "Length limit for title on eBay is 80 characters. Customize the title below or checkbox to auto truncate title to 80 characters.", "titleAutoTruncateTextFieldErrorMessage": "Maximum character length is 80", "titleSettingPrefixSuffixCheckboxLabel": "Add prefix/suffix to title while upload to eBay", "titleSettings": "Title Settings", "updateProfile": "Update Profile", "uploadFailedMessage": "Upload Failed. Please Try Again.", "viewProfile": "View Profile", "viewProfilePageEmptySearchResultDescription": "Try changing the filters or search term", "viewProfilePageEmptySearchResultTitle": "No Products yet", "viewProfilePageErrorResolvedToast": "Successfully resolved the error. Please check if there are any other errors.", "viewProfilePageRemoveProductActionLabel": {"one": "Remove {{count}} product from profile", "other": "Remove {{count}} products from profile"}, "viewProfilePageTitle": "Showing products for {{profileName}} profile", "viewProfileSettings": "View Profile Settings", "endEbayProductsWhileDeletingProfile": "End eBay products while deleting the profile", "confirmDeleteProfile": "Yes, Delete Profile", "eBayProfileDeleteMainMessage": "All your Shopify products that are in this profile will stop syncing to eBay if you delete this profile", "emptyStates": {"noUploadedProducts": {"heading": "No Uploaded Products Found", "action": "View Failed Products", "body": "Looks like you don't seem to have your Shopify products uploaded. View the errors"}}, "selectProfile": {"bannerDescription": "You do not have any profiles at the moment. Please create a profile and continue below.", "selectPlaceholder": "Select Profile"}, "itemLocation": {"country": "Please select a country for item location", "postCode": "Please update post code for item location", "city": "Please update city for item location"}, "vatSetting": {"title": "VAT percentage on item", "helpText": " This is the VAT percentage that will be applied on the item. Put \"0\" if you do not want to set VAT percentage."}, "aiSuggestions": {"title": "AI Suggested Product Title", "ask_upgrade_plan": "🚀 Upgrade your plan today to unlock AI suggestion ! "}, "duplicateListingViolation": {"description": "Duplicate Listing Detected: Modify title or use AI suggestion for a distinct title."}, "titleExceedingMaxLength": "Title exceeds maximum length"}, "viewProfile": {"removeProductFromProfileModalMessage": "If you remove the product from profile the link between eBay products will also get removed.", "priceErrorMessage": "Price should be greater than 0.99 {{currency}}", "priceResolveModalPriceTextFieldLabel": "Price in {{currency}}", "priceResolveModalInfoBannerDescription": "The Price of this product is not valid. It should be greater than 0.99 {{currency}}. Please enter a valid price so that this product will get uploaded to eBay. ", "weightErrorMessageForValueLessThanZero": "Package weight should be zero or greater than zero.", "weightErrorMessageForExceededLimit": "Package weight exceeds {{weight}}. Please switch to Freight shipping for large items over {{weight}}.", "weightResolveModalInfoBannerDescription": "The product weight entered is invalid. Please ensure the weight is greater than 0. If the product weight exceeds the limits for standard shipping methods, consider using freight shipping.", "weightResolveModalWeightTextFieldLabel": "Weight", "weightResolveModalWeightSelectFieldLabel": "Weight unit"}, "settings": {"currencySettings": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Specify the currency settings sync for your eBay store.", "currencySettingCurrencyHelpText": "This is the currency used in your Shopify Store", "ebayCurrencySettingCurrencyHelpText": "This is the currency used in your eBay Store", "syncShopifyCurrency": "Sync store currency", "syncShopifyCurrencyInProgress": "Syncing store currency ...", "syncShopifyCurrencyCompleted": "Store currency synced", "syncShopifyCurrencyFailed": "Failed to sync store currency"}, "dutch": "Dutch", "english": "English", "modal": {"primaryActionContent": "Turn off", "title": "Turn off eBay order id sync?", "body": "Your eBay order id will not be synced as shopify order id but as an order tag once you confirm.\nAre you sure you do not want to eBay order id as shopify order id?"}, "languageSwitcherLabel": "Select language for user interface", "settingsUpdatedSuccessfully": "Settings updated successfully", "updateSettings": "Update Settings", "userInterfaceSettings": "User Interface Settings", "productSyncSetting": {"title": "Product Sync Setting", "description": "Configure product tag synchronization and automatic SKU linking.", "tagSettings": {"title": "Import products to Shopify setting", "label": "Tag sync settings", "description": "Import eBay product's Shipping profile name as tag to Shopify Product"}, "syncSetting": {"title": "Sync Setting", "productLinkSetting": {"title": "Product Link Setting", "checkboxLabel": "Automatic link products based on SKUs only."}}}, "inventoryAndOrderSettings": {"title": "Order & Inventory Sync", "description": "Enable or disable order, inventory, & tax sync and set VAT settings.", "orderAndInventorySync": {"label": "Order & Inventory Sync", "title": "Select the appropriate order and inventory sync option for your Shopify and eBay stores", "choices": {"enableBoth": "Enable both Order & Inventory Sync", "enableInventoryOnly": "Enable Inventory Sync only", "disableBoth": "No Order & Inventory Sync"}}, "sharedSku": {"label": "Shared SKU inventory Sync", "title": "Check the option if you want to sync your inventory from shopify to eBay even if there are multiple items with same SKUs on eBay.", "CheckboxLabel": "Sync inventory to shared SKUs on eBay"}, "vatSync": {"CheckboxLabel": "Add custom exclusive tax while importing orders to shopify", "vatPercentagePlaceholder": "Enter Tax %", "vatPercentageLabel": "Tax % on eBay Products", "customTaxTitleLabel": "Custom Tax Title", "customTaxTitlePlaceholder": "Enter Custom Tax Title"}, "overrideUntrackedOrContinueSellingQty": {"typeName": "Not tracked or Continue selling when out of stock", "label": "Override Inventory Value", "description": "This is the default inventory value for eBay listings if your Shopify inventory is marked as <0>{{overrideTypeName}}</0>. You can set this value for eBay listings according to your requirements."}, "orderEmailSync": {"title": "Order Email Sync", "label": "Sync the buyer email address from eBay order to the Shopify order.", "helpText": "Enable this option to sync the buyer email address (Example: <EMAIL>) from eBay order to Shopify."}, "orderFailNotification": {"title": "Order Failed Notification", "label": "Enable Order Failed Notification", "helpText": "Please enable this option if you want to receive an email if any of your orders fail to sync."}, "syncShippingAsBillingAddress": {"title": "Sync Shipping Address as Billing Address", "label": "Sync the shipping address as billing address in Shopify orders", "helpText": "Enable this option to include the shipping address as billing address when syncing eBay orders to Shopify."}}, "generalSettings": {"title": "General Settings", "description": "Manage automatic SKU generation and end product listings on status change.", "autoSkuGeneration": {"label": "Automatic SKU Generation", "description": "Automatically generate SKU when if available while importing or uploading"}, "autoEndListing": {"label": "  Automatic End Listing", "description": "Automatically end listing when linked & active Shopify product is changed to draft/deleted/archive state?"}}, "shopifyOrderIdConfig": {"title": "Shopify Order ID Configurations", "shopifyOrderIdCheckboxLabel": "Set Prefix for Shopify Order ID", "enableEbayOrderIdSyncingCheckboxLabel": "Sync eBay order ID as Shopify order ID", "shopifyOrderIdTextBox": {"placeholder": "Set your desired prefix", "label": "Prefix"}}, "shopifyOrderTags": {"title": "Shopify Order Tags Configurations", "shopifyOrderIdCheckboxLabel": "Set Prefix for Shopify Order ID", "enableEbayOrderIdSyncingCheckboxLabel": "Sync eBay order ID as Shopify order ID", "serOrderTags": {"helpText": "To sync eBay order as tag add {ebay_order_id}. Example: eBay,{ebay_order_id}.", "label": "Set Shopify Order Tags", "placeholder": "Example: eBay,{ebay_order_id}"}}, "taxAndVatConfig": {"title": "Tax Settings", "syncEbayCollectedTax": "Sync tax collected by eBay to Shopify"}, "locationSettings": {"annotatedDescription": "Connect the locations where your eBay inventory is managed. This helps synchronize inventory across your connected locations.", "loading": "Loading locations...", "title": "Location Settings", "manageLocations": "Manage Locations", "activeLocations": "Connected Locations", "allLocationsActive": "All locations are connected", "specificLocationsActive": "{{count}} locations connected", "noLocationsSelected": "No locations connected. Please connect at least one location.", "selectLocationsTitle": "Select eBay Inventory Locations", "noLocations": "No locations found.", "selectAll": "Select all locations", "warning": "Please connect at least one location.", "availableLocations": "Available Locations", "description": "Important: Only inventory from connected locations will be synced to eBay. Make sure to choose locations carefully as this directly affects your eBay listing quantities."}}, "shopifyCollections": {"collection": "Collection", "collections": "Collections", "infoBannerDescription": "In this page you can select Shopify collections and upload the products within the selected collection to eBay.", "searchPlaceholder": "Search by collection name", "shopifyCollections": "Shopify Collections", "uploadSelectedCollection": "Upload {{count}} Selected Collection"}, "shopifyProducts": {"filters": {"collectionFilter": {"filterName": "Shopify collection", "searchPlaceHolder": "Search for collections", "showMoreCollections": "Show more collections", "selectionLabel": "Selected collection is {{label}}"}, "locationFilter": {"filterName": "Shopify location", "searchPlaceHolder": "Search for locations", "showMoreLocations": "Show more locations", "selectionLabel": "Selected location is {{label}}"}, "placeholder": "Search by Product SKU or Title"}, "openProfileTooltip": "View profile for this product", "metaFieldsSyncBanner": {"title": "Syncing your metafields", "description": "We are currently fetching the package dimensions from the selected Shopify product as you have selected the calculated shipping. The upload process will resume as soon as the metafields are fetched"}, "shopifyProductsBannerInfo": "This page displays the products in your Shopify store and you can upload these products to your eBay store.", "all": "All", "bulkCreateButtonLabel": {"one": "Upload {{count}} product on eBay", "other": "Upload {{count}} products on eBay"}, "uploadShopifyCollectionsBulk": "Upload products in bulk using collection", "bulkEndButtonLabel": {"one": "End {{count}} product on eBay", "other": "End {{count}} products on eBay"}, "emptyStateDescription": "Looks like you haven't uploaded your Shopify products to eBay yet. Please upload products from Shopify.", "emptyStateHeading": "Uploaded Products Not Found", "emptyStatePrimaryButtonLabel": "Upload Shopify Products", "endingShopifyProducts": "Ending eBay Products...", "endingShopifyProductsMessage": "Your eBay products uploaded from Shopify are being ending. Please wait for the process to complete.", "endOnEbay": "End on eBay", "endedOnEbay": "Ended on eBay", "endedOnEbayToolTip": "This linked eBay listing has been ended on eBay due to empty inventory in the Shopify product. Please update the quantity to relist the listing", "importingShopifyProducts": "Importing Shopify Products", "importingShopifyProductsMessage": "Your Shopify products are importing. Please wait for completion", "infoBannerDescription": "The following products are currently listed in your Shopify store. If you want to upload them to your eBay store, you have the choice to either manually select or bulk select them", "inventory": "Inventory", "perPage": "Per Page", "product": "Product", "products": "Products", "searchPlaceholder": "Search by Product SKU or Title", "shopifyProducts": "Shopify Products", "status": "Status", "uploadShopifyProductsToEbay": "Upload Shopify Products to eBay", "uploadingShopifyProducts": "Uploading Shopify Products...", "uploadingShopifyProductsMessage": "Your Shopify products are being uploading to eBay. Please wait for the process to complete.", "uploadShopifyCollection": "Upload Shopify Collection", "viewOn": "View On"}, "appRating": {"thanksForRatingText": "We have received your feedback.\n We are always striving to make our app better and your experience matters 😊 ", "ratingBannerTitle": "Love eBay Integration - DPL ? Your Opinion Counts !", "ratingBannerContent": "Whether you love us or feel we could be doing better, please take a moment to rate our app and share\n your experience with us.", "rateYourExperience": "Rate Your Experience with Us", "reviewModalTextFieldPlaceHolder": "Please provide us your feedback.", "reviewModalTitle": "Share your experience with us"}, "limitReached": {"orderSyncLimit": {"title": "Order sync limit reached", "message": "You have reached your monthly order sync limit. Upgrade now to seamlessly sync your eBay orders to Shopify without any interruption!", "cta": "Upgrade plan"}}, "support": {"title": "Get Support", "heading": "How can we help you?", "description": "We're here to help! Our support team is ready to assist you with any questions or issues you may have with the eBay Integration & Sync app.", "contactOptions": "Contact Options", "openChat": "Open Live Chat", "emailSupport": "Email Support", "chatInfo": {"title": "Live Chat Support", "description": "Get instant help through our live chat. Our support team is available to answer your questions and help resolve any issues quickly.", "availability": "Chat support is available during business hours (9 AM - 6 PM EST, Monday - Friday)"}, "emailInfo": {"title": "Email Support", "description": "Send us an <NAME_EMAIL> and we'll get back to you within 24 hours.", "responseTime": "We typically respond to emails within 2-4 hours during business hours."}, "footer": "For urgent issues, please use the live chat option for the fastest response."}}}