<?php

namespace App\Services\Common;

use DOMDocument;
use SimpleXMLElement;

/**
 * Utility class for XML operations
 */
class XmlUtils
{
    /**
     * Converts an XML string to an associative array
     *
     * @param string $xml The XML string to convert
     * @return array The resulting associative array
     */
public static function xmlToArray(string $xml): array
 {
    try {
        $xmlElement = new SimpleXMLElement($xml);
        return json_decode(json_encode($xmlElement), true);
    } catch (\Exception $e) {
        throw new \InvalidArgumentException('Invalid XML provided: ' . $e->getMessage());
    }
 }

    /**
     * Checks if a string is valid XML
     *
     * @param string $xml The XML string to validate
     * @return bool True if valid XML, false otherwise
     */
    public static function isValidXml(string $xml): bool
    {
        libxml_use_internal_errors(true);

        $doc = new DOMDocument();
        $isValid = $doc->loadXML($xml);
    
        libxml_clear_errors();
        libxml_use_internal_errors(false);
    
        return $isValid;
    }
}