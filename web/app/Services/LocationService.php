<?php

namespace App\Services;

use App\Models\Ebay\EbayUser;
use App\Models\Session;
use App\Module\Shopify\Services\Product\ShopifyProductSyncService;
use Illuminate\Support\Facades\Log;

class LocationService
{
    /**
     * @var ShopifyProductSyncService
     */
    protected ShopifyProductSyncService $productSyncService;

    /**
     * Create a new service instance.
     *
     * @param ShopifyProductSyncService $productSyncService
     * @return void
     */
    public function __construct(ShopifyProductSyncService $productSyncService)
    {
        $this->productSyncService = $productSyncService;
    }
    
    /**
     * Process location data changes and trigger appropriate actions
     *
     * @param EbayUser $ebayUser
     * @param mixed $selectedLocations
     * @return bool Whether the location data has changed
     */
    public function processLocationDataChanges(EbayUser $ebayUser): bool
    {
        // Location data has changed, trigger a full product sync
        $this->handleLocationChange($ebayUser);
        
        return true;
    }
    
    
    /**
     * Handle actions required when location changes
     *
     * @param EbayUser $ebayUser
     * @return void
     */
    private function handleLocationChange(EbayUser $ebayUser): void
    {
        Log::channel('daily')->info("Location data changed for ebay user", [
            'ebayUserId' => $ebayUser->id,
            'shopifySessionId' => $ebayUser->shopify_session_id
        ]);
        
        // Get the Session model
        $sessionModel = Session::where('session_id', $ebayUser->shopify_session_id)->first();
        
        if (!$sessionModel) {
            Log::channel('daily')->error("Session not found for shopify session ID: {$ebayUser->shopify_session_id}");
            return;
        }
        
        // Trigger full sync of products using the dedicated service
        $this->productSyncService->triggerFullSync($sessionModel, 'Location data changed');
    }
} 