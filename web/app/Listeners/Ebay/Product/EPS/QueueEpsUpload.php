<?php

namespace App\Listeners\Ebay\Product\EPS;

use App\Events\Product\EbayProductUpdated;
use App\Events\Product\EPS\EpsUploadCompleted;
use App\Jobs\Ebay\EPS\UploadImageToEps;
use Illuminate\Support\Facades\Log;
use App\Models\ShopifyProduct;
use Illuminate\Support\Facades\Bus;
use App\Module\Ebay\Services\Product\Formatter\EbayItemRequestFormatter;

class QueueEpsUpload
{
    public function __construct(
        private readonly EbayItemRequestFormatter $ebayItemRequestFormatter
    ) {}

    public function handle(EbayProductUpdated $event): void 
    {
        /** @var ShopifyProduct $shopifyProduct */
        $shopifyProduct = $event->shopifyProduct;
        $shopifySessionId = $shopifyProduct->shopify_session_id;
        $shopifyProductId = $shopifyProduct->id;
        $platformShopifyProductId = $shopifyProduct->shopify_product_id;
        $ebayProductId = $shopifyProduct->ebay_product_id;

        Log::channel('daily')->info('QueueEpsUpload : started for shopify product', [
            'shopify_session_id' => $shopifySessionId,
            'shopify_product_id' => $shopifyProductId,
            'platform_shopify_product_id' => $platformShopifyProductId,
            'ebay_item_id' => $ebayProductId,
        ]);


        $shopifyProduct->load('images');

        $epsUploadJobs = [];
        $shopifyImageIds = $this->getShopifyProductImagesIdsForEpsUpload($shopifyProduct);
        foreach ($shopifyImageIds as $shopifyImageId) {
            $epsUploadJobs[] = new UploadImageToEps($shopifyProduct->session_id, $shopifyProductId, $shopifyImageId);
        }

        if (count($epsUploadJobs) === 0) {
            Log::channel('daily')->error('QueueEpsUpload : no images found for Shopify product', [
                'shopify_session_id' => $shopifySessionId,
                'shopify_product_id' => $shopifyProductId,
                'platform_shopify_product_id' => $platformShopifyProductId,
                'ebay_item_id' => $ebayProductId,
            ]);
            return;
        }

        Bus::chain([
            ...$epsUploadJobs,
            function () use (
                $shopifyProductId, 
                $shopifySessionId, 
                $ebayProductId, 
                $platformShopifyProductId
                ) {
                    Log::channel('daily')->info('QueueEpsUpload : completed for shopify product', [
                        'shopify_session_id' => $shopifySessionId,
                        'shopify_product_id' => $shopifyProductId,
                        'platform_shopify_product_id' => $platformShopifyProductId,
                        'ebay_item_id' => $ebayProductId,
                    ]);
            
                    event(new EpsUploadCompleted(
                        $shopifySessionId,
                        $shopifyProductId,
                        $platformShopifyProductId
                    ));
            }
        ])
        ->onQueue('eps_upload')
        ->catch(function (\Throwable $e) use (
              $shopifySessionId,
              $shopifyProductId,
              $ebayProductId,
              $platformShopifyProductId
            ) {
                Log::channel('daily')->error(
                    'QueueEpsUpload : error in uploading shopify product image to eps',
                    [
                        'shopify_session_id' => $shopifySessionId,
                        'shopify_product_id'  => $shopifyProductId,
                        'platform_shopify_product_id' => $platformShopifyProductId,
                        'ebay_item_id'        => $ebayProductId,
                        'error'               => $e->getMessage(),
                        'file'                => $e->getFile(),
                        'line'                => $e->getLine(),
                    ]
                );
         })
        ->dispatch();

        Log::channel('daily')->info('QueueEpsUpload : job dispatched for shopify product', [
            'shopify_session_id' => $shopifySessionId,
            'shopify_product_id' => $shopifyProductId,
            'platform_shopify_product_id' => $platformShopifyProductId,
            'ebay_item_id' => $ebayProductId,
        ]);

    }

    private function getShopifyProductImagesIdsForEpsUpload(ShopifyProduct $shopifyProduct): array
    {
        $imageSyncBasedOnFirstOption = $shopifyProduct->session?->image_sync_based_on_first_option ?? false;

        $imageData = $imageSyncBasedOnFirstOption
            ? $this->ebayItemRequestFormatter->getImagesBasedOnFirstOption($shopifyProduct)
            : $this->ebayItemRequestFormatter->getImages($shopifyProduct);


        $mainImageIds = array_keys($imageData['main_image'] ?? []);
        $pictureGalleryIds = array_keys($imageData['picture_gallery_image_id_url_map'] ?? []);
        $variantsImageIds = array_keys($imageData['variants_image_id_to_option_url_map'] ?? []);

        $imageIds = array_unique([
            ...$mainImageIds,
            ...$pictureGalleryIds,
            ...$variantsImageIds
        ]);

        return $imageIds;
    }


    
} 