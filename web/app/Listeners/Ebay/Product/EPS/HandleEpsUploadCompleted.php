<?php

namespace App\Listeners\Ebay\Product\EPS;

use App\Events\Product\EPS\EpsUploadCompleted;
use Illuminate\Support\Facades\Log;
use App\Models\ShopifyProduct;
use App\Module\Ebay\Services\Product\Formatter\EbayItemRequestFormatter;
use App\Module\Ebay\Services\Product\EPS\ShopifyEbayImageMappingService;
use DTS\eBaySDK\Trading\Types\ReviseFixedPriceItemRequestType;
use DTS\eBaySDK\Trading\Types\ItemType;
use DTS\eBaySDK\Trading\Types\PictureDetailsType;
use DTS\eBaySDK\Trading\Types\VariationsType;
use DTS\eBaySDK\Trading\Types\PicturesType;
use DTS\eBaySDK\Trading\Types\VariationSpecificPictureSetType;
use App\Module\Ebay\Services\Product\Builder\EbayItemRequestBuilder;
use App\Module\Ebay\Services\Product\EPS\EpsImageValidationService;
use Illuminate\Support\Collection;
use DTS\eBaySDK\Trading\Enums\GalleryTypeCodeType;
use DTS\eBaySDK\Trading\Services\TradingService;
use App\Module\Ebay\Helper\Helper;
use Illuminate\Support\Arr;

class HandleEpsUploadCompleted
{

    public function __construct(
        private readonly EbayItemRequestFormatter $ebayItemRequestFormatter,
        private readonly EbayItemRequestBuilder $ebayItemRequestBuilder,
        private readonly ShopifyEbayImageMappingService $shopifyEbayImageMappingService,
        private readonly EpsImageValidationService $epsImageValidationService
    ) {}

    public function handle(
        EpsUploadCompleted $event
    ):void {

        $shopifyProductId = $event->shopifyProductId;
        $platformShopifyProductId = $event->platformShopifyProductId;
        $shopifySessionId = $event->shopifySessionId;

        Log::channel('daily')->info('HandleEpsUploadCompleted : started handling for shopify product', [
            'shopify_session_id' => $shopifySessionId,
            'shopify_product_id' => $shopifyProductId,
            'platform_shopify_product_id' => $platformShopifyProductId,
        ]);

        $shopifyProduct = ShopifyProduct::find($shopifyProductId);

        if (!$shopifyProduct) {
            Log::channel('daily')->error('HandleEpsUploadCompleted : shopify product not found', [
                'shopify_session_id' => $shopifySessionId,
                'shopify_product_id' => $shopifyProductId,
                'platform_shopify_product_id' => $platformShopifyProductId
            ]);
            return;
        }

        if(!$shopifyProduct->ebay_product_id){
           Log::channel('daily')->error('HandleEpsUploadCompleted : ebay product id not associated with shopify product', [
            'shopify_session_id' => $shopifyProduct->shopify_session_id,
            'shopify_product_id' => $shopifyProductId,
            'ebay_product_id' => $shopifyProduct->ebay_product_id,
            'platform_shopify_product_id' => $shopifyProduct->shopify_product_id,
           ]);
           return;
        }

        

        $ebayUser = $shopifyProduct->session?->ebayUser;

        if (!$ebayUser) {
            Log::channel('daily')->error('HandleEpsUploadCompleted : ebay user not found', [
                'shopify_session_id' => $shopifyProduct->shopify_session_id,
                'shopify_product_id' => $shopifyProductId,
                'platform_shopify_product_id' => $shopifyProduct->shopify_product_id,
            ]);
            return;
        }

        // Get Shopify images for revision
        $shopifyImages = $this->getShopifyImages($shopifyProduct);

        $variantImageIdUrlMap = [];

        foreach ($shopifyImages['variants_image_id_to_option_url_map'] as $variantImageId => $optionUrlMap) {
            // Extract the first (and typically only) URL from the option map based on the fact : there's exactly one URL per option
            $variantImageIdUrlMap[$variantImageId] = Arr::first($optionUrlMap);
        }

        $shopifyImageIdUrlMappings = 
            ($shopifyImages['main_image'] ?? []) 
            + ($shopifyImages['picture_gallery_image_id_url_map'] ?? [])
            + $variantImageIdUrlMap;

        $shopifyImageIds = array_unique(array_keys($shopifyImageIdUrlMappings));
       
        $shopifyEbayImageMappings = $this->shopifyEbayImageMappingService->getValidMappings($shopifyImageIds);

        // Build EPS URL mappings with validation
        $imageMappings = $this->buildValidEpsImageMappings(
            $shopifyImageIdUrlMappings, 
            $shopifyEbayImageMappings
        );

        if (empty($imageMappings)) {
            Log::channel('daily')->info('HandleEpsUploadCompleted : No valid image mappings found for EPS upload completion', [
                'shopify_session_id' => $shopifyProduct->shopify_session_id,
                'shopify_product_id' => $shopifyProductId,
                'ebay_product_id' => $shopifyProduct->ebay_product_id,
                'platform_shopify_product_id' => $shopifyProduct->shopify_product_id,
            ]);
            return;
        }

        // Build picture revision request
        $revisionRequest = $this->buildPictureRevisionRequest(
            $shopifyProduct, 
            $shopifyImages,
            $imageMappings
        );

        // Skip API call if no meaningful updates to make
        if ($revisionRequest === null) {
            Log::channel('daily')->info('HandleEpsUploadCompleted : No picture updates needed, skipping API call', [
                'shopify_session_id' => $shopifyProduct->shopify_session_id,
                'shopify_product_id' => $shopifyProductId,
                'ebay_product_id' => $shopifyProduct->ebay_product_id,
                'platform_shopify_product_id' => $shopifyProduct->shopify_product_id,
                'revision_request' => $revisionRequest
            ]);
            return;
        }

         // log the shopify image id url mappings
         Log::channel('daily')->info('HandleEpsUploadCompleted : shopify image id url mappings', [
            'shopify_session_id' => $shopifyProduct->shopify_session_id,
            'shopify_product_id' => $shopifyProductId,
            'ebay_product_id' => $shopifyProduct->ebay_product_id,
            'platform_shopify_product_id' => $shopifyProduct->shopify_product_id,
            'shopify_image_id_url_mappings' => $shopifyImageIdUrlMappings,
            'shopify_ebay_image_mappings' => $shopifyEbayImageMappings,
            'image_mappings' => $imageMappings,
            'revision_request' => $revisionRequest
        ]);
        
        $service = new TradingService(Helper::eBayCredentials($ebayUser));
        $response = $service->reviseFixedPriceItem($revisionRequest);

        if (isset($response->Errors)) {
            Log::channel('daily')
                ->info(
                    "HandleEpsUploadCompleted : Error while trying to revise fixed price item:",
                    [
                        'shopify_session_id' => $shopifyProduct->shopify_session_id,
                        'shopify_product_id' => $shopifyProductId,
                        'ebay_product_id' => $shopifyProduct->ebay_product_id,
                        'platform_shopify_product_id' => $shopifyProduct->shopify_product_id,
                        'response' => $response,
                    ]
                );
            return;
        }

        Log::channel('daily')->info('HandleEpsUploadCompleted : completed handling for shopify product', [
            'shopify_session_id' => $shopifyProduct->shopify_session_id,
            'shopify_product_id' => $shopifyProductId,
            'ebay_product_id' => $shopifyProduct->ebay_product_id,
            'platform_shopify_product_id' => $shopifyProduct->shopify_product_id,
            'valid_images_count' => count($imageMappings)
        ]);
    }

    private function getShopifyImages(ShopifyProduct $shopifyProduct): array
    {
        $imageSyncBasedOnFirstOption = $shopifyProduct->session?->image_sync_based_on_first_option ?? false;

        return $imageSyncBasedOnFirstOption
            ? $this->ebayItemRequestFormatter->getImagesBasedOnFirstOption($shopifyProduct)
            : $this->ebayItemRequestFormatter->getImages($shopifyProduct);
    }

    /**
     * Build validated EPS image mappings
     */
    private function buildValidEpsImageMappings(
        array $shopifyImageIdUrlMappings,
        Collection $shopifyEbayImageMappings
    ): array {
        $mappings = [];
        foreach ($shopifyImageIdUrlMappings as $shopifyImageId => $shopifyImageUrl) {
            $shopifyEbayImageMapping = $shopifyEbayImageMappings->firstWhere('shopify_image_id', $shopifyImageId);
            $epsImageUrl = $shopifyEbayImageMapping?->ebay_eps_url;

            $mappings[$shopifyImageId] = $shopifyImageUrl;

            if($epsImageUrl){
                $imageValidationResult = $this->epsImageValidationService->validateImage($epsImageUrl);
                if (!$imageValidationResult->isValid){
                    Log::channel('daily')->warning('HandleEpsUploadCompleted : EPS image validation failed', [
                        'shopify_image_id' => $shopifyImageId,
                        'eps_url' => $epsImageUrl,
                        'validation_errors' => $imageValidationResult->errors ?? []
                        ]);

                    continue;
                }
                $mappings[$shopifyImageId] = $epsImageUrl;
            }
        }

        return $mappings;
    }

    /**
     * Build picture revision request for eBay API
     * Returns null if no meaningful updates are needed
     */
    private function buildPictureRevisionRequest(
        ShopifyProduct $shopifyProduct,
        array $shopifyImages,
        array $imageMappings
    ): ?ReviseFixedPriceItemRequestType {
        
        $item = new ItemType();
        $item->ItemID = $shopifyProduct->ebay_product_id;



        // Build main picture details
        $pictureDetails = $this->buildPictureDetails(
            $shopifyImages, 
            $imageMappings,
            $shopifyProduct            
        );
        
        // Only include PictureDetails if there are actual eps mapped gallery images
        if ($pictureDetails !== null) {
            $item->PictureDetails = $pictureDetails;
        }

        // Build variation pictures for variable products
        if ($shopifyProduct->isVariable) {
            $variationPictures = $this->buildVariationPictures(
                $shopifyImages, 
                $imageMappings,
                $shopifyProduct
            );

            // Only include "Variations" if there are actual EPS-mapped variation pictures
            if ($variationPictures !== null) {
                $item->Variations = $variationPictures;
            }
        }

        // Return null if no meaningful updates to make
        if (!isset($item->PictureDetails) && !isset($item->Variations)) {
            return null;
        }

        $request = new ReviseFixedPriceItemRequestType();
        $request->Item = $item;

        return $request;
    }

    /**
     * Build PictureDetails for main product images
     * Returns null if no valid images are found
     */
    private function buildPictureDetails(
        array $shopifyImages,
        array $imageMappings,
        ShopifyProduct $shopifyProduct
    ): ?PictureDetailsType {
        // Filter out variant images to get gallery images
        $variantImageIds = array_keys($shopifyImages['variants_image_id_to_option_url_map'] ?? []);
        $galleryImageUrls = array_values(array_unique(array_filter(
            $imageMappings,
            fn($imageId) => !in_array($imageId, $variantImageIds),
            ARRAY_FILTER_USE_KEY
        )));

        // Fallback to main image if no gallery images
        if (empty($galleryImageUrls)) {
            $mainImageId = array_key_first($shopifyImages['main_image'] ?? []);
            if ($mainImageId && isset($imageMappings[$mainImageId])) {
                $galleryImageUrls = [$imageMappings[$mainImageId]];
            }
        }

        if (empty($galleryImageUrls)) {
            Log::channel('daily')->info('HandleEpsUploadCompleted : buildPictureDetails : No gallery images', [
                'shopify_session_id' => $shopifyProduct->shopify_session_id,
                'shopify_product_id' => $shopifyProduct->id,
                'platform_shopify_product_id' => $shopifyProduct->shopify_product_id,
            ]);
            return null;
        }

        $pictureDetails = new PictureDetailsType();
        $pictureDetails->PictureURL = $galleryImageUrls;
        $pictureDetails->GalleryType = GalleryTypeCodeType::C_GALLERY;

        return $pictureDetails;
    }

    /**
     * Build variation pictures for variable products
     * Returns null if no valid EPS-mapped variation pictures are found
     */
    private function buildVariationPictures(
        array $shopifyImages,
        array $imageMappings,
        ShopifyProduct $shopifyProduct
    ): ?VariationsType {
        
        // Get variation option details
        $variationSpecificOptionName = $shopifyImages['variants_option_name'] ?? '';
        $variationImageIdToOptionUrlMap = $shopifyImages['variants_image_id_to_option_url_map'] ?? [];

        // Return null if no variation data is available
        if (empty($variationSpecificOptionName) || empty($variationImageIdToOptionUrlMap)) {
            Log::channel('daily')->info('HandleEpsUploadCompleted : buildVariationPictures : No variation data available', [
                'shopify_session_id' => $shopifyProduct->shopify_session_id,
                'shopify_product_id' => $shopifyProduct->id,
                'platform_shopify_product_id' => $shopifyProduct->shopify_product_id,
                'variation_option_name' => $variationSpecificOptionName,
                'has_variation_images' => !empty($variationImageIdToOptionUrlMap)
            ]);
            return null;
        }

        $variationSpecificPictureSets = [];

        foreach ($variationImageIdToOptionUrlMap as $variantImageId => $optionUrlMap) {
            // Skip if no EPS mapping exists for this variant image
            if (!isset($imageMappings[$variantImageId])) {
                Log::channel('daily')->info('HandleEpsUploadCompleted : buildVariationPictures : variation image id not found in EPS mappings', [
                    'shopify_session_id' => $shopifyProduct->shopify_session_id,
                    'shopify_product_id' => $shopifyProduct->id,
                    'platform_shopify_product_id' => $shopifyProduct->shopify_product_id,
                    'variant_image_id' => $variantImageId
                ]);
                continue;
            }

            $pictureSet = new VariationSpecificPictureSetType();
            // Get the first (and only) option value
            $optionValue = array_key_first($optionUrlMap);
            $pictureSet->VariationSpecificValue = (string)$optionValue;
            $pictureSet->PictureURL = [$imageMappings[$variantImageId]];
            $variationSpecificPictureSets[] = $pictureSet;
        }

        // Return null if no valid EPS-mapped variation pictures were found
        if (empty($variationSpecificPictureSets)) {
            Log::channel('daily')->info('HandleEpsUploadCompleted : buildVariationPictures : No valid EPS-mapped variation pictures found',[
                'shopify_session_id' => $shopifyProduct->shopify_session_id,
                'shopify_product_id' => $shopifyProduct->id,
                'platform_shopify_product_id' => $shopifyProduct->shopify_product_id,
            ]);
            return null;
        }

        // Build variations with pictures
        $variations = new VariationsType();
        $pictures = new PicturesType();
        $pictures->VariationSpecificName = $variationSpecificOptionName;
        $pictures->VariationSpecificPictureSet = $variationSpecificPictureSets;
        $variations->Pictures = [$pictures];

        return $variations;
    }
}