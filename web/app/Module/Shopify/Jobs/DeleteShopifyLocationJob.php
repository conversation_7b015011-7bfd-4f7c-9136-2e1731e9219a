<?php

namespace App\Module\Shopify\Jobs;

use App\Models\Ebay\EbayUser;
use App\Models\Session;
use App\Models\ShopifyLocation;
use App\Models\ShopifyInventoryLevel;
use App\Models\ShopifyProductLocation;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class DeleteShopifyLocationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The shop domain
     *
     * @var string
     */
    protected $shop;

    /**
     * The Shopify location ID
     *
     * @var string|int
     */
    protected $locationId;

    /**
     * Create a new job instance.
     *
     * @param string $shop
     * @param string|int $locationId
     * @return void
     */
    public function __construct(string $shop, $locationId)
    {
        $this->shop = $shop;
        $this->locationId = $locationId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            Log::channel('daily')->info('Starting DeleteShopifyLocationJob', [
                'shop' => $this->shop,
                'locationId' => $this->locationId
            ]);

            // Find the session associated with the shop
            $session = Session::where('shop', $this->shop)->first();
            
            if (!$session) {
                Log::channel('daily')->warning('Session not found for shop', [
                    'shop' => $this->shop
                ]);
                return;
            }

            // Find the location
            $location = ShopifyLocation::where('session_id', $session->id)
                ->where('shopify_location_id', $this->locationId)
                ->first();

            if (!$location) {
                Log::channel('daily')->warning('ShopifyLocation not found for deletion', [
                    'shop' => $this->shop,
                    'sessionId' => $session->id,
                    'locationId' => $this->locationId
                ]);
                return;
            }

            // Delete inventory levels in chunks to avoid memory issues
            $chunkSize = 1000;
            $totalDeleted = 0;

            $query = ShopifyInventoryLevel::where('session_id', $session->id)
                ->where('shopify_location_id', $this->locationId);
            
            $totalCount = $query->count();
            
            Log::channel('daily')->info('Starting deletion of inventory levels', [
                'shop' => $this->shop,
                'locationId' => $this->locationId,
                'totalInventoryLevels' => $totalCount
            ]);

            // Use chunk for efficient processing of large datasets
            $query->chunkById($chunkSize, function ($inventoryLevels) use (&$totalDeleted) {
                foreach ($inventoryLevels as $inventoryLevel) {
                    $inventoryLevel->delete();
                    $totalDeleted++;
                }
                
                Log::channel('daily')->info('Deleted chunk of inventory levels', [
                    'shop' => $this->shop,
                    'locationId' => $this->locationId,
                    'chunkSize' => count($inventoryLevels),
                    'totalDeletedSoFar' => $totalDeleted
                ]);
            });

            // Update EbayUserSettings to remove this location from selected_locations arrays
            $this->removeLocationFromUserSettings($session->id, $this->locationId);

             // Delete the location from the product locations lookup table
             ShopifyProductLocation::query()
             ->where('session_id', $session->id)
             ->where('location_id', $this->locationId)
             ->chunkById($chunkSize, function ($productLocations) {
                 foreach ($productLocations as $productLocation) {
                     $productLocation->delete();
                 }
             });
            
            // Now delete the location itself
            $location->delete();
            
            Log::channel('daily')->info('Successfully completed DeleteShopifyLocationJob', [
                'shop' => $this->shop,
                'locationId' => $this->locationId,
                'totalInventoryLevelsDeleted' => $totalDeleted
            ]);
            
        } catch (\Exception $e) {
            Log::channel('daily')->error('Error in DeleteShopifyLocationJob', [
                'shop' => $this->shop,
                'locationId' => $this->locationId,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            
            // Rethrow the exception to trigger job failure
            throw $e;
        }
    }

    /**
     * Remove location ID from EbayUserSetting record that has it in selected_locations
     *
     * @param int $sessionId
     * @param int $locationId
     * @return void
     */
    public function removeLocationFromUserSettings(int $sessionId, int $locationId): void
    {
        try {
            $ebayUser = EbayUser::where('session_id', $sessionId)->first();
            
            if (!$ebayUser || !$ebayUser->userSetting) {
                return;
            }

            $selectedLocations = $ebayUser->userSetting->selected_locations ?? [];
            if (empty($selectedLocations)) {
                return;
            }

            // Filter out the deleted location from selected array (whether present or not)
            $filteredSelected = collect($selectedLocations)
                ->filter(fn($id) => $id != $locationId)
                ->values()
                ->toArray();

            // Check if there are any locations NOT in the filtered selection
            $unselectedCount = ShopifyLocation::where('session_id', $sessionId)
                ->where('shopify_location_id', '!=', $locationId) // Exclude deleted location
                ->whereNotIn('shopify_location_id', $filteredSelected)
                ->count();

            // If no unselected locations exist, all remaining are selected (set to null)
            $finalSelected = ($unselectedCount === 0) ? null : $filteredSelected;

            $ebayUser->userSetting->update(['selected_locations' => $finalSelected]);

            Log::channel('daily')->info('Updated user settings after location deletion', [
                'shop' => $this->shop,
                'locationId' => $locationId,
                'setToNull' => $finalSelected === null
            ]);

        } catch (\Exception $e) {
            Log::channel('daily')->error('Error removing location from user settings', [
                'sessionId' => $sessionId,
                'locationId' => $locationId,
                'message' => $e->getMessage()
            ]);
        }
    }
} 