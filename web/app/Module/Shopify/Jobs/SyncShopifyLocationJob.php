<?php

declare(strict_types=1);

namespace App\Module\Shopify\Jobs;

use App\Jobs\Notifications\SendLocationDeactivatedEmailJob;
use App\Models\Ebay\EbayUserSetting;
use App\Models\Session;
use App\Models\ShopifyLocation;
use App\Repositories\ShopifyLocationRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Models\Ebay\EbayUser;

class SyncShopifyLocationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The shop domain
     *
     * @var string
     */
    protected string $shop;

    /**
     * The location data from webhook
     *
     * @var array
     */
    protected array $locationData;

    /**
     * The event type (create or update)
     *
     * @var string
     */
    protected string $eventType;

    /**
     * Create a new job instance.
     *
     * @param string $shop The shop domain
     * @param array $locationData The location data from webhook
     * @param string $eventType The event type (create or update)
     * @return void
     */
    public function __construct(string $shop, array $locationData, string $eventType = 'update')
    {
        $this->shop = $shop;
        $this->locationData = $locationData;
        $this->eventType = $eventType;
    }

    /**
     * Execute the job.
     *
     * @param ShopifyLocationRepository $locationRepository
     * @return void
     */
    public function handle(ShopifyLocationRepository $locationRepository): void
    {
        try {
            // Find shop by domain
            $session = Session::where('shop', $this->shop)->first();
            
            if (!$session) {
                Log::channel('daily')->error("Shop not found for location {$this->eventType} job", [
                    'shop' => $this->shop
                ]);
                return;
            }
            
            // Process location using the repository, with isWebhook flag set to true
            $location = $locationRepository->processLocation($this->locationData, $session, true);
            
            // Handle location deactivation if applicable
            if ($this->isLocationDeactivated()) {
                $this->handleLocationDeactivation($session);
            }
            
            Log::channel('daily')->info("Location successfully {$this->eventType} from job", [
                'shop' => $this->shop,
                'location_id' => $this->locationData['id'],
                'location_name' => $location->name,
                'event_type' => $this->eventType
            ]);
        } catch (\Exception $e) {
            Log::channel('daily')->error(
                "Exception processing location {$this->eventType} job",
                [
                    'shop' => $this->shop,
                    'data' => $this->locationData,
                    'event_type' => $this->eventType,
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            );
            
            // Re-throw the exception to trigger the failed job handler if configured
            throw $e;
        }
    }

    /**
     * Check if the location is being deactivated
     */
    private function isLocationDeactivated(): bool
    {
        return isset($this->locationData['active']) && $this->locationData['active'] === false;
    }

    /**
     * Handle location deactivation by applying smart fallback logic and sending notifications
     */
    private function handleLocationDeactivation(Session $session): void
    {
        $locationId = (string)($this->locationData['id'] ?? '');
        
        Log::channel('daily')->info('Processing location deactivation with smart fallback logic', [
            'shop' => $this->shop,
            'location_id' => $locationId,
            'location_name' => $this->locationData['name'] ?? 'Unknown'
        ]);

        // Get the affected EbayUserSetting (only one per shop)
        $ebayUserSetting = $this->getAffectedEbayUserSetting($locationId, $session);
        
        if (!$ebayUserSetting) {
            Log::channel('daily')->info('No EbayUserSetting affected by location deactivation', [
                'shop' => $this->shop,
                'location_id' => $locationId
            ]);
            return;
        }

        // Apply smart fallback logic
        $actionTaken = $this->applySmartLocationFallback($ebayUserSetting, $locationId, $session);
        
        // Only dispatch email notification if fallback was applied
        if ($actionTaken === 'fallback_applied') {
            $this->dispatchDeactivationEmail($ebayUserSetting, $session);
        }
    }

    /**
     * Get the EbayUserSetting that has the deactivated location in selected_locations
     */
    private function getAffectedEbayUserSetting(string $locationId, Session $session): ?EbayUserSetting
    {
        // Find the EbayUser for this shop (one-to-one relationship)
        $ebayUser = EbayUser::where('session_id', $session->id)->first();
        
        if (!$ebayUser) {
            return null;
        }
        
        // Convert location ID to integer for JSON query (database stores integers)
        $locationIdInt = (int)$locationId;
        
        // Find EbayUserSetting that belongs to this user and has the deactivated location
        return EbayUserSetting::where('ebay_user_id', $ebayUser->id)
            ->whereJsonContains('selected_locations', $locationIdInt)
            ->first();
    }

    /**
     * Apply smart fallback logic to an EbayUserSetting
     * Returns the action taken: 'no_change', 'fallback_applied'
     */
    private function applySmartLocationFallback(EbayUserSetting $setting, string $locationId, Session $session): string
    {
        $originalLocations = $setting->selected_locations;
        
        if ($originalLocations === null) {
            // This shouldn't happen as we only query settings with the deactivated location
            // But if selected_locations is null, no change needed
            Log::channel('daily')->info('EbayUserSetting uses all locations - no change needed', [
                'shop' => $this->shop,
                'setting_id' => $setting->id,
                'deactivated_location_id' => $locationId
            ]);
            return 'no_change';
        }

        if (!is_array($originalLocations)) {
            Log::channel('daily')->warning('EbayUserSetting selected_locations is not an array', [
                'shop' => $this->shop,
                'setting_id' => $setting->id,
                'selected_locations' => $originalLocations
            ]);
            return 'no_change';
        }

        // Check how many other active locations remain in the selection
        $remainingActiveLocations = $this->getActiveLocationsFromSelection($originalLocations, $locationId, $session);
        
        if (count($remainingActiveLocations) > 0) {
            // User has other active locations selected - keep the selection as is
            Log::channel('daily')->info('Other active locations remain selected - no fallback needed', [
                'shop' => $this->shop,
                'setting_id' => $setting->id,
                'deactivated_location_id' => $locationId,
                'remaining_active_locations' => $remainingActiveLocations,
                'action' => 'no_change'
            ]);
            return 'no_change';
        }

        // No other active locations remain - apply fallback to use all locations
        $setting->selected_locations = null;
        $setting->save();
        
        Log::channel('daily')->info('No active locations remain in selection - fallback applied', [
            'shop' => $this->shop,
            'setting_id' => $setting->id,
            'deactivated_location_id' => $locationId,
            'original_locations' => $originalLocations,
            'action' => 'fallback_applied'
        ]);
        
        return 'fallback_applied';
    }

    /**
     * Get active locations from the user's selection (excluding the deactivated one)
     */
    private function getActiveLocationsFromSelection(array $selectedLocations, string $deactivatedLocationId, Session $session): array
    {
        // Convert deactivated location ID to integer for comparison
        $deactivatedLocationIdInt = (int)$deactivatedLocationId;
        
        // Remove the deactivated location from the list
        $otherSelectedLocations = array_filter($selectedLocations, function ($id) use ($deactivatedLocationIdInt) {
            return (int)$id !== $deactivatedLocationIdInt;
        });

        if (empty($otherSelectedLocations)) {
            return [];
        }

        // Query database to check which of the remaining locations are still active
        $activeLocations = ShopifyLocation::where('session_id', $session->id)
            ->whereIn('shopify_location_id', $otherSelectedLocations)
            ->where('active', true)
            ->pluck('shopify_location_id')
            ->toArray();

        return $activeLocations;
    }

    /**
     * Dispatch appropriate email notification based on action taken
     */
    private function dispatchDeactivationEmail(EbayUserSetting $setting, Session $session): void
    {
        try {
            SendLocationDeactivatedEmailJob::dispatch(
                $this->shop,
                $this->locationData,
                $setting->ebay_user_id
            )->onConnection('redis');
            
            Log::channel('daily')->info('Location deactivation email job dispatched', [
                'shop' => $this->shop,
                'location_id' => $this->locationData['id'] ?? 'unknown',
                'action_taken' => 'fallback_applied'
            ]);
        } catch (\Exception $e) {
            Log::channel('daily')->error('Failed to dispatch location deactivation email', [
                'shop' => $this->shop,
                'location_id' => $this->locationData['id'] ?? 'unknown',
                'action_taken' => 'fallback_applied',
                'message' => $e->getMessage()
            ]);
        }
    }
} 