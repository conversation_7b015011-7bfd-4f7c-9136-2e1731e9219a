<?php

namespace App\Module\Shopify\Helper;

class ShopifyGidHelper
{
    /**
     * Extract numeric ID from Shopify's GID format
     *
     * @param string $gid The GID format ID (e.g., 'gid://shopify/InventoryLevel/100804591797')
     * @return int|null The extracted numeric ID or null if invalid format
     */
    public static function extractNumericId(string $gid): ?int
    {
        if (preg_match('/\/(\d+)(?:\?.*)?$/', $gid, $matches)) {
            return (int) $matches[1];
        }
        return null;
    }

    /**
     * Validate if a string is a valid Shopify GID format
     *
     * @param string $gid The GID to validate
     * @return bool True if valid GID format, false otherwise
     */
    public static function isValidGid(string $gid): bool
    {
        return (bool) preg_match('/^gid:\/\/shopify\/[A-Za-z]+\/\d+(?:\?.*)?$/', $gid);
    }

    /**
     * Get the resource type from a Shopify GID
     *
     * @param string $gid The GID format ID (e.g., 'gid://shopify/InventoryLevel/100804591797')
     * @return string|null The resource type or null if invalid format
     */
    public static function getResourceType(string $gid): ?string
    {
        if (preg_match('/^gid:\/\/shopify\/([A-Za-z]+)\/\d+(?:\?.*)?$/', $gid, $matches)) {
            return $matches[1];
        }
        return null;
    }
} 