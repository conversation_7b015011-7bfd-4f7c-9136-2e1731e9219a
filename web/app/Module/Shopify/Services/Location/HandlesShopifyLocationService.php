<?php

namespace App\Module\Shopify\Services\Location;

use App\Repositories\ShopifyLocationRepository;
use dpl\ShopifySync\Contracts\HandlesShopifyLocationSync;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class HandlesShopifyLocationService implements HandlesShopifyLocationSync
{
    private ShopifyLocationRepository $locationRepository;

    public function __construct(ShopifyLocationRepository $locationRepository)
    {
        $this->locationRepository = $locationRepository;
    }

    public function handle($shop, array $data, array $metadata = []): void
    {
        try {
            foreach (Arr::first($data) as $locationData) {
                $this->locationRepository->processLocation($locationData['node'], $shop);
            }
            Log::channel('daily')->info('Shopify locations synced successfully', ['shop' => $shop]);
        } catch (\Exception $e) {
            Log::channel('daily')->error('Error syncing Shopify locations', [
                'shop' => $shop,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}