<?php

namespace App\Module\Shopify\Services\Product;

use App\Models\Session;
use App\Models\ShopifyProduct;
use dpl\ShopifySync\Models\ShopifySyncShop;
use dpl\ShopifySync\Services\SyncShopifyProductService;
use Illuminate\Support\Facades\Log;

class ShopifyProductSyncService
{
    /**
     * @var SyncShopifyProductService
     */
    protected SyncShopifyProductService $syncShopifyProductService;

    /**
     * Create a new service instance.
     *
     * @param SyncShopifyProductService $syncShopifyProductService
     * @return void
     */
    public function __construct(SyncShopifyProductService $syncShopifyProductService)
    {
        $this->syncShopifyProductService = $syncShopifyProductService;
    }

    /**
     * Trigger a full sync of Shopify products
     *
     * @param Session $session
     * @param string|null $reason Reason for the sync (for logging)
     * @return void
     */
    public function triggerFullSync(Session $session, ?string $reason = null): void
    {
        // Check session validity first
        if (!$session) {
            Log::channel('daily')->error('Cannot sync products - session not found');
            return;
        }

        $logContext = [
            'session_id' => $session->id,
            'shop_domain' => $session->shop,
        ];
        
        if ($reason) {
            $logContext['reason'] = $reason;
            Log::channel('daily')->info("Triggering full Shopify product sync", array_merge($logContext, ['reason' => $reason]));
        } else {
            Log::channel('daily')->info('Triggering full Shopify product sync', $logContext);
        }
        
        // Update session settings for sync
        $session->update(['shopify_last_import_at' => null, 'is_shopify_products_importing' => true]);
        
        // Delete sync shop record to force full resync
        ShopifySyncShop::where('specifier', $session->shop)->delete();
        
        // Mark existing products for refresh
        ShopifyProduct::query()
            ->where('session_id', $session->id)
            ->update(['is_active' => false]);
        
        // Schedule shop for sync
        $this->syncShopifyProductService->scheduleShop($session);
    }
} 