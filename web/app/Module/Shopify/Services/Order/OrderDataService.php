<?php

namespace App\Module\Shopify\Services\Order;

use App\Models\Ebay\EbayUserSetting;
use App\Models\Ebay\Order;
use App\Models\Ebay\OrderLineItem;
use App\Models\Session;
use App\Module\Ebay\Helper\Helper;
use App\Traits\AppUtilsTrait;
use Illuminate\Support\Arr;
use Shopify\Clients\Graphql;

class OrderDataService
{
    use AppUtilsTrait;

    public function getFormattedOrderData(Order $order, EbayUserSetting $ebayUserSetting): array
    {
        $syncEbayCollectedTax = $ebayUserSetting->sync_ebay_collected_tax;
        $customTaxData = [
            'vat_percentage' => $ebayUserSetting->vat_percentage,
            'custom_tax_title' => $ebayUserSetting->custom_tax_title ?? 'VAT',
        ];
        $line_items = $this->getFormatLineItems($order, $ebayUserSetting);
        $data = [
            "created_at" => date('c', strtotime($order->creation_date)),
            "currency" => $order->currency,
            "customer" => $this->getFormattedCustomer($order),
            "email" => filter_var($order->buyer_email, FILTER_VALIDATE_EMAIL) ? $order->buyer_email : '',
            "financial_status" => 'paid',
            "line_items" => $line_items,
            "taxes_included" => $ebayUserSetting->hasVatPercentage(),
            "total_discounts" => abs($order->discount) + abs($order->adjustment),
            "shipping_lines" => $this->getShippingLines($order, $customTaxData),
            "source_name" => 'eBay',
            'shipping_address' => $this->getFormattedShippingAddress($order)
        ];

        // Add billing address if sync setting is enabled
        if ($ebayUserSetting->sync_shipping_as_billing_address ?? false) {
            $data['billing_address'] = $this->getFormattedBillingAddress($order);
        }

        if ($this->shouldUnSyncEmail($order, $ebayUserSetting)) {
            unset($data['email'], $data['customer']['email']);
        }

        if ($syncEbayCollectedTax && $order->tax) {
                $data["total_tax"] = $order->tax;
                $data["taxes_included"] = false;
        } else {
            $order->amount_paid -= $order->tax;
        }
        $data["transactions"] = $this->getTransaction($order);
        if ($ebayUserSetting->sync_ebay_order_id) {
            $shopifyOrderPrefix = $ebayUserSetting->shopify_order_prefix ?? '';
            $data["name"] = sprintf("%s%s", $shopifyOrderPrefix, $order->ebay_order_id);
        }

        return $data;
    }

    private function shouldUnSyncEmail(Order $order, EbayUserSetting $ebayUserSetting): bool
    {
        if (!$ebayUserSetting->sync_ebay_order_email) {
            return true;
        }

        $orderMetaInfo = $order->meta_info ?? [];

        return $order->shopify_order_id
            && isset($orderMetaInfo['buyerEmailChanged'])
            && !$orderMetaInfo['buyerEmailChanged'];
    }


    private function getTransaction(Order $order): array
    {
        $transaction = [
            "kind" => "sale",
            "status" => "success",
            "amount" => $order->amount_paid,
            "gateway" => $order->payment_method,
        ];
        return array($transaction);
    }

    private function getShippingLines(Order $order, array $customTaxData): array
    {
        $title = !empty($order->selected_shipping_service) ? $order->selected_shipping_service : 'Standard Shipping';
        $shippingLines = [];
        $delivery_discount = abs($order->delivery_discount);
        //We are checking the condition because sometimes the shipping discount is greater than shipping charge
        if ($delivery_discount > $order->delivery_cost) {
            $total_cost = $order->delivery_cost;
        } else {
            $total_cost = $order->delivery_cost - $delivery_discount;
        }
        if (!$total_cost) {
            foreach ($order->orderLineItems as $lineItem) {
                $total_cost += $lineItem->actual_shipping_cost + $lineItem->actual_handling_cost;
            }
        }

        $shippingLines[] = [
            "title" => $title,
            "price" => $total_cost,
        ];

        if ($customTaxData['vat_percentage'] > 0) {
            $itemTax = ($total_cost * $customTaxData['vat_percentage']) / (100 + $customTaxData['vat_percentage']);
            $taxLines = $this->getOrderTaxLines($itemTax, $customTaxData['vat_percentage'] / 100, $customTaxData['custom_tax_title']);
            $shippingLines[0]['tax_lines'] = [$taxLines];
        }
        return $shippingLines;
    }

    private function getOrderTaxLines($total_tax_amount, $rate = 0.1, $title = "Sales Tax"): array
    {
        return [
            'price' => $total_tax_amount,
            'rate' => $rate,
            'title' => $title
        ];
    }


    private function getFormatLineItems(Order $order, EbayUserSetting $ebayUserSetting): array
    {
        $syncEbayCollectedTax = $ebayUserSetting->sync_ebay_collected_tax;
        $lineItems = [];
        $taxRate = 0.1;
        foreach ($order->orderLineItems as $lineItem) {
            if ($lineItem->total_tax_amount) {
                $taxRate = round($lineItem->total_tax_amount / ($lineItem->transaction_price + $lineItem->actual_shipping_cost), 4);
            }
            if ($order->has_bundle_product) {
                // Call getBundleLineItemsDetails to check if it's a bundle and fetch components
                $bundleComponents = $this->getBundleLineItemsDetails($lineItem->shopify_variant_id, $order->session);
                // Check if the product is a bundle (assuming bundle products have a specific identifier or condition)
                if ($bundleComponents) {
                    // Calculate the total bundle price based on the prices of components
                    $totalBundlePrice = 0;
                    foreach ($bundleComponents as $component) {
                        $totalBundlePrice += $component['node']['productVariant']['price'] * $component['node']['quantity'];
                    }
                    // Loop through each component of the bundle and add as a separate line item
                    foreach ($bundleComponents as $component) {
                        $componentPrice = $component['node']['productVariant']['price'] * $component['node']['quantity'];

                        // Calculate the ratio of this component's price to the total bundle price
                        $priceRatio = $componentPrice / $totalBundlePrice;

                        // Split the price, discount, and tax based on this ratio
                        $splitPrice = round($lineItem->transaction_price * $priceRatio / ($component['node']['quantity'] * $lineItem->quantity), 2);
                        $splitDiscount = isset($lineItem->discounted_line_item_cost)
                            ? round((float)$lineItem->discounted_line_item_cost * $priceRatio, 2)
                            : 0;
                        $variantId = $component['node']['productVariant']['legacyResourceId'];
                        // Initialize the component line item
                        $componentItem = [
                            // Price based on the ratio
                            "price" => $splitPrice,
                            // Adjust quantity based on the bundle
                            "quantity" => $lineItem->quantity * $component['node']['quantity'],
                            "sku" => $component['node']['productVariant']['sku'],
                            "variant_id" => (int)$variantId,
                            "total_discount" => $splitDiscount // Discount split based on the ratio
                        ];

                        // Split tax based on the price ratio
                        if ($syncEbayCollectedTax && $lineItem->total_tax_amount) {
                            $splitTax = round($lineItem->total_tax_amount * $priceRatio, 2);
                            $componentItem["tax_lines"][] = $this->getOrderTaxLines($splitTax, $taxRate);
                        } elseif ($ebayUserSetting->vat_percentage) {
                            $customTaxData = [
                                'vat_percentage' => $ebayUserSetting->vat_percentage,
                                'custom_tax_title' => $ebayUserSetting->custom_tax_title ?? 'VAT',
                            ];
                            $componentItem = $this
                                ->addCustomVatToLineItem($componentItem, $lineItem, $customTaxData);
                        }
                        $lineItems[] = $componentItem; // Add the component as a line item
                        $variantIds[] = $variantId;
                    }
                    $lineItem->update(['bundle_variant_id' => json_encode($variantIds)]);
                    continue;
                }
            }
            // Handle non-bundle products as usual
            $item = [
                "price" => ($lineItem->transaction_price) / $lineItem->quantity,
                "quantity" => $lineItem->quantity,
                "sku" => $lineItem->sku,
                "variant_id" => (int)$lineItem->shopify_variant_id,
            ];
            if ($syncEbayCollectedTax && $lineItem->total_tax_amount) {
                $item["tax_lines"][] = $this->getOrderTaxLines($lineItem->total_tax_amount, $taxRate);
            } elseif ($ebayUserSetting->vat_percentage) {
                $customTaxData = [
                    'vat_percentage' => $ebayUserSetting->vat_percentage,
                    'custom_tax_title' => $ebayUserSetting->custom_tax_title ?? 'VAT',
                ];
                $item = $this->addCustomVatToLineItem(
                    $item, 
                    $lineItem, 
                    $customTaxData
                );
            }
            if ($lineItem->discounted_line_item_cost) {
                $item['total_discount'] = round(
                    $lineItem->transaction_price - $lineItem->discounted_line_item_cost,
                    2
                );
            }
            $lineItems[] = $item;
        }
        return $lineItems;
    }

    public function getBundleLineItemsDetails($variantId, Session $session)
    {
        $query = <<<QUERY
        query {
          productVariant(id: "gid://shopify/ProductVariant/$variantId") {
            productVariantComponents(first:100) {
                edges {
                    node {
                        id
                        quantity
                        productVariant {
                            legacyResourceId
                            price
                            sku
                            title
                            product {
                                title
                            }
                        }
                    }
                }
            }
          }
        }
        QUERY;
        $client = new Graphql($session->shop, $session->access_token);
        $response = $client->query(
            [
                "query" => $query
            ],
        );
        return Arr::get($response->getDecodedBody(), 'data.productVariant.productVariantComponents.edges');
    }



    private function getFormattedCustomer(Order $order): array
    {
        $email = filter_var($order->buyer_email, FILTER_VALIDATE_EMAIL) ? $order->buyer_email : '';
        return [
            "email" => $email,
            "first_name" => $order->buyer_first_name,
            "last_name" => $order->buyer_last_name,
        ];
    }

    public function getFormattedShippingAddress(Order $order): array
    {
        $shipping_address = json_decode($order->shipping_address);
        if (isset($shipping_address->phone_number)) {
            $phone_number = $shipping_address->phone_number;
        } else {
            $phone_number = $order->buyer_primary_phone_number;
        }
        if (isset($shipping_address->first_name)) {
            $first_name = $this->removeEmojis($shipping_address->first_name);
            $last_name = $this->removeEmojis($shipping_address->last_name);
        } else {
            $first_name = $order->buyer_first_name;
            $last_name = $order->buyer_last_name;
        }
        $countryCode = $shipping_address->countryCode ?? 'ZZ';
        if (in_array($countryCode, ['PR', 'AS', 'GU', 'MP', 'UM', 'VI', 'PW'])) {
            $countryCode = 'US';
        }
        
        // Check if province is overridden in meta_info
        $province = $shipping_address->stateOrProvince ?? '';
        if (isset($order->meta_info['overrides']['shipping']['province'])) {
            $province = $order->meta_info['overrides']['shipping']['province'];
        }

        $city = $shipping_address->city ?? '';
        if (isset($order->meta_info['overrides']['shipping']['city'])) {
            $city = $order->meta_info['overrides']['shipping']['city'];
        }

        $address1 = $this->formatShippingAddressLine($shipping_address->addressLine1 ?? '', $countryCode);
        $address2 = $this->formatShippingAddressLine($shipping_address->addressLine2 ?? '', $countryCode);
        if ($address1 === '') {
            if($address2 === '') {
                $address1 = 'N/A';
            } else {
                $address1 = $address2;
                $address2 = '';
            }
        }
        return [
            'first_name' => $first_name,
            'last_name' => $last_name,
            'address1' => $address1,
            'address2' => $address2,
            'phone' => Helper::updatePhoneNumberWithCountryCallingCode($countryCode, $phone_number),
            'city' => $city,
            'province' => $province,
            'country_code' => $countryCode,
            'zip' => $shipping_address->postalCode ?? ''
        ];
    }

    /**
     * Formats shipping address line by filtering out eBay collection point identifiers.
     * For UK and Australia orders, eBay sometimes uses eBay Virtual Tracking Numbers
     * like 'ebayABC1234'  or 'ebay:ABC1234' instead of actual address lines. These identifiers
     * are internal references to collection points and should not be included in Shopify order addresses.
     *
     * @param string|null $shippingAddressLine The address line to format
     * @param string $countryCode ISO country code
     * @return string Formatted address line or empty string if invalid
     */
    private function formatShippingAddressLine(
        ?string $shippingAddressLine,
        string $countryCode
    ): string {
        if (empty($shippingAddressLine)) {
            return '';
        }

        if (
            in_array($countryCode, ['GB', 'AU']) &&
            preg_match('/^ebay:?[a-zA-Z0-9]{7}$/', $shippingAddressLine)
        ) {
            return '';
        }

        return $shippingAddressLine;
    }

    private function addCustomVatToLineItem(array $item, OrderLineItem $lineItem, array $customTaxData): array
    {
        if ($lineItem->discounted_line_item_cost) {
            $price = $lineItem->discounted_line_item_cost;
        } else {
            $price = $item['price'] * $item['quantity'];
        }

        $item_tax = ($price * $customTaxData['vat_percentage']) / (100 + $customTaxData['vat_percentage']);
        $tax_lines = $this->getOrderTaxLines($item_tax, $customTaxData['vat_percentage'] / 100, $customTaxData['custom_tax_title']);
        $item['tax_lines'][] = $tax_lines;
        return $item;
    }

    /**
     * Get formatted billing address by reusing shipping address data.
     * This method supports the sync shipping-as-billing address functionality.
     *
     * @param Order $order
     * @return array
     */
    private function getFormattedBillingAddress(Order $order): array
    {
        // Reuse shipping address formatting logic for billing address
        // This ensures consistency in address formatting and validation
        return $this->getFormattedShippingAddress($order);
    }
}
