<?php

namespace App\Module\Shopify\Services\Inventory;

use App\Models\Session;
use App\Models\ShopifyProductVariant;
use Illuminate\Support\Facades\Log;

class InventoryProcessService
{
    /**
     * Create a new service instance.
     */
    public function __construct(
        private readonly ShopifyInventoryLevelService $inventoryLevelService
    ) {
    }

    /**
     * Process inventory data from a Shopify product
     *
     * @param array $data The product data from Shopify
     * @param ShopifyProductVariant $variant The variant to associate inventory with
     * @param Session $session The current Shopify session
     * @return bool Success status
     */
    public function processVariantInventory(array $variantData, ShopifyProductVariant $shopifyProductVariant, Session $session): bool
    {
        try {
            // Process any inventory levels data first
            $this->inventoryLevelService->processInventoryLevels($variantData, $shopifyProductVariant, $session);
            
            return true;
        } catch (\Exception $e) {
            Log::error('Error processing variant inventory', [
                'message' => $e->getMessage(),
                'variant_id' => $shopifyProductVariant->id,
                'data' => $variantData
            ]);
            return false;
        }
    }
} 