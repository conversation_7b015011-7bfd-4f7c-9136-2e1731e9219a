<?php

namespace App\Module\Shopify\Services\Inventory;

use App\Models\ShopifyInventoryLevel;
use App\Models\ShopifyProductVariant;
use App\Models\Session;
use App\Module\Shopify\Helper\ShopifyGidHelper;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class ShopifyInventoryLevelService
{
    /**
     * Process inventory levels data from Shopify webhook or API response
     *
     * @param array $data The product data from Shopify
     * @param ShopifyProductVariant $variant The variant to associate inventory with
     * @param Session $session The current Shopify session
     * @return Collection The created/updated inventory levels
     */
    public function processInventoryLevels(array $variantData, ShopifyProductVariant $variant, Session $session): Collection
    {
        $results = collect();

        // Get existing inventory levels for this variant and session
        $existingLevels = ShopifyInventoryLevel::where('shopify_variant_id', $variant->id)
            ->where('session_id', $session->id)
            ->get();
        
        // Keep track of processed location IDs
        $processedLocationIds = [];
        
        // Check if the data has inventory levels
        if (!isset($variantData['inventoryLevels']) || empty($variantData['inventoryLevels'])) {
            // If no inventory levels in the data, delete all existing ones with a single query
            try {
                $deletedCount = ShopifyInventoryLevel::where('shopify_variant_id', $variant->id)
                    ->where('session_id', $session->id)
                    ->delete();
                
                if ($deletedCount > 0) {
                    Log::channel('daily')->info("Deleted all {$deletedCount} inventory levels for variant with no inventory data", [
                        'variant_id' => $variant->id
                    ]);
                }
            } catch (\Exception $e) {
                Log::channel('daily')->error('Error deleting all inventory levels', [
                    'message' => $e->getMessage(),
                    'variant_id' => $variant->id
                ]);
            }
            return $results;
        }

        // Process incoming inventory levels (create or update)
        foreach ($variantData['inventoryLevels'] as $inventoryLevelData) {
            try {
                $inventoryLevel = $this->createOrUpdateInventoryLevel($inventoryLevelData, $variant, $session);
                $results->push($inventoryLevel);
                
                // Add this location ID to our processed list
                $locationId = $inventoryLevel->shopify_location_id;
                $processedLocationIds[] = $locationId;
            } catch (\Exception $e) {
                Log::channel('daily')->error('Error processing inventory level', [
                    'message' => $e->getMessage(),
                    'variant_id' => $variant->id,
                    'inventory_level_data' => $inventoryLevelData
                ]);
            }
        }
        
        // Delete inventory levels that weren't in the incoming data (using whereIn for better performance)
        try {
            // Get all location IDs for this variant/session
            $allLocationIds = $existingLevels->pluck('shopify_location_id')->toArray();
            
            // Find location IDs to delete (those in allLocationIds but not in processedLocationIds)
            $locationIdsToDelete = array_diff($allLocationIds, $processedLocationIds);
            
            // Only run deletion if we have locations to delete
            if (!empty($locationIdsToDelete)) {
                // Delete inventory levels with location IDs that need to be deleted
                $deletedCount = ShopifyInventoryLevel::where('shopify_variant_id', $variant->id)
                    ->where('session_id', $session->id)
                    ->whereIn('shopify_location_id', $locationIdsToDelete)
                    ->delete();
                
                if ($deletedCount > 0) {
                    Log::channel('daily')->info("Deleted {$deletedCount} inventory levels not present in updated data", [
                        'variant_id' => $variant->id
                    ]);
                }
            }
        } catch (\Exception $e) {
            Log::channel('daily')->error('Error deleting inventory levels', [
                'message' => $e->getMessage(),
                'variant_id' => $variant->id
            ]);
        }

        return $results;
    }

    /**
     * Create or update a ShopifyInventoryLevel record
     *
     * @param array $inventoryLevelData The inventory level data from Shopify
     * @param ShopifyProductVariant $variant The variant to associate inventory with
     * @param Session $session The current Shopify session
     * @return ShopifyInventoryLevel The created/updated inventory level
     */
    private function createOrUpdateInventoryLevel(
        array $inventoryLevelData,
        ShopifyProductVariant $variant,
        Session $session
    ): ShopifyInventoryLevel {
        // Extract and validate the numeric IDs from Shopify GIDs
        $shopifyInventoryLevelId = ShopifyGidHelper::extractNumericId($inventoryLevelData['id']);
        $shopifyLocationId = ShopifyGidHelper::extractNumericId($inventoryLevelData['location']['id']);

        if ($shopifyInventoryLevelId === null || $shopifyLocationId === null) {
            Log::channel('daily')->error('Invalid inventory level or location ID', [
                'session_id' => $session->shop,
                'inventory_level_id' => $inventoryLevelData['id'],
                'location_id' => $inventoryLevelData['location']['id'],
                'variant_id' => $variant->id,
                'session_id' => $session->id
            ]);
            throw new \InvalidArgumentException('Invalid inventory level or location ID');
        }

        // Extract quantities
        $quantities = $this->extractQuantities($inventoryLevelData['quantities']);

        // Create or update the inventory level
        // With our composite unique key, updateOrCreate will work correctly
        $inventoryLevel = ShopifyInventoryLevel::updateOrCreate(
            [
                'shopify_variant_id' => $variant->id,
                'shopify_location_id' => $shopifyLocationId,
                'session_id' => $session->id,
            ],
            [
                'shopify_inventory_level_id' => $shopifyInventoryLevelId,
                'available_quantity' => $quantities['available'] ?? 0,
                'committed_quantity' => $quantities['committed'] ?? 0,
                'on_hand_quantity' => $quantities['on_hand'] ?? 0,
                'shopify_updated_at' => $inventoryLevelData['updatedAt']
            ]
        );

        return $inventoryLevel;
    }

    /**
     * Extract quantities from Shopify inventory quantities data
     *
     * @param array $quantitiesData The quantities data from Shopify
     * @return array Associative array of quantity types and values
     */
    private function extractQuantities(array $quantitiesData): array
    {
        $quantities = [
            'available' => 0,
            'committed' => 0,
            'on_hand' => 0
        ];

        foreach ($quantitiesData as $quantity) {
            if (isset($quantity['name']) && isset($quantity['quantity'])) {
                $quantities[$quantity['name']] = (int) $quantity['quantity'];
            }
        }

        return $quantities;
    }

    /**
     * Update a variant's inventory quantity based on data from Shopify
     *
     * @param ShopifyProductVariant $variant The variant to update
     * @param int $quantity The new inventory quantity
     * @param Session $session The current Shopify session
     * @return bool Success status
     */
    public function updateVariantInventoryQuantity(
        ShopifyProductVariant $variant,
        int $quantity,
        Session $session
    ): bool {
        try {
            // Update the variant's inventory quantity
            $variant->inventory_quantity = $quantity;
            $variant->save();

            // Update or create the default inventory level
            $inventoryLevels = $variant->inventoryLevels()->where('session_id', $session->id)->get();
            
            if ($inventoryLevels->isEmpty()) {
                // Skip if no inventory levels exist and we don't have location info
                return true;
            }
            
            // Update all existing inventory levels with the new quantity
            // This is a simplified approach - in a real implementation, you might want to 
            // distribute the quantity across locations according to business rules
            foreach ($inventoryLevels as $level) {
                $level->available_quantity = $quantity;
                $level->on_hand_quantity = $quantity;
                $level->save();
            }

            return true;
        } catch (\Exception $e) {
            Log::channel('daily')->error('Error updating variant inventory quantity', [
                'message' => $e->getMessage(),
                'variant_id' => $variant->id,
                'quantity' => $quantity
            ]);
            return false;
        }
    }
} 