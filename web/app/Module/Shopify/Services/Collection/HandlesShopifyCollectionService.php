<?php

namespace App\Module\Shopify\Services\Collection;

use App\Jobs\UploadOrUpdateProductToEbayJob;
use App\Models\Session;
use App\Models\Shopify\ShopifyCollection;
use App\Models\Shopify\ShopifyProductsCollection;
use App\Models\ShopifyProduct;
use dpl\ShopifySync\Contracts\HandlesShopifyCollectionSync;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class HandlesShopifyCollectionService implements HandlesShopifyCollectionSync
{
    /**
     * @param  Session  $shop
     * @param  array  $data
     * @param  array  $metadata
     * @return void
     */
    public function handle($shop, array $data, array $metadata = []): void
    {
        $products = isset($data['products']) ? $data['products'] : [];
        $collection = $data['collection'];
        $collectionModel = $this->updateOrCreateCollection($shop, $collection);
        if (!$collectionModel->wasRecentlyCreated) {
            $existingProductIds = ShopifyProductsCollection::select('shopify_product_id')
                ->where('session_id', $shop->id)
                ->where('shopify_collection_id', $collectionModel->id)
                ->get()->pluck('shopify_product_id')->toArray();
            $removedProductIds = array_diff($existingProductIds, $products);
            $products = array_diff($products, $existingProductIds);
            $removedProductIdsChunk = array_chunk($removedProductIds, 2000);
            foreach ($removedProductIdsChunk as $removedProductIds) {
                ShopifyProductsCollection::where('session_id', $shop->id)
                    ->where('shopify_collection_id', $collectionModel->id)
                    ->whereIn('shopify_product_id', $removedProductIds)
                    ->delete();
            }
            if ($collectionModel->profile_id && $collectionModel->profile->automatic_product_upload) {
                $this->uploadNewProductsToEbay($collectionModel, $products);
            }
        }
        Log::channel('daily')->info('Handle Shopify Collection Service.', [
            'session_id' => $shop->id,
            'shopify_collection_id' => $collection['id'],
            'shopify_product_ids' => $products,
        ]);
        $data = [];
        foreach ($products as $id) {
            $data[] = [
                'session_id' => $shop->id,
                'shopify_product_id' => $id,
                'shopify_collection_id' => $collectionModel->id,
            ];
        }

        if ($data) {
            $chunkSize = 2000;
            $chunks = array_chunk($data, $chunkSize);
            foreach ($chunks as $chunk) {
                ShopifyProductsCollection::insert($chunk);
            }
        }
    }

    public function updateOrCreateCollection(Session $shop, array $collection)
    {
        return ShopifyCollection::updateOrCreate(
            [
                'session_id' => $shop->id,
                'shopify_session_id' => $shop->session_id,
                'shopify_collection_id' => $collection['id']
            ],
            [
                'handle' => $collection['handle'],
                'title' => $collection['title'],
                'updated_at_shopify' => $collection['updatedAt'],
                'image_src' => ''
            ]
        );
    }
    protected function uploadNewProductsToEbay($collection, $products_array)
    {
        // Chunk the massive UPDATE to prevent lock table exhaustion (Error 1206)
        $chunks = array_chunk($products_array, 500); 
        
        foreach ($chunks as $chunk) {
            ShopifyProduct::where('session_id', $collection->session_id)
                ->whereIn('shopify_product_id', $chunk)
                ->update(['profile_id' => $collection->profile_id]);
        }
        
        $products = ShopifyProduct::select('id')
            ->whereIn('shopify_product_id', $products_array)
            ->where('session_id', $collection->session_id)
            ->where('was_deleted_from_ebay', false)
            ->get();

        $jobs = [];
        foreach ($products as $product) {
            $jobs[] = new UploadOrUpdateProductToEbayJob(
                $product->id,
                $collection->session_id
            );
        }
        if (count($jobs)) {
            Bus::chain($jobs)
                ->onQueue('product_upload_to_ebay')
                ->dispatch();
        }
    }
}
