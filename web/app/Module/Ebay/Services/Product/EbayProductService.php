<?php

namespace App\Module\Ebay\Services\Product;

use App\Events\Product\UpdateShopifyPageProgressInPusher;
use App\Exceptions\EbayAccessTokeOrRefreshTokenException;
use App\Exceptions\EbayApiRateLimitException;
use App\Exceptions\EbaySystemErrorException;
use App\Jobs\FinalizeEbayProductImportJob;
use App\Jobs\Product\FetchEbayProducts;
use App\Models\ActivityLogger;
use App\Models\Ebay\EbayProduct;
use App\Models\Ebay\EbayUser;
use App\Models\ShopifyProduct;
use App\Module\Ebay\Helper\Helper;
use App\Module\Ebay\Repositories\Product\EbayProductRepository;
use App\Module\Ebay\Repositories\Product\EbayProductUploadRepository;
use App\Module\Shopify\Repositories\Product\ShopifyProductRepository;
use App\Module\Shopify\Enums\ShopifyBulkActionEnum;
use App\Module\Shopify\Enums\ShopifyProductUploadStatusEnum;
use App\Services\Common\PusherNotification;
use App\Traits\ActivityLoggerTrait;
use Carbon\Carbon;
use DateTime;
use DTS\eBaySDK\Trading\Services\TradingService;
use DTS\eBaySDK\Trading\Types\GetItemResponseType;
use DTS\eBaySDK\Types\RepeatableType;
use Exception;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Throwable;

class EbayProductService
{
    use ActivityLoggerTrait;

    protected TradingService $service;
    protected array $serviceArgs;

    public function __construct(
        protected EbayProductRepository $ebayProductRepository,
        protected EbayProductConfigService $reqConfig,
        protected EbayProductHandlerService $ebayProductHandlerService,
        protected EbayProductDataService $ebayProductDataService
    ) {
    }

    public function totalActiveListingsCount(EbayUser $ebayUser)
    {
        $serviceArgs = Helper::eBayCredentials($ebayUser);
        $service = new TradingService($serviceArgs);

        //request config
        $req = $this->reqConfig->generateGetActiveListingsRequest();
        try {
            Log::channel('daily')
                ->info('func:fetchTotalActiveListingsEbay Fetching total active listings with
                 TradingService getMyeBaySelling  ' . $ebayUser->ebay_user_name);
            $response = $service->getMyeBaySelling($req)->toArray();
        } catch (Exception $e) {
            Log::channel('daily')
                ->critical("Error:fetchTotalActiveListingsEbay", [
                    "eBay user name" => $ebayUser->ebay_user_name,
                    "message" => $e->getMessage()
                ]);
            return 0;
        }
        if (isset($response['Errors']) || !isset($response['ActiveList']['PaginationResult']['TotalNumberOfEntries'])) {
            Log::channel('daily')
                ->critical("Error:fetchTotalActiveListingsEbay", [
                    "eBay user name" => $ebayUser->ebay_user_name,
                    "message" => json_encode($response)
                ]);
            return 0;
        }
        return $response['ActiveList']['PaginationResult']['TotalNumberOfEntries'];
    }

    /**
     * @throws Throwable
     * Dispatches job to fetch products till the current date at once
     */
    public function dispatchFetchEbayProductJobs(EbayUser $ebayUser, bool $isInitialSync = false): void
    {
        try {
            $products_queue =  $this->totalActiveListingsCount($ebayUser) < 1000 ?
                "products_queue" :
                "massive_products_queue";
            $shopify_session_id = $ebayUser->__get('shopify_session_id');
            $jobs = $this->generateJobs($ebayUser, true);
            EbayProduct::where('ebay_user_id', $ebayUser->id)->update(['is_active' => false]);
            $batch = Bus::batch([$jobs])
                ->finally(function (Batch $batch) use ($shopify_session_id, $isInitialSync) {
                    $ebayUser = EbayUser::where('shopify_session_id', $shopify_session_id)->first();
                    if ($ebayUser && $isInitialSync) {
                        FinalizeEbayProductImportJob::dispatch($ebayUser, $shopify_session_id, $batch->id);
                    }
                })
                ->onQueue($products_queue)
                ->name($shopify_session_id)
                ->dispatch();
            // updates the batch id and is_product_fetching values
            $this->ebayProductDataService->updateEbayUserProdFetchArgs(false, $ebayUser, $batch);
        } catch (Exception $e) {
            Log::channel('daily')
                ->error(
                    "Error:dispatchFetchEbayProductJobs user:$ebayUser->shopify_session_id message" . $e->getMessage()
                );
        }
    }

    public function hasEbayProducts($ebayUserId): bool
    {
        return (bool)EbayProduct::where('ebay_user_id', $ebayUserId)->count();
    }

    /**
     * @throws Throwable
     */
    public function dispatchEndListingsJobs(
        array $jobs,
        string $cacheKey,
        EbayUser $ebayUser,
        ShopifyProductRepository $repo,
        ActivityLogger $activityLogger
    ): void {
        Bus::batch($jobs)
            ->finally(function () use ($repo, $cacheKey, $ebayUser, $activityLogger) {
                $activityLogger->update(['progress' => 100, 'completed_at' => Carbon::now()]);
                $repo->updateIsShopifyBulkAction($ebayUser, ShopifyBulkActionEnum::DEFAULT->value);
                $this->updateEndListingProgressInPusher(
                    false,
                    [],
                    $ebayUser->__get('shopify_session_id'),
                    $cacheKey,
                    $activityLogger,
                    100
                );
            })
            ->onQueue('end_listing')
            ->dispatch();
    }

    public function fetchProductDetailsFromItemId(
        string $itemId,
        EbayUser $ebayUser,
        int $shopify_product_id = null
    ): void {
        $serviceArgs = Helper::eBayCredentials($ebayUser);
        $service = new TradingService($serviceArgs);

        $req = $this->reqConfig->generateGetItemRequest($itemId);
        Log::channel('daily')
            ->info('func:fetchProductDetailsFromItemId Fetching a product with TradingService getItem.', [
                'eBay user name' => $ebayUser->ebay_user_name,
                'Item Id' => $itemId
            ]);
        $response = $service->getItem($req)->toArray();

        if (isset($response['Errors'])) {
            Log::channel('daily')->error("func:fetchProductDetailsFromItemId Error:", [
                'Item id' => $itemId,
                'eBay User name id' => $ebayUser->ebay_user_name,
                'Errors' => $response['Errors'],
            ]);
            return;
        }
        if (!is_null($shopify_product_id)) {
            $ebayProdUploadRepo = new EbayProductUploadRepository();
            $shopify_product = $ebayProdUploadRepo->getShopifyProduct($shopify_product_id, $ebayUser->session_id);
            $this->ebayProductHandlerService
                ->handleProduct($response['Item'], $ebayUser, $shopify_product);
            $data = array(
                'ebay_product_id' => $itemId,
                'ebay_product_url' => $response['Item']['ListingDetails']['ViewItemURL'] ?? null,
                'is_ebay_listing_ended_due_to_inventory' => false
            );
            $ebayProdUploadRepo->updateShopifyProductDetails($shopify_product_id, $data);
        } else {
            $this->ebayProductHandlerService
                ->handleProduct($response['Item'], $ebayUser);
        }
    }

    public function getEbayItem(EbayUser $ebayUser, int $itemId): array|null
    {
        $serviceArgs = Helper::eBayCredentials($ebayUser);
        $service = new TradingService($serviceArgs);
        $req = $this->reqConfig->generateGetItemRequest($itemId);
        $response = $service->getItem($req)->toArray();
        Log::channel('daily')
            ->info('func:fetchProductDetailsFromItemId Fetching a product with TradingService getItem.', [
                'eBay user name' => $ebayUser->ebay_user_name,
                'Item Id' => $itemId
            ]);
        if (isset($response['Errors']) || !isset($response['Item'])) {
            Log::channel('daily')->error("func:getEbayItem Response:", [
                'eBay User name id' => $ebayUser->ebay_user_name,
                'Item id' => $itemId,
                'Response' => $response
            ]);
            return null;
        }
        return $response['Item'];
    }

    /**
     * @throws EbayAccessTokeOrRefreshTokenException
     */
    public function fetchEbayItemResponse(EbayUser $ebayUser, int $itemId): GetItemResponseType
    {
        $serviceArgs = Helper::eBayCredentials($ebayUser);
        $service = new TradingService($serviceArgs);
        $req = $this->reqConfig->generateGetItemRequest($itemId);
        return $service->getItem($req);
    }

    /**
     * Performs an API call, dispatches all the next pages for the given date range
     * Saves the list of products in the table and update the progress to  pusher
     * @throws EbayAccessTokeOrRefreshTokenException
     * @throws EbayApiRateLimitException
     * @throws EbaySystemErrorException
     */
    public function fetchEbayProducts(
        EbayUser $ebayUser,
        array $dateArgsArray,
        Batch $batch,
        bool $notifyWithPusher
    ): array {
        Log::channel('daily')
            ->info("Setting Jobs to fetch ebay Products with following arguments  $ebayUser->ebay_user_name:"
                . json_encode($dateArgsArray));
        $this->serviceArgs = Helper::eBayCredentials($ebayUser);
        $this->service = new TradingService($this->serviceArgs);
        $products = array();

        //request config
        $getSellerListRequest = $this->reqConfig->generateRequest($dateArgsArray);
        $response = $this->service->getSellerList($getSellerListRequest)->toArray();

        if (isset($response['Errors'])) {
            Log::channel('daily')->error(
                "Error:fetchEbayProducts ",
                [
                    'ebayUserId' => $ebayUser->id,
                    'response' => $response
                ]
            );
            foreach ($response['Errors'] as $error) {
                if ($error['ErrorCode'] === "18000") {
                    throw new EbayApiRateLimitException(
                        "API rate limit while fetching Ebay products $ebayUser->ebay_user_name"
                    );
                }

                if ($error['ErrorCode'] === "10007") {
                    throw new EbaySystemErrorException(
                        "Ebay system error while fetching Ebay products $ebayUser->ebay_user_name"
                    );
                }
            }
        }

        $hasMoreItems = isset($response['HasMoreItems']) && $response['HasMoreItems'];
        // fetch next page listings if it has more items and next pages are not dispatched
        if ($hasMoreItems) {
            $dateArgsArray['page_number']++;
            $batch->add([new FetchEbayProducts($ebayUser->id, $dateArgsArray, $notifyWithPusher)]);
        }

        // handle fetched products response if Item array is not empty
        if (isset($response['ItemArray']['Item'])) {
            $this->ebayProductHandlerService->handleFetchedProducts($response, $ebayUser);
        }
        if (isset($response['HasMoreItems']) && !$hasMoreItems) {
            $ebay_products_query = EbayProduct::where('ebay_user_id', $ebayUser->id)
                ->where('is_active', false);
            $ebayProductIds = (clone $ebay_products_query)->whereNotNull('item_id')
                ->pluck('item_id')->toArray();
            ShopifyProduct::where('session_id', $ebayUser->session_id)
                ->whereIn('ebay_product_id', $ebayProductIds)
                ->where('is_ebay_listing_ended_due_to_inventory', false)
                ->update([
                    'ebay_product_id' => null,
                    'ebay_product_url' => null,
                    'upload_status' => ShopifyProductUploadStatusEnum::NOT_UPLOADED->value,
                    'profile_id' => null
                ]);
            $ebay_products_query->delete();
            $ebayUser->update(['is_ebay_products_fetching' => false]);
        }


        //updates the progress to pusher
        if (!isset($response['ItemArray']['Item']) && $notifyWithPusher) {
            Log::channel('daily')->info('Item not found in response', [
                'eBay User Name' => $ebayUser->ebay_user_name,
                'response' => $response
            ]);
            PusherNotification::updateEbayProgress($ebayUser->shopify_session_id);
            return [];
        }
        if ($notifyWithPusher) {
            foreach ($response['ItemArray']['Item'] as $key => $item) {
                if (
                    $item['ListingType'] !== 'FixedPriceItem' ||
                    !in_array($item['SellingStatus']['ListingStatus'], ['Active', 'Ended'])
                ) {
                    continue;
                }
                
                $products[] = array(
                    'title' => $item['Title'],
                    'image' => isset($item['PictureDetails']['PictureURL']) ? $item['PictureDetails']['PictureURL'][0] : null, //phpcs:ignore
                    'id' => $item['ItemID']
                );
            }
            
            // Limit products array to prevent Pusher size limit error (10KB max)
            $productsChunk = array_slice($products, 0, 10); // Only send 10 products at a time
            
            PusherNotification::updateEbayProgress(
                $ebayUser->shopify_session_id,
                $productsChunk,
                $hasMoreItems ? $batch->progress() : 100
            );
        }
        return [];
    }


    public function getItem(EbayUser $ebayUser, string $item_id): array
    {
        $serviceArgs = Helper::eBayCredentials($ebayUser);
        $service = new TradingService($serviceArgs);

        $req = $this->reqConfig->generateGetItemRequest($item_id);
        Log::channel('daily')
            ->info('func:getItem Fetching a product with TradingService getItem ' . $ebayUser->ebay_user_name);
        $res = $service->getItem($req)->toArray();

        return $res['Item'];
    }

    /**
     * @throws EbayAccessTokeOrRefreshTokenException
     * @throws Exception
     */
    public function handleEndListingWithItemId(EbayUser $ebayUser, $itemId): void
    {
        $serviceArgs = Helper::eBayCredentials($ebayUser);
        $service = new TradingService($serviceArgs);
        $productData = array(
            'ebay_product_id' => $itemId,
            'cancellation_reason' => ['NotAvailable']
        );
        $req = $this->reqConfig->generateEndListingRequest($productData);

        Log::channel('daily')->info('Item ID of ' . $req->ItemID . 'for user ' . $ebayUser->ebay_user_name . 'called using func:handleEndListing  End fixed price Item with TradingService'); //phpcs:ignore
        $res = $service->endFixedPriceItem($req);

        if (isset($res->Errors) && !$this->isListingAlreadyEnded($res->Errors)) {
            throw new Exception('Error occurred while ending ebay item id of ' . $itemId . ' for ebay user name'
                . $ebayUser->ebay_user_name . ' Reason: ' . $res);
        }
        $this->ebayProductRepository->removeLinkStatusDataFromEndedProduct(
            $ebayUser->__get('id'),
            $itemId
        );

        // search for shopify product and since the ebay listing is ended and has the link
        $searchLinkedShopifyProduct = ShopifyProduct::where('session_id', $ebayUser->session_id)
            ->where('ebay_product_id', $itemId)
            ->first();

        if (
            $searchLinkedShopifyProduct &&
            $searchLinkedShopifyProduct->ebay_product_id &&
            !$searchLinkedShopifyProduct->is_ebay_listing_ended_due_to_inventory
        ) {
            $searchLinkedShopifyProduct->update([
                'is_ebay_listing_ended_due_to_inventory' => true
            ]);
        }
    }

    public function handleEndListing(
        EbayUser $ebayUser,
        array $productData,
        string $cacheKey,
        ActivityLogger $activityLogger
    ): void {
        $serviceArgs = Helper::eBayCredentials($ebayUser);
        $service = new TradingService($serviceArgs);
        $req = $this->reqConfig->generateEndListingRequest($productData);
        Log::channel('daily')->info('Item ID of ' . $req->ItemID . 'for user ' . $ebayUser->ebay_user_name . 'called using func:handleEndListing  End fixed price Item with TradingService'); //phpcs:ignore
        $res = $service->endFixedPriceItem($req);
        $shopifyProductRepository = new ShopifyProductRepository();


        Log::channel('daily')->debug('handleEndListing', [
            'ebayUserName' => $ebayUser->ebay_user_name,
            'productData' => $productData,
            'response' => $res
        ]);

        if (!isset($res->Errors) || $this->isListingAlreadyEnded($res->Errors)) {
            $shopifyProductRepository->removeEbayListingDataFromShopifyProduct($productData['id']);
            $this->ebayProductRepository->removeLinkStatusDataFromEndedProduct(
                $ebayUser->__get('id'),
                $productData['ebay_product_id']
            );
        }

        $this->updateEndListingProgressInPusher(
            isset($res->Errors),
            $productData,
            $ebayUser->__get('shopify_session_id'),
            $cacheKey,
            $activityLogger
        );
    }

    public function isListingAlreadyEnded(RepeatableType|array $errors): bool
    {
        foreach ($errors as $error) {
            if ($error->ErrorCode == "1047" || $error->ErrorCode == "17") {
                return true;
            }
        }
        return false;
    }

    public function updateEndListingProgressInPusher(
        bool $isError,
        array $productData,
        string $shopify_session_id,
        string $cache_key,
        ActivityLogger $activityLogger,
        int $progress_override = null,
    ): void {
        $cacheData = Helper::updateAndRetrieveLatestProgress($cache_key, 1, $progress_override);
        if (is_null($cacheData) || is_null($progress = $cacheData['progress'])) {
            return;
        }
        $pusherData = array(
            'type' => ShopifyBulkActionEnum::DELETING->value,
            'shopify_user_id' => $shopify_session_id,
            'progress' => $progress,
            'processedCount' => [
                'uploaded_products' => $cacheData['processed_jobs'],
                'total_products' => $cacheData['total_jobs'],
            ]
        );
        // add products in pusher data if $productData is not empty(for override case)
        if (!empty($productData)) {
            $pusherData['products'] = array([
                'title' => $productData['title'],
                'image' => $productData['image'],
                'id' => $productData['id'],
                'isFailed' => $isError
            ]);
        }
        if (!empty($pusherData)) {
            $activityLogger->update(['progress' => $progress]);
            broadcast(new UpdateShopifyPageProgressInPusher($pusherData))->toOthers();
        }
    }

    /**
     * @param $reqArgs
     * @param $ebayUser
     * @param array $jobs
     * @return array
     * @throws Exception
     */
    public function generateJobs($ebayUser, $notify_with_pusher = false): array
    {
        $current_time = new DateTime(date('Y-m-d H:i'));
        $endTimeTo = Helper::add119days($current_time);
        $reqArgs = array(
            'end_time_from' => $current_time,
            'end_time_to' => $endTimeTo,
            'page_number' => 1
        );
        $jobs[] = new FetchEbayProducts($ebayUser->id, $reqArgs, $notify_with_pusher);
        $data = ['last_product_fetched_at' => $current_time];
        $this->ebayProductRepository->updateEbayUser($ebayUser, $data);
        return $jobs;
    }
}
