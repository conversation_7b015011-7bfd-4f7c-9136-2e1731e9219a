<?php

namespace App\Module\Ebay\Services\Product\EPS;

class EpsImageValidationResult
{
    /**
     * Whether the image passed validation
     *
     * @var bool
     */
    public bool $isValid = true;

    /**
     * List of validation errors if any
     *
     * @var array
     */
    public array $errors = [];

    /**
     * Add an error message
     *
     * @param string $message
     * @return void
     */
    private function addError(string $message): void
    {
        $this->errors[] = $message;
        $this->isValid = false;
    }

    /**
     * Add results from a validation method
     *
     * @param array $results
     * @return void
     */
    public function addResults(array $results): void
    {
        if (isset($results['errors']) && is_array($results['errors'])) {
            foreach ($results['errors'] as $error) {
                $this->addError($error);
            }
        }
    }

    /**
     * Check if there are any errors
     *
     * @return bool
     */
    public function hasErrors(): bool
    {
        return !empty($this->errors);
    }
}
