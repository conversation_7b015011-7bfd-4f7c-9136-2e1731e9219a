<?php

namespace App\Module\Ebay\Services\Product\EPS;

use DOMDocument;
use DOMException;

/**
 * Dedicated service for building EPS XML payloads
 */
class EpsXmlBuilder
{
    private const XML_NAMESPACE = 'urn:ebay:apis:eBLBaseComponents';

    /**
     * Build EPS XML payload for image upload
     *
     * @param array $data The data to include in the XML
     * @return string The XML document as a string
     * @throws DOMException
     */
public function buildUploadRequest(array $data): string
 {
     if (!isset($data['ExternalPictureURL']) || !isset($data['PictureSet'])) {
        throw new \InvalidArgumentException('Required fields ExternalPictureURL and PictureSet must be provided');
     }

     $dom = new DOMDocument('1.0', 'utf-8');
        $dom->formatOutput = true;

        $root = $dom->createElement('UploadSiteHostedPicturesRequest');
        $root->setAttribute('xmlns', self::XML_NAMESPACE);
        $dom->appendChild($root);

        $pictureUrl = $dom->createElement('ExternalPictureURL', 
            htmlspecialchars($data['ExternalPictureURL'], ENT_XML1, 'UTF-8')
        );
        $pictureSet = $dom->createElement('PictureSet', $data['PictureSet']);
       
        $root->appendChild($pictureUrl);
        $root->appendChild($pictureSet);

        return $dom->saveXML();
    }
} 