<?php

namespace App\Module\Ebay\Services\Product\EPS;

use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

/**
 * Service for validating EPS images before submission to eBay
 *
 * Performs comprehensive validation of image URLs including:
 * - URL structure and security checks
 * - Image accessibility via HTTP HEAD requests
 * - File format validation against allowed types
 * - File size validation (max 12MB)
 *
 * Returns an EpsImageValidationResult containing validation status and any errors
 */
class EpsImageValidationService
{
    public const MAX_FILE_SIZE = 12582912; // 12MB
    public const MAX_URL_LENGTH = 1024;
    public const REQUEST_TIMEOUT = 10;
    public const ALLOWED_FORMATS = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/tiff',
        'image/bmp'
    ];

    public const BLOCKED_HOSTS = [
        'localhost',
        '127.0.0.1',
        '::1'
    ];

    public function validateImage(string $imageUrl): EpsImageValidationResult
    {
        $result = new EpsImageValidationResult();

        try {
             // Step 1: Validate URL structure
            $urlResult = $this->validateUrl($imageUrl);
            $result->addResults($urlResult);

            // If URL validation fails, return immediately with errors
            if ($result->hasErrors()) {
                $result->isValid = false;
                return $result;
            }

            // Step 2: Check format and size with HEAD request
            $headResults = $this->performHeadValidation($imageUrl);
            $result->addResults($headResults);

            if ($result->hasErrors()) {
                $result->isValid = false;
                return $result;
            }
        } catch (Exception $e) {
            $result->errors[] = $e->getMessage();
            $result->isValid = false;
        }
        return $result;
    }


    /**
     * Perform HEAD request validation for format and size
     *
     * @param string $imageUrl
     * @return array
     */
    private function performHeadValidation(string $imageUrl): array
    {
        $result = [
            'errors' => []
        ];

        try {
            $response = Http::timeout(self::REQUEST_TIMEOUT)->head($imageUrl);
            $statusCode = $response->status();

            // Check if image is accessible
            if ($statusCode !== 200) {
                $result['errors'][] = "Image is not accessible (HTTP status: $statusCode)";
                return $result;
            }

            $headers = $response->headers();


            // Normalize headers to lowercase keys
            $headers = collect($headers)
                ->mapWithKeys(fn($value, $key) => [strtolower($key) => $value])
                ->toArray();

            $contentTypeHeader = Arr::get($headers, 'content-type');
            $contentLengthHeader = Arr::get($headers, 'content-length');

            // Get content type and check if it's an image
            if ($contentType = Arr::first($contentTypeHeader)) {
                $formatResult = $this->validateFormat($contentType);
                array_push($result['errors'], ...$formatResult['errors']);
            }

            // Check file size if Content-Length header is present
            if ($contentLength = Arr::first($contentLengthHeader)) {
                $size = (int) $contentLength;
                $sizeResult = $this->validateFileSize($size);

                array_push($result['errors'], ...$sizeResult['errors']);
            }

            return $result;
        } catch (Exception $e) {
            $result['errors'][] = "Head Validation - Failed to access image: " . $e->getMessage();
            return $result;
        }
    }

     /**
     * Validate file size
     *
     * @param int $size
     * @return array
     */
    private function validateFileSize(int $size): array
    {
        $result = [
            'errors' => []
        ];
        // Check if size exceeds maximum
        if ($size > self::MAX_FILE_SIZE) {
            $result['errors'][] = sprintf(
                'Image size (%s) exceeds maximum allowed size (%s)',
                $this->formatBytes($size),
                $this->formatBytes(self::MAX_FILE_SIZE)
            );
        }

        return $result;
    }

     /**
     * Format bytes to human-readable format
     *
     * @param int $bytes
     * @param int $precision
     * @return string
     */
    private function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, $precision) . ' ' . $units[$pow];
    }

    /**
     * Validate image format
     *
     * @param string $contentType
     * @return array
     */
    private function validateFormat(string $contentType): array
    {
        $result = [
            'errors' => []
        ];

        // Check if content type is allowed
        if (!in_array($contentType, self::ALLOWED_FORMATS)) {
            $result['errors'][] = "Image format '$contentType' is not supported";
        }


        return $result;
    }


     /**
     * Validate image URL structure and accessibility
     *
     * @param string $imageUrl
     * @return array
     */
    private function validateUrl(string $imageUrl): array
    {
         $result = [
            'errors' => []
         ];

         // Check if URL is properly formatted
         if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
             $result['errors'][] = 'Invalid URL format';
             return $result;
         }

         // Check if URL uses HTTP or HTTPS
         $parsedUrl = parse_url($imageUrl);
         if (!isset($parsedUrl['scheme']) || !in_array($parsedUrl['scheme'], ['http', 'https'])) {
             $result['errors'][] = 'URL must use HTTP or HTTPS protocol';
             return $result;
         }

         // Security check: prevent local/private network access
         if (
             isset($parsedUrl['host'])
             && in_array(strtolower($parsedUrl['host']), self::BLOCKED_HOSTS)
         ) {
               $result['errors'][] = 'Local URLs are not allowed';
               return $result;
         }

         // Check URL length (eBay might have limits)
         if (strlen($imageUrl) > self::MAX_URL_LENGTH) {
             $result['errors'][] = 'URL is unusually long';
         }

         return $result;
    }
}
