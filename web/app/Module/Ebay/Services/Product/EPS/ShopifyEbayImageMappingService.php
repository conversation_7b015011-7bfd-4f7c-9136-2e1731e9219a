<?php

namespace App\Module\Ebay\Services\Product\EPS;

use App\Models\ShopifyEbayImageMapping;
use Illuminate\Database\Eloquent\Collection;

class ShopifyEbayImageMappingService
{
/**
 * Create or update image mapping
 */
    public function createOrUpdateMapping(array $attributes): ShopifyEbayImageMapping
    {
        /** @var ShopifyEbayImageMapping $mapping */
         $mapping = ShopifyEbayImageMapping::query()->updateOrCreate(
             [
                'shopify_image_id' => $attributes['shopify_image_id']
             ],
             $attributes
         );
         return $mapping;
    }

    /**
     * Get valid mapping for image
     */
    public function getValidMapping(string $shopifyImageId): ?ShopifyEbayImageMapping
    {
        return ShopifyEbayImageMapping::query()
            ->where('shopify_image_id', $shopifyImageId)
            ->validEpsUrls()
            ->first();
    }
     /**
     * Get valid mappings for multiple image ids
     */
    public function getValidMappings(array $shopifyImageIds): Collection
    {
        if (empty($shopifyImageIds)) {
            return new Collection();
        }

         return ShopifyEbayImageMapping::query()
             ->whereIn('shopify_image_id', $shopifyImageIds)
             ->validEpsUrls()
             ->get();
    }
}
