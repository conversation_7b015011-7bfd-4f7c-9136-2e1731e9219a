<?php

namespace App\Module\Ebay\Services\Product\EPS;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;
use App\Module\Ebay\Services\User\EbaySiteMappingService;
use App\Models\Ebay\EbayUser;
use App\Module\Ebay\Services\OAuth\EbayOAuthService;
use App\Exceptions\EbayAccessTokeOrRefreshTokenException;
use Illuminate\Http\Client\Response;
use App\Services\Common\XmlUtils;
use App\Models\ShopifyProductImage;
use App\Module\Ebay\Services\Product\EPS\EpsImageValidationService;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use App\Module\Ebay\Enums\EpsUploadStatusEnum;

class EpsUploadService
{
    private const PRODUCTION_EBAY_API_URL = 'https://api.ebay.com/ws/api.dll';
    private const SANDBOX_EBAY_API_URL = 'https://api.sandbox.ebay.com/ws/api.dll';
    private const API_COMPATIBILITY_LEVEL = '967';
    private const API_CALL_NAME = 'UploadSiteHostedPictures';

    public function __construct(
        private readonly EbayOAuthService $ebayOAuthService,
        private readonly EbaySiteMappingService $ebaySiteMappingService,
        private readonly ShopifyEbayImageMappingService $shopifyEbayImageMappingService,
        private readonly EpsXmlBuilder $xmlBuilder,
        private readonly EpsImageValidationService $imageValidator
    ) {
    }

    /**
     * @throws \DOMException
     * @throws EbayAccessTokeOrRefreshTokenException
     */
    public function handleEpsUpload(EbayUser $ebayUser, ShopifyProductImage $shopifyProductImage): void
    {
        try {
            $imageUrl = $shopifyProductImage->url;

            $imageValidationResult = $this->imageValidator->validateImage($imageUrl);

            if (!$imageValidationResult->isValid) {
                Log::channel('daily')->error('HandleEpsUpload : Image is not valid for EPS upload', [
                    'ebay_user_name' => $ebayUser->ebay_user_name,
                    'ebay_user_id' => $ebayUser->id,
                    'shopify_session_id' => $ebayUser->shopify_session_id,
                    'image_url' => $imageUrl,
                    'shopify_product_id' => $shopifyProductImage->shopify_product_id,
                    'shopify_image_id' => $shopifyProductImage->image_id,
                    'validation_errors' => $imageValidationResult->errors
                ]);

                $this->shopifyEbayImageMappingService->createOrUpdateMapping([
                    'ebay_user_name' => $ebayUser->ebay_user_name,
                    'shopify_session_id' => $ebayUser->shopify_session_id,
                    'shopify_image_id' => $shopifyProductImage->image_id,
                    'shopify_image_url' => $imageUrl,
                    'eps_upload_status' => EpsUploadStatusEnum::VALIDATION_FAILED->value,
                    'eps_upload_error_message' => implode(',', $imageValidationResult->errors)
                ]);

                return;
            }


            $response = $this->callEpsApi($ebayUser, $imageUrl);

            if (!$response->successful()) {
                Log::channel('daily')->error('HandleEpsUpload : Failed response while uploading image to eBay EPS', [
                    'ebay_user_name' => $ebayUser->ebay_user_name,
                    'shopify_session_id' => $ebayUser->shopify_session_id,
                    'ebay_user_id' => $ebayUser->id,
                    'image_url' => $imageUrl,
                    'error' => $response->body(),
                    'status_code' => $response->status()
                ]);
                throw new Exception('HandleEpsUpload : Failed response while uploading image to eBay EPS');
            }

            $responseBody = $response->body();

            if (!XmlUtils::isValidXml($responseBody)) {
                Log::channel('daily')->error('HandleEpsUpload : Invalid XML in EPS response', [
                    'ebay_user_name' => $ebayUser->ebay_user_name,
                    'shopify_session_id' => $ebayUser->shopify_session_id,
                    'ebay_user_id' => $ebayUser->id,
                    'image_url' => $imageUrl
                ]);
                throw new Exception('Invalid XML in EPS response');
            }

            $responseData = XmlUtils::xmlToArray($responseBody);


            if (!isset($responseData['SiteHostedPictureDetails'])) {
                $errors = $this->extractErrors($responseData);
                Log::channel('daily')->error('HandleEpsUpload : SiteHostedPictureDetails not found in EPS response', [
                    'ebay_user_name' => $ebayUser->ebay_user_name,
                    'shopify_session_id' => $ebayUser->shopify_session_id,
                    'shopify_product_id' => $shopifyProductImage->shopify_product_id,
                    'shopify_image_id' => $shopifyProductImage->image_id,
                    'image_url' => $imageUrl,
                    'errors' => $errors
                ]);

                $this->shopifyEbayImageMappingService->createOrUpdateMapping([
                    'shopify_image_id' => $shopifyProductImage->image_id,
                    'shopify_image_url' => $imageUrl,
                    'eps_upload_status' => EpsUploadStatusEnum::FAILED->value,
                    'eps_upload_error_message' => implode(',', array_column($errors, 'long_message'))
                ]);
                return;
            }


            $ebayEpsUrl = Arr::get($responseData, 'SiteHostedPictureDetails.FullURL');
            $epsUrlExpiresAt = Arr::get($responseData, 'SiteHostedPictureDetails.UseByDate');

            $this->shopifyEbayImageMappingService->createOrUpdateMapping([
                'shopify_image_id' => $shopifyProductImage->image_id,
                'shopify_image_url' => $imageUrl,
                'ebay_eps_url' => $ebayEpsUrl ? $this->replaceEbayImageSize($ebayEpsUrl) : null,
                'eps_upload_status' => EpsUploadStatusEnum::SUCCESS->value,
                'eps_url_expires_at' => $epsUrlExpiresAt ? Carbon::parse($epsUrlExpiresAt) : null,
                'eps_upload_error_message' => null
            ]);
        } catch (EbayAccessTokeOrRefreshTokenException $e) {
            Log::channel('daily')->error('HandleEpsUpload : Access token or refresh token exception', [
                'ebay_user_name' => $ebayUser->ebay_user_name,
                'shopify_session_id' => $ebayUser->shopify_session_id,
                'ebay_user_id' => $ebayUser->id,
                'image_url' => $shopifyProductImage->url,
                'error' => $e->getMessage()
            ]);
            throw $e;
        } catch (Exception $e) {
            Log::channel('daily')->error('HandleEpsUpload : Error uploading image to EPS', [
                'ebay_user_name' => $ebayUser->ebay_user_name,
                'shopify_session_id' => $ebayUser->shopify_session_id,
                'ebay_user_id' => $ebayUser->id,
                'image_url' => $shopifyProductImage->url,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Call eBay EPS API
     * @throws EbayAccessTokeOrRefreshTokenException
     * @throws \DOMException
     */
    private function callEpsApi(EbayUser $ebayUser, string $imageUrl): Response
    {

        Log::channel('daily')->info('HandleEpsUpload : Calling eBay EPS API', [
            'ebay_user_name' => $ebayUser->ebay_user_name,
            'shopify_session_id' => $ebayUser->shopify_session_id,
            'ebay_user_id' => $ebayUser->id,
            'image_url' => $imageUrl,
            'api_endpoint' => $this->getApiEndpoint()
        ]);

        $accessToken = $this->ebayOAuthService->getAccessToken($ebayUser);
        $siteMap = $this->ebaySiteMappingService->getSiteMap($ebayUser->site);
        $uploadRequest = $this->xmlBuilder->buildUploadRequest([
            'ExternalPictureURL' => $imageUrl,
            'PictureSet' => 'Supersize'
        ]);

        return Http::withHeaders([
            'X-EBAY-API-SITEID' => $siteMap->siteId,
            'X-EBAY-API-COMPATIBILITY-LEVEL' => self::API_COMPATIBILITY_LEVEL,
            'X-EBAY-API-IAF-TOKEN' => $accessToken,
            'X-EBAY-API-APP-NAME' => config('ebay.client_id'),
            'X-EBAY-API-DEV-NAME' => config('ebay.dev_id'),
            'X-EBAY-API-CERT-NAME' => config('ebay.client_secret'),
            'X-EBAY-API-CALL-NAME' => self::API_CALL_NAME
        ])
        ->timeout(30)
        ->withBody($uploadRequest, 'text/xml;charset=utf-8')
        ->post($this->getApiEndpoint());
    }

    private function getApiEndpoint(): string
    {
        return config('ebay.sandbox') ? self::SANDBOX_EBAY_API_URL : self::PRODUCTION_EBAY_API_URL;
    }

    /**
     * Extract errors from response data
     */
    private function extractErrors(array $responseData): array
    {
        $errors = [];

        if (isset($responseData['Errors'])) {
            $errorList = $responseData['Errors'];

             // Handle both single error and array of errors
            if (isset($errorList['ErrorCode'])) {
                $errorList = [$errorList];
            }

            foreach ($errorList as $error) {
                $errors[] = [
                    'error_code' => $error['ErrorCode'] ?? null,
                    'short_message' => $error['ShortMessage'] ?? null,
                    'long_message' => $error['LongMessage'] ?? null,
                    'severity' => $error['SeverityCode'] ?? null,
                    'classification' => $error['ErrorClassification'] ?? null
                ];
            }
        }

        return $errors;
    }

    private function replaceEbayImageSize(string $imageUrl, string $newSize = '57'): string
    {
        return preg_replace('/\$_\d+/', '$_' . $newSize, $imageUrl);
    }
}
