<?php

namespace App\Module\Ebay\Services\Product\Handler;

use App\Exceptions\EbayApiRateLimitException;
use App\Exceptions\InvalidSpecificValueForPicturesException;
use App\Jobs\Product\FetchEbayProductWithItemIdJob;
use App\Models\Ebay\EbayUser;
use App\Models\ShopifyProduct;
use App\Module\Ebay\Repositories\Product\EbayProductUploadRepository;
use App\Module\Ebay\Services\Common\Formatter\ErrorDataFormatter;
use App\Module\Shopify\Enums\ShopifyProductUploadStatusEnum;
use DTS\eBaySDK\Trading\Types\AddFixedPriceItemResponseType;
use DTS\eBaySDK\Trading\Types\ErrorType;
use DTS\eBaySDK\Trading\Types\RelistFixedPriceItemResponseType;
use DTS\eBaySDK\Trading\Types\ReviseFixedPriceItemResponseType;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use App\Events\Product\EbayProductUpdated;
use App\Module\Ebay\Helper\Helper;

class ResponseHandler
{
    // eBay Error Code Constants
    private const RATE_LIMIT_ERROR = "518";
    private const AUCTION_ENDED_ERROR = "291";
    private const INVALID_PICTURES_ERROR_1 = "21916639";
    private const INVALID_PICTURES_ERROR_2 = "21916638";

    public function __construct(
        protected ErrorDataFormatter $errorDataFormatter,
        protected EbayProductUploadRepository $ebayProductUploadRepository
    ) {
    }

    /**
     * @throws EbayApiRateLimitException
     * @throws InvalidSpecificValueForPicturesException
     */
    public function handle(
        ShopifyProduct $shopifyProduct,
        EbayUser $ebayUser,
        ReviseFixedPriceItemResponseType|AddFixedPriceItemResponseType|RelistFixedPriceItemResponseType $response,
        array $context = []
    ): void {
        $this->ebayProductUploadRepository->removeStoredEbayProductUploadErrors($shopifyProduct->id);

        $hasAnyError = false;
        $hasInvalidSpecificValueForPicturesError = false;
        $isRelistContext = $context['is_relist'] ?? false;

        if (isset($response->Errors)) {
            Log::channel('daily')
                ->info(
                    "Error while trying to upload or update:",
                    [
                        'Shopify Session' => $shopifyProduct->shopify_session_id,
                        'Shopify Product Id' => $shopifyProduct->id,
                        'Ebay product Id' => $shopifyProduct->ebay_product_id,
                        'Profile Id' => $shopifyProduct->profile_id,
                        'Response' => $response,
                    ]
                );
            foreach ($response->Errors as $error) {
                if ($error->ErrorCode == self::RATE_LIMIT_ERROR) {
                    throw new EbayApiRateLimitException("API Rate limit reached. Retrying the job with delay.");
                }

                // Handle auction ended error with relist logic
                if ($error->ErrorCode == self::AUCTION_ENDED_ERROR && !$isRelistContext && $shopifyProduct->hasActiveInventory()) {
                    Log::channel('daily')->info('Auction ended error detected, attempting to relist item', [
                        'Shopify Product Id' => $shopifyProduct->id,
                        'Ebay product Id' => $shopifyProduct->ebay_product_id,
                        'Profile Id' => $shopifyProduct->profile_id,
                    ]);
                    
                    /** @var EbayProductRelistHandler $relistHandler */
                    $relistHandler = App::make(EbayProductRelistHandler::class);
                    $relistHandler->handle($ebayUser, $shopifyProduct);
                    return; // Exit early, relist handler will manage the response
                }

                $this->storeEbayRequestErrors($error, $shopifyProduct, $response->Message);

                if (!$hasAnyError && $error->SeverityCode == 'Error') {
                    $hasAnyError = true;
                    // Update the status immediately when we detect an error
                    $shopifyProduct->update(['upload_status' => ShopifyProductUploadStatusEnum::ERROR->value]);
                }

                if (in_array($error->ErrorCode, [self::INVALID_PICTURES_ERROR_1, self::INVALID_PICTURES_ERROR_2])) {
                    $hasInvalidSpecificValueForPicturesError = true;
                }
            }

            if ($hasInvalidSpecificValueForPicturesError) {
                Log::channel('daily')->error('InvalidSpecificValueForPictures', [
                    'Shopify Product Id' => $shopifyProduct->id,
                    'Ebay product Id' => $shopifyProduct->ebay_product_id,
                    'Profile Id' => $shopifyProduct->profile_id,
                ]);
                throw new InvalidSpecificValueForPicturesException(
                    "Invalid Specific Value For Pictures"
                );
            }
        }

        if (!$hasAnyError) {
            $this->saveSuccessUploadAndFetchEbayProduct(
                $response,
                $ebayUser,
                $shopifyProduct
            );


            if(!Helper::isEpsTestMode()){
                 // make sure the model reflects the DB state
                $shopifyProduct->refresh();

                if (
                    $shopifyProduct->profile_id 
                    && $shopifyProduct->ebay_product_id 
                    && $shopifyProduct->profile->image_sync 
                    && $shopifyProduct->session->eps_upload_enabled
                ) {
                    EbayProductUpdated::dispatch($shopifyProduct);
                }
            }

           
        }
    }

    private function storeEbayRequestErrors(
        ErrorType $error,
        ShopifyProduct $shopifyProduct,
        string $ebayMessage = null
    ): void {
        $errorData = $this->errorDataFormatter->format(
            $error,
            $shopifyProduct->id,
            $shopifyProduct->shopify_session_id,
            $ebayMessage
        );

        $this->ebayProductUploadRepository->storeEbayProductUploadErrors($errorData);
    }

    private function saveSuccessUploadAndFetchEbayProduct(
        AddFixedPriceItemResponseType|ReviseFixedPriceItemResponseType|RelistFixedPriceItemResponseType $response,
        EbayUser $ebayUser,
        ShopifyProduct $shopifyProduct
    ): void {
        $productData['ebay_product_id'] = $response->ItemID;
        $productData['upload_status'] = ShopifyProductUploadStatusEnum::SUCCESS->value;
        $this->ebayProductUploadRepository
            ->updateShopifyProductDetails($shopifyProduct->id, $productData);
        FetchEbayProductWithItemIdJob::dispatchSync(
            $response->ItemID,
            $ebayUser,
            $shopifyProduct->id
        );
    }
}
