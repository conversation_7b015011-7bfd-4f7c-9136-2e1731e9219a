<?php

namespace App\Module\Ebay\Services\Product;

use App\Exceptions\EbayAccessTokeOrRefreshTokenException;
use App\Module\Ebay\Repositories\Product\UploadErrorResolutionRepository;
use App\Module\Ebay\Services\Product\Contracts\ErrorResolverInterface;
use App\Module\Ebay\Services\Product\ErrorResolvers\PriceErrorResolver;
use App\Module\Ebay\Services\Product\ErrorResolvers\WeightErrorResolver;
use App\Module\Ebay\Services\Product\ErrorResolvers\ProductRelistErrorResolver;
use App\Module\Ebay\Services\Product\ErrorResolvers\DuplicateListingViolationErrorResolver;
use App\Module\Ebay\Services\Product\ErrorResolvers\QuantityInvalidErrorResolver;
use App\Module\Ebay\Services\Product\ErrorResolvers\TitleErrorResolver;
use App\Module\Ebay\Services\Product\ErrorResolvers\DescriptionMissingErrorResolver;
use App\Module\Ebay\Services\Product\ErrorResolvers\InventoryOverrideErrorResolver;
use App\Module\Ebay\Services\Product\ErrorResolvers\DisallowedCharacterErrorResolver;
use App\Module\Ebay\Services\Product\ErrorResolvers\DuplicateSkuInVariantsErrorResolver;
use App\Module\Ebay\Services\Product\ErrorResolvers\EndAndUploadProductAgainErrorResolver;
use App\Module\Ebay\Services\Product\ProductUpload\EbayFixedPriceItemUploadService;
use App\Models\Session;
use App\Models\ShopifyProduct;
use Illuminate\Support\Facades\Log;

class UploadErrorResolutionService
{
    public function __construct(
        protected UploadErrorResolutionRepository $uploadErrorResolutionRepository,
        protected EbayFixedPriceItemUploadService $ebayFixedPriceItemUploadService
    ) {
    }

    /**
     * @inheritDoc
     */
    public function resolveProductUpload(int $error_id, array $data, Session $session): bool
    {
        $error = $this->uploadErrorResolutionRepository->findError($error_id);
        if (!$error) {
            Log::warning('Error not found for resolution', ['error_id' => $error_id]);
            return false;
        }

        try {
            // Get the appropriate resolver for this error code
            $resolver = $this->getErrorResolver($error->error_code);
            if (!$resolver) {
                Log::warning('No resolver found for error code', [
                    'error_code' => $error->error_code,
                    'error_id' => $error_id
                ]);
                return false;
            }

            $shouldRedirect = $resolver->resolve($error, $data, $session);

            // If no redirect is needed, upload the product directly
            if (!$shouldRedirect) {
                $shopifyProduct = ShopifyProduct::find($error->shopify_product_id);
                if ($shopifyProduct) {
                    $this->ebayFixedPriceItemUploadService->uploadOrUpdate($shopifyProduct);
                }
            }

            return $shouldRedirect;

        } catch (EbayAccessTokeOrRefreshTokenException $e) {
            // Re-throw this specific exception for the controller to handle
            throw $e;
        } catch (\Exception $e) {
            Log::error('Error resolving product upload', [
                'error_code' => $error->error_code,
                'error_id' => $error_id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Get the appropriate error resolver for the given error code
     * Following the same pattern as ShopifyWebhookMessageHandler
     *
     * @param int $errorCode
     * @return ErrorResolverInterface|null
     */
    private function getErrorResolver(int $errorCode): ?ErrorResolverInterface
    {
        return match ($errorCode) {
            // Price related errors
            73 => app(PriceErrorResolver::class),
            
            // Weight related errors  
            717, 219021, 21916495 => app(WeightErrorResolver::class),
            
            // Product relist errors
            291 => app(ProductRelistErrorResolver::class),
            
            // Duplicate listing violation errors
            21919067 => app(DuplicateListingViolationErrorResolver::class),
            
            // Quantity invalid errors
            515, 942 => app(QuantityInvalidErrorResolver::class),
            
            // Title related errors
            70 => app(TitleErrorResolver::class),
            
            // Description missing errors
            106 => app(DescriptionMissingErrorResolver::class),
            
            // Inventory override errors
            21919188 => app(InventoryOverrideErrorResolver::class),
            
            // Disallowed character errors
            21920309 => app(DisallowedCharacterErrorResolver::class),
            
            // Duplicate SKU in variants errors
            21916585 => app(DuplicateSkuInVariantsErrorResolver::class),
            
            // End and upload product again errors
            1 => app(EndAndUploadProductAgainErrorResolver::class),
            
            default => null
        };
    }
}
