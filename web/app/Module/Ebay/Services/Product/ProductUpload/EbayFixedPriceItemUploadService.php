<?php

namespace App\Module\Ebay\Services\Product\ProductUpload;

use App\Events\Product\EbayProductUpdated;
use App\Exceptions\EbayAccessTokeOrRefreshTokenException;
use App\Exceptions\EbayApiRateLimitException;
use App\Exceptions\InvalidSpecificValueForPicturesException;
use App\Exceptions\VariationStateChangeException;
use App\Models\ShopifyProduct;
use App\Module\Ebay\DTO\ProgressData;
use App\Module\Ebay\Repositories\Product\EbayProductUploadRepository;
use App\Module\Ebay\Services\Product\Handler\EbayProductUpdateHandler;
use App\Module\Ebay\Services\Product\Handler\EbayProductUpdateWithoutProfileHandler;
use App\Module\Ebay\Services\Product\Handler\EbayProductUploadHandler;
use App\Module\Ebay\Services\Product\Handler\PreUploadErrorHandler;
use App\Module\Ebay\Trait\InteractsWithUploadErrorMessage;
use App\Module\Shopify\Services\MetaField\MetaFieldService;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Module\Ebay\Helper\Helper;

class EbayFixedPriceItemUploadService
{
    use InteractsWithUploadErrorMessage;

    public function __construct(
        protected EbayProductUploadRepository $ebayProductUploadRepo,
        protected EbayProductUpdateWithoutProfileHandler $ebayProductUpdateWithoutProfileHandler,
        protected EbayProductUpdateHandler $ebayProductUpdateHandler,
        protected EbayProductUploadHandler $ebayProductUploadHandler,
        protected PreUploadErrorHandler $preUploadErrorHandler,
        protected MetaFieldService $metaFieldService
    ) {
    }

    public function calculateProgress(array $progressData): ?ProgressData
    {
        $cacheData = Cache::get($progressData['cacheKey'], []);
        if (empty($cacheData)) {
            Log::channel('daily')->info("returning null because cache data is empty");
            return null;
        }
        $total = (int) $progressData['total'];
        $processed = (int) ($cacheData['processed'] ?? 0);
        $processed++;
        $progress = (float) ($processed / $total * 100);
        $progressData['processed'] = $processed;
        $progressData['progress'] = $progress;
        Cache::put($progressData['cacheKey'], [
            'processed' => $processed,
            'total' => $total,
        ], 86400);
        return ProgressData::make($progressData);
    }

    public function uploadOrUpdate(ShopifyProduct $shopifyProduct): void
    {
        Log::channel('daily')
            ->info(
                'Started upload or update process: ',
                [
                    'Shopify Product Id' => $shopifyProduct->id,
                    'Profile Id' => $shopifyProduct->profile_id,
                    'eBay Item Id' => $shopifyProduct->ebay_product_id,
                    'Session' => $shopifyProduct->shopify_session_id
                ]
            );
        try {
            if ($shopifyProduct->isNotIncludedInProfile()) {
                $this->ebayProductUpdateWithoutProfileHandler->handle($shopifyProduct);
                return;
            }
            $this->preUploadErrorHandler->handle($shopifyProduct);
            $this->metaFieldService->fetchPackageDimensionsIfNecessary($shopifyProduct);
          
            if (isset($shopifyProduct->ebay_product_id)) {
                $this->ebayProductUpdateHandler->handle($shopifyProduct);
               
                // executing eps upload in test mode
                if(Helper::isEpsTestMode()){
                    $epsUploadEnabled = $shopifyProduct->session?->eps_upload_enabled ?? false;
                    $imageSyncInProfile = $shopifyProduct->profile?->image_sync ?? false;
                    if($epsUploadEnabled && $imageSyncInProfile) {
                        EbayProductUpdated::dispatch($shopifyProduct);
                    }
                }

                return;
            }
            $this->ebayProductUploadHandler->handle($shopifyProduct);
             // executing eps upload in test mode
             if(Helper::isEpsTestMode()){
                $epsUploadEnabled = $shopifyProduct->session?->eps_upload_enabled ?? false;
                if($epsUploadEnabled) {
                    EbayProductUpdated::dispatch($shopifyProduct);
                }
             }

        } catch (VariationStateChangeException $e) {
            Log::channel('daily')->error("Variation state changed.", [
                'Session' => $shopifyProduct->shopify_session_id,
                'Shopify Product Id  ' => $shopifyProduct->id,
                'message' => $e->getMessage()
            ]);
        } catch (Exception $e) {
            Log::channel('daily')
                ->error(
                    "uploadOrUpdate Error: ",
                    [
                        'Session' => $shopifyProduct->shopify_session_id,
                        'Shopify Product Id' => $shopifyProduct->id,
                        'message' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine()
                    ]
                );
            if (
                $e instanceof EbayAccessTokeOrRefreshTokenException
                || $e instanceof EbayApiRateLimitException
                || $e instanceof InvalidSpecificValueForPicturesException
            ) {
                throw $e;
            }
        }
    }
}
