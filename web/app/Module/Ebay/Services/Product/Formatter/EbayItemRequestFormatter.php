<?php

namespace App\Module\Ebay\Services\Product\Formatter;

use App\Models\CategoryItemAspects;
use App\Models\Ebay\EbayProduct;
use App\Models\Ebay\EbayUser;
use App\Models\Ebay\Profile;
use App\Models\EbayCategories;
use App\Models\ShopifyProduct;
use App\Models\ShopifyProductVariant;
use App\Module\Ebay\Services\Product\DTO\GenerateItemRequestDTO;
use App\Module\Ebay\Services\Product\ProductUpload\EbayBusinessPoliciesConfigService;
use App\Module\Ebay\Services\Product\ProductUpload\ProfileBasedManipulationService;
use App\Services\Common\ArrayUtils;
use App\Services\Common\StringUtils;
use App\Services\Common\WeightConverter;
use App\Services\Shopify\ShopifyMetafieldService;
use App\Traits\AppUtilsTrait;
use DTS\eBaySDK\Trading\Enums\ListingDurationCodeType;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class EbayItemRequestFormatter extends BaseEbayItemRequestFormatter
{
    use AppUtilsTrait;

    public function __construct(
        protected EbayBusinessPoliciesConfigService $ebayBusinessPoliciesConfigService,
        protected WeightConverter $weightConverter,
        protected ProfileBasedManipulationService $profileBasedManipulationService,
        protected ShopifyMetafieldService $shopifyMetafieldService
    ) {
    }

    public function format(EbayUser $ebayUser, ShopifyProduct $shopifyProduct): GenerateItemRequestDTO
    {
        $isProductUpdate = !!$shopifyProduct->ebay_product_id;
        $firstVariation = $shopifyProduct->variations->first();
        $profile = $shopifyProduct->profile;
        $return_policy_id = $profile->return_policy_id;
        $shipping_policy_id = $profile->shipping_policy_id;
        $payment_policy_id = $profile->payment_policy_id;
        $category = EbayCategories::where('category_id', $profile->category_id)
            ->where('marketplace_id', $ebayUser->marketplace_id)
            ->first();
        $price_sync = !is_null($profile) && $profile->price_sync;
        $title_sync = !is_null($profile) && $profile->title_sync;
        $imageSync = !is_null($profile) && $profile->image_sync;
        $overrideInventoryBy = $profile->__get('override_inventory_by');
        $weight = $shopifyProduct->weight_override ?? $firstVariation->weight;
        $weight_unit = $shopifyProduct->weight_unit_override ?? $firstVariation->weight_unit;
        if ($ebayUser->site === 'US' && !in_array($weight_unit, ['lb', 'oz'])) {
            $weight = $this->weightConverter->convert($weight_unit, 'oz', $weight);
            $weight_unit = 'oz';
        }
        $item = [
            'EAN_enabled' => $category->EAN_enabled,
            'UPC_enabled' => $category->UPC_enabled,
            'ISBN_enabled' => $category->ISBN_enabled,
            'sub_conditions' => $profile->sub_conditions,
            'seller_profile' => $this->ebayBusinessPoliciesConfigService
                ->getSellerProfile($ebayUser, $shipping_policy_id, $return_policy_id, $payment_policy_id),
            'title' => ($isProductUpdate && !$title_sync) ? ''
                : $this->getTitle($shopifyProduct, $profile),
            'description' => $this->getDescription($isProductUpdate, $shopifyProduct),
            'featured_img_url' => $shopifyProduct['image_src'],
            'listing_duration' => ListingDurationCodeType::C_GTC,
            'currency' => $ebayUser->currency,
            'condition_id' => $profile->condition_id,
            'dispatch_time_max' => 1,
            'primary_category_id' => (string)$profile->category_id,
            'isVariable' => $shopifyProduct->isVariable,
            'itemId' => $shopifyProduct->ebay_product_id,
            'attributes_mapping' => $this
                ->getItemSpecificsValue($profile, $shopifyProduct, $category),
            'shopifyRowId' => $shopifyProduct->id,
            'weight' => $this->getWeights($weight, $weight_unit),
            'metaFields' => $this->getMetaFields($shopifyProduct->metafields),
            'is_variation_sync_enabled' => $profile->variation_sync ?? false,
            'primary_store_category_id' => !empty($profile->primary_store_category_id)
                ? (int)$profile->primary_store_category_id
                : null,
            'secondary_store_category_id' => !empty($profile->secondary_store_category_id)
                ? (int)$profile->secondary_store_category_id
                : null,
            'vat_percentage' => $profile->vat_percentage
        ];
        $item = [...$item, ...$this->prepareItemLocationData($ebayUser, $profile, $isProductUpdate)];
        if ($shopifyProduct->isVariable) {
            $item['options'] = $this->getOptionsData($shopifyProduct->options);
            $variations = $this->getVariationData(
                $profile,
                $shopifyProduct,
                $ebayUser,
                $overrideInventoryBy,
            );
            $item['variations'] = $variations['var'];
            $item['excluded_variant_image_ids'] = $variations['variant_image_ids'] ?? [];

            if (
                !$isProductUpdate
                || $imageSync
                || $shopifyProduct->hasInvalidSpecificValueForPictureError()
            ) {
                if ($ebayUser->session->image_sync_based_on_first_option) {
                    $item['images'] = $this->getImagesBasedOnFirstOption($shopifyProduct);
                } else {
                    $item['images'] = $this->getImages($shopifyProduct);
                }
            }
        } else {
            if (!$isProductUpdate || $price_sync) {
                $item['price'] = $this->profileBasedManipulationService->getUpdatedPrice(
                    $profile,
                    $firstVariation->price,
                    $ebayUser,
                    $shopifyProduct->price_override
                );
            }
            $item['barcode'] = $firstVariation->barcode ?? '';

            $sku = $this->getSKU($shopifyProduct, $firstVariation, $ebayUser);
            $item['sku'] = $sku;
            if (!$isProductUpdate || $imageSync) {
                $item['images'] = $this->getImages($shopifyProduct);
            }
            $overrideUntrackedOrContinueSellingQty = $ebayUser->userSetting
                ?->override_untracked_or_continue_selling_qty
                ?? 0;
            $item['quantity'] = $this->getQuantity(
                $overrideInventoryBy,
                $firstVariation,
                $overrideUntrackedOrContinueSellingQty
            );
        }
        return new GenerateItemRequestDTO($item);
    }

    private function getDescription(bool $isProductUpdate, ShopifyProduct $shopifyProduct): string
    {
        $description_sync = !is_null($shopifyProduct->profile) && $shopifyProduct->profile->description_sync;
        if ($isProductUpdate && !$description_sync) {
            return '';
        }
        if (empty($shopifyProduct['descriptionHtml'])) {
            $description = $shopifyProduct['description_override'];
        } else {
            $description = $shopifyProduct['descriptionHtml'];
        }
        return StringUtils::secureInsecureLinks(StringUtils::sanitizeHtml($description));
    }

    private function getMetafieldMappings(array $attributes): array
    {
        $metafieldMappings = [];
        $lookUpWord = 'Shopify Metafield: ';
        foreach ($attributes as $values) {
            if (is_array($values)) {
                foreach ($values as $value) {
                    if (is_string($value) && str_starts_with($value, $lookUpWord)) {
                        $metafieldMappings[] = str_replace($lookUpWord, '', $value);
                    }
                }
            }
        }
        return $metafieldMappings;
    }

    public function getItemSpecificsValue(
        Profile $profile,
        ShopifyProduct $shopifyProduct,
        EbayCategories $category
    ): array {
        $attributes = $profile->attributes_mapping;
        $attributes_max_length = $profile->attributes_max_length ?? [];
        $metafieldMappings = $this->getMetafieldMappings($attributes);
        $metafieldMappings = array_unique($metafieldMappings);
        $shopifyProductMetafields = [];
        if (!empty($metafieldMappings)) {
            $shopifyProductMetafields = $this->shopifyMetafieldService->fetchProductMetafields(
                $shopifyProduct->session,
                $shopifyProduct->shopify_product_id,
                $metafieldMappings
            );
        }
        $shopify_attributes = $this->shopifySuggestedAttributeMapping($shopifyProduct);
        $data = array();
        $optionsColumns = array_map('strtolower', array_column($shopifyProduct->options->toArray(), 'name'));
        $shouldUpdateProfile = false;

        foreach ($attributes as $key => $values) {
            if (isset($attributes_max_length[$key])) {
                $maxLength = $attributes_max_length[$key];
            } else {
                $itemAspects = null;
                if (!$itemAspects) {
                    $itemAspects = CategoryItemAspects::where('category_id', $category->id)->get();
                }
                $itemAspect = $itemAspects->where('localized_aspect_name', $key)->first();
                $maxLength = $itemAspect->attributes['aspectConstraint']['aspectMaxLength'] ?? 65;
                $attributes_max_length[$key] = $maxLength;
                $shouldUpdateProfile = true;
            }
            if (in_array(strtolower($key), $optionsColumns)) {
                //skip matching item specifics and variation values
                continue;
            }
            if (!is_array($values)) {
                continue;
            }
            $values = array_filter($values);

            $data[] = [
                'name' => $key,
                'value' => array_map(function ($value) use (
                    $maxLength,
                    $shopifyProductMetafields,
                    $shopify_attributes
                ) {
                    if (is_string($value) && str_starts_with($value, 'Shopify Metafield: ')) {
                        $metafieldKey = str_replace('Shopify Metafield: ', '', $value);
                        $metafieldValue = $shopifyProductMetafields[$metafieldKey] ?? '';
                        return  $maxLength ? $this->truncateString($metafieldValue, $maxLength) : $metafieldValue;
                    }

                    $attributeValue = Arr::get($shopify_attributes, $value, $value);
                    return $maxLength ? $this->truncateString($attributeValue, $maxLength) : $attributeValue;
                }, $values)
            ];
        }
        if ($shouldUpdateProfile) {
            $shopifyProduct->profile()->update(['attributes_max_length' => $attributes_max_length]);
        }
        return $data;
    }

    private function prepareItemLocationData(EbayUser $ebayUser, Profile $profile, bool $isProductUpdate): array
    {
        if ($profile->item_location_country_code !== null) {
            $itemLocationData['country'] = $profile->item_location_country_code;
            if ($profile->item_location_post_code) {
                $itemLocationData['postal_code'] = $profile->item_location_post_code;
            }
            if (!$isProductUpdate || !$profile->item_location_post_code) {
                $itemLocationData['location'] = $profile->item_location_city ?? '';
            }
            return $itemLocationData;
        }

        $registrationAddress = json_decode($ebayUser->registration_address, true) ?? [];

        $itemLocationData['country'] = Arr::get($registrationAddress, 'Country', 'ZZ');
        $itemPostalCode = Arr::get($registrationAddress, 'PostalCode', 'default');
        if ($itemPostalCode !== 'default') {
            $itemLocationData['postal_code'] = $itemPostalCode;
        }
        if (!$isProductUpdate || $itemPostalCode === 'default') {
            $itemLocationData['location'] = StringUtils::joinWords([
                Arr::get($registrationAddress, 'CityName', ''),
                Arr::get($registrationAddress, 'StateOrProvince', ''),
            ], ', ');
        }
        return $itemLocationData;
    }

    private function getWeights(float $weight, string $weight_unit): array
    {
        if ($weight_unit === 'oz') {
            $weight = ceil($weight);
            $weight_major = floor($weight / 16);
            $weight_major_unit = 'lbs';
            $weight_minor = ceil(fmod($weight, 16));
            $weight_minor_unit = 'oz';
            if ($weight_minor > 15) {
                ++$weight_major;
                $weight_minor = 0;
            }
        } elseif ($weight_unit === 'g' || $weight_unit === 'gm') {
            $weight = ceil($weight);
            $weight_major = floor($weight / 1000);
            $weight_major_unit = 'kg';
            $weight_minor = ceil(fmod($weight, 1000));
            $weight_minor_unit = 'gr';
            if ($weight_minor > 999) {
                ++$weight_major;
                $weight_minor = 0;
            }
        } elseif ($weight_unit === 'kg') {
            $weight_major = floor($weight);
            $weight_major_unit = 'kg';
            $weight_minor = ceil(($weight - $weight_major) * 1000);
            $weight_minor_unit = 'gr';
        } else {
            $weight_major = floor($weight);
            $weight_major_unit = 'lbs';
            $weight_minor = ceil(($weight - $weight_major) * 16);
            $weight_minor_unit = 'oz';
            if ($weight_minor > 15) {
                ++$weight_major;
                $weight_minor = 0;
            }
        }
        return [
            'weight_major' => $weight_major,
            'weight_unit_major' => $weight_major_unit,
            'weight_minor' => $weight_minor,
            'weight_unit_minor' => $weight_minor_unit,
        ];
    }

    private function getMetaFields($metaFields): array
    {
        $dimensions = [];

        foreach ($metaFields as $metaField) {
            $key = $metaField->key;
            $unit = $metaField->unit;
            $value = $metaField->value;
            $dimensions[$key] = [
                'value' => $value,
                'unit' => $unit
            ];
        }

        return $dimensions;
    }

    private function getTitle(ShopifyProduct $shopifyProduct, Profile $profile)
    {

        $prefix = $profile->title_prefix ?? null;
        $suffix = $profile->title_suffix ?? null;

        $eBayMaxTitleLength = 80;

        $characterLimit = $eBayMaxTitleLength - ($prefix ? strlen($prefix) + 1 : 0) - ($suffix ? strlen($suffix) + 1 : 0);

        $optimizedTitle = Arr::get($shopifyProduct->meta_info, 'optimization.title');

        if ($optimizedTitle) {
            return $this->formatFullTitle($prefix, $suffix, $optimizedTitle);
        }

        $originalTitle = $shopifyProduct->title;

        if (strlen($originalTitle) <= $characterLimit) {
            return $this->formatFullTitle($prefix, $suffix, $originalTitle);
        }

        $profileAutoTruncateEnabled = $profile->truncate_title ?? false;

        if ($profileAutoTruncateEnabled) {
            $truncatedTitle = $this->truncateString($originalTitle, $characterLimit);
            $shopifyProduct->update(['title_override' => $truncatedTitle]);
            return $this->truncateString($this->formatFullTitle($prefix, $suffix, $originalTitle), $eBayMaxTitleLength);
        }

        $shopifyProductTitleOverrideExist = !empty($shopifyProduct->title_override);

        if ($shopifyProductTitleOverrideExist) {
            return $this->formatFullTitle($prefix, $suffix, $shopifyProduct->title_override);
        }

        return $this->formatFullTitle($prefix, $suffix, $originalTitle);
    }

    private function formatFullTitle(?string $prefix, ?string $suffix, string $title): string
    {
        if ($prefix) {
            $title = "$prefix $title";
        }
        if ($suffix) {
            $title = "$title $suffix";
        }
        return $title;
    }

    public function getVariationData(
        Profile $profile,
        ShopifyProduct $shopifyProduct,
        EbayUser $ebayUser,
        int $overrideInventoryBy,
    ): array {
        $options = $shopifyProduct['options'];
        $data = array();
        $shopifyVariations = [];
        $skuMap = [];
        $overrideUntrackedOrContinueSellingQty = $ebayUser->userSetting
            ?->override_untracked_or_continue_selling_qty
            ?? 0;

        foreach ($shopifyProduct->variations as $variation) {
            $sku = $this->getSKU($shopifyProduct, $variation, $ebayUser, $skuMap);
            $skuMap[] = $sku;
            $var = [
                'sku' => $sku,
                'barcode' => $variation->barcode ?? '',
                'currency' => $ebayUser->currency,
                'specifics' => $this->getFormattedVariationSpecificsData($variation, $options),
                'original_price' => $variation->price,
                'quantity' => $this->getQuantity(
                    $overrideInventoryBy,
                    $variation,
                    $overrideUntrackedOrContinueSellingQty
                )
            ];
            if ($profile->price_sync || !$shopifyProduct->ebay_product_id) {
                $var['price'] = $this->profileBasedManipulationService->getUpdatedPrice(
                    $profile,
                    $variation->price,
                    $ebayUser,
                    $shopifyProduct->price_override
                );
            }
            $shopifyVariations[] = $var;
        }
        if ($shopifyProduct->ebay_product_id) {
            $eBayVariations = $this->getLinkedEbayProductDetails(
                $ebayUser->__get('id'),
                $shopifyProduct->ebay_product_id
            );
            $allEbayVariantsOptions = array_column($eBayVariations, 'specifics');
            $allShopifyVariantsOptions = array_column($shopifyVariations, 'specifics');
            $removedShopifyVariantsOptions = array_filter(
                $allEbayVariantsOptions,
                static function ($ebayOptions) use ($allShopifyVariantsOptions) {
                    return !in_array($ebayOptions, $allShopifyVariantsOptions, true);
                }
            );
            if (ArrayUtils::areArraysEqual($removedShopifyVariantsOptions, $allShopifyVariantsOptions)) {
                $data['var'] = [];
                return $data;
            }
            $addedShopifyOptions = array_filter(
                $allShopifyVariantsOptions,
                static function ($ebayOptions) use ($allEbayVariantsOptions) {
                    return !in_array($ebayOptions, $allEbayVariantsOptions, true);
                }
            );
            foreach ($addedShopifyOptions as $option) {
                foreach ($shopifyVariations as $key => $variation) {
                    if ($variation['specifics'] === $option) {
                        if (!$profile->variation_sync) {
                            unset($shopifyVariations[$key]);
                            break;
                        }
                        if (!$profile->price_sync) {
                            $variation['price'] = $this->profileBasedManipulationService->getUpdatedPrice(
                                $profile,
                                $variation['original_price'],
                                $ebayUser,
                                $shopifyProduct->price_override
                            );
                        }
                        $variation['is_added'] = true;
                        $shopifyVariations[$key] = $variation;
                        break;
                    }
                }
            }
            foreach ($removedShopifyVariantsOptions as $option) {
                foreach ($eBayVariations as $variation) {
                    if ($variation['specifics'] === $option) {
                        $variation['is_removed'] = true;
                        $shopifyVariations[] = $variation;
                        break;
                    }
                }
            }
        }
        $data['var'] = $shopifyVariations;
        return $data;
    }

    public function getImages(ShopifyProduct $shopifyProduct): array
    {
        $images = $shopifyProduct['images']->sortBy('position');
        $mainImage = $images->where('position', 0)->pluck('url', 'image_id')->toArray();

        if (!$shopifyProduct->isVariable) {
            $galleryImages = $images->skip(0)->take(24)->pluck('url', 'image_id')->toArray();

            return [
                'main_image' => $mainImage,
                'picture_gallery' => array_values($galleryImages),
                'picture_gallery_image_id_url_map' => $galleryImages,
                'variants_image' => [],
                'variants_image_id_to_option_url_map' => [],
                'variants_option_name' => ''
            ];
        }

        // handle if variable product

        $options = array_map(static function ($i) {
                return "option" . $i;
        }, range(1, $shopifyProduct->options->count()));

        $favourableSubset = [];
        $position = 1;
        foreach ($options as $option) {
            $subset = $shopifyProduct->variations->map(function ($variation) use ($option) {
                if ($variation->$option && $variation->image_id) {
                    return $variation->$option . "-$variation->image_id";
                }
                return false;
            })->reject(function ($value) {
                return $value === false;
            })->unique()->all();
            $subsetCount = count($subset);
            if (count($favourableSubset) === 0 || $subsetCount < count($favourableSubset)) {
                $favourableSubset = $subset;
                $position = str_replace("option", "", $option);
            }
        }

        $excludedVariantImages = [];
        foreach ($favourableSubset as $item) {
            $parts = explode('-', $item);
            $variantImageId = array_pop($parts);
            $variationOption = implode('-', $parts);
            $excludedVariantImages[$variantImageId] = $variationOption;
        }
        $excludedVariantImages = array_unique($excludedVariantImages);

        $variantSpecificOptionName = $shopifyProduct->options->where('position', $position)->first()?->name ?? '';


        $variantImages = [];
        $excludedVariantImageIds = array_keys($excludedVariantImages);
        $variantImageDetails = $images
            ->whereIn('image_id', $excludedVariantImageIds)
            ->pluck('url', 'image_id')
            ->toArray();

        foreach ($excludedVariantImages as $variantImageId => $optionName) {
            if (isset($variantImageDetails[$variantImageId])) {
                $variantImages[$variantImageId] = [$optionName => $variantImageDetails[$variantImageId]];
            }
        }

        $variantImages = array_filter($variantImages);

        $images = $images->filter(function ($image) use ($excludedVariantImageIds) {
            return $image->position === 0 || !in_array($image['image_id'], $excludedVariantImageIds);
        });



        // eBay allows a maximum of 24 gallery images
        $galleryImages = $images->skip(0)->take(24)->pluck('url', 'image_id')->toArray();

        return [
            'main_image' => $mainImage,
            'picture_gallery' => array_values($galleryImages),
            'picture_gallery_image_id_url_map' => $galleryImages,
            'variants_image' => array_values($variantImages),
            'variants_image_id_to_option_url_map' => $variantImages,
            'variants_option_name' => $variantSpecificOptionName
        ];
    }

    public function getImagesBasedOnFirstOption(ShopifyProduct $shopifyProduct): array
    {
        $images = $shopifyProduct['images']->sortBy('position');
        $mainImage = $images->where('position', 0)->pluck('url', 'image_id')->toArray();
        if (!$shopifyProduct->isVariable) {
            $galleryImageIdUrlMap = $images->skip(0)->take(24)->pluck('url', 'image_id')->toArray();
            return [
                'main_image' => $mainImage,
                'picture_gallery' => array_values($galleryImageIdUrlMap),
                'picture_gallery_image_id_url_map' => $galleryImageIdUrlMap,
                'variants_image' => [],
                'variants_image_id_to_option_url_map' => [],
                'variants_option_name' => ''
            ];
        }
        $variantSpecificOptionName = $shopifyProduct->options()->orderBy('position', 'asc')->first()->name;
        $uniqueVariations = $shopifyProduct->variations->whereNotNull('image_id')->unique('option1');

        $variantImageIdToOptionUrlMap = [];
        foreach ($uniqueVariations as $variation) {
            $image = $images->where('image_id', $variation->image_id)->first();
            if ($image && $image->url) {
                $variantImageIdToOptionUrlMap[$image->image_id] = [$variation->option1 => $image->url];
            } 
        }

        $images = $images->filter(function ($image) use ($variantImageIdToOptionUrlMap) {
            return $image->position === 0 || !in_array($image['url'], Arr::flatten($variantImageIdToOptionUrlMap));
        });
        $galleryImageIdUrlMap = $images->skip(0)->take(24)->pluck('url', 'image_id')->toArray();

        // Extract unique values
        $variantOptionUrlMap = collect($variantImageIdToOptionUrlMap)
            ->unique(function ($item) {
                return Arr::first($item); // Get the first (only) value in the associative array
            })
            ->values() // Reset array keys
            ->toArray();
        return [
            'main_image' => $mainImage,
            'picture_gallery' => array_values($galleryImageIdUrlMap),
            'picture_gallery_image_id_url_map' => $galleryImageIdUrlMap,
            'variants_image' => array_values($variantOptionUrlMap),
            'variants_image_id_to_option_url_map' => $variantImageIdToOptionUrlMap,
            'variants_option_name' => $variantSpecificOptionName
        ];
    }
    private function getQuantity(
        int $overrideInventoryBy,
        ShopifyProductVariant $variation,
        ?int $overrideUntrackedOrContinueSellingQty
    ) {
        $isNotTracked = $variation->isNotTracked();
        $continueSelling = $variation->continueSellingWhenOutOfStock();
        $inventory = $variation->getQuantity();

        if (
            $inventory < $overrideUntrackedOrContinueSellingQty
            && ($isNotTracked || $continueSelling)
            && $overrideUntrackedOrContinueSellingQty > 0
        ) {
            $inventory = $overrideUntrackedOrContinueSellingQty;
        }

        return (
            $overrideInventoryBy
            && (
                $inventory >= $overrideInventoryBy
                || ($isNotTracked || $continueSelling)
            )
        ) ? $overrideInventoryBy : $inventory;
    }

    public function shopifySuggestedAttributeMapping(ShopifyProduct $shopifyProduct): array
    {
        $tags = '';
        if ($shopifyProduct->tags) {
            $shopifyProduct->tags = json_decode($shopifyProduct->tags, true);
            if (is_array($shopifyProduct->tags)) {
                $tags = implode(',', $shopifyProduct->tags);
            }
        }
        return array(
            'shopify_title' => $shopifyProduct->title,
            'shopify_tags' => $tags,
            'shopify_product_type' => $shopifyProduct->product_type,
            'shopify_handle' => $shopifyProduct->handle,
            'shopify_vendor' => $shopifyProduct->vendor,
            'shopify_barcode' => ($shopifyProduct->getFirstVariant())?->barcode ?? 'N/A',
            'shopify_sku' => ($shopifyProduct->getFirstVariant())?->sku ?? '',
        );
    }

    private function getLinkedEbayProductDetails(int $ebayUserId, int $ebayProductId): array
    {
        $resultArray = [];
        $ebayProd = EbayProduct::with('variations:quantity,product_id,option_values,sku,price')
            ->where('ebay_user_id', $ebayUserId)
            ->where('item_id', $ebayProductId)
            ->first();

        // Check if the product exists
        if (!$ebayProd) {
            return $resultArray;
        }

        $optionName = json_decode($ebayProd->options, true);

        foreach ($ebayProd->variations as $variation) {
            $options = [];
            if (is_array(json_decode($variation->option_values, true))) {
                $options = array_map(function ($value, $name) {
                    return [
                        'name' => trim($name),
                        'value' => $value,
                    ];
                }, json_decode($variation->option_values, true), $optionName);
            }
            $resultArray[] = [
                'sku' => $variation->sku,
                'quantity' => $variation->quantity,
                'price' => $variation->price ?? null,
                'currency' => $ebayProd->currency,
                'specifics' => $options,
            ];
        }

        return $resultArray;
    }
}
