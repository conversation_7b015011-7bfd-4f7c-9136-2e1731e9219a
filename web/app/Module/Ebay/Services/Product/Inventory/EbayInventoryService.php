<?php

namespace App\Module\Ebay\Services\Product\Inventory;

/*
 * This service file contains code for updating inventory from Shopify to eBay which matches the SKU of the product
 */

use App\Exceptions\EbayAccessTokeOrRefreshTokenException;
use App\Jobs\EndEbayListingWithItemIdOnly;
use App\Models\Ebay\EbayUser;
use App\Models\Ebay\EbayVariation;
use App\Models\ShopifyProduct;
use App\Models\ShopifyProductVariant;
use App\Module\Ebay\Helper\Helper;
use App\Module\Ebay\Repositories\Product\EbayInventoryRepository;
use App\Module\Ebay\Services\API\RestfulService;
use DTS\eBaySDK\Trading\Enums\AckCodeType;
use DTS\eBaySDK\Trading\Services\TradingService;
use Exception;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class EbayInventoryService
{
    protected TradingService $service;
    protected EbayInventoryRepository $repo;
    protected array $serviceArgs;

    public function __construct(EbayInventoryRepository $repo)
    {
        $this->repo = $repo;
    }

    /**
     * @throws EbayAccessTokeOrRefreshTokenException
     * @throws Exception
     */
    public function updateEbayProductInventory(
        EbayUser $ebayUser,
        EbayVariation $ebayVariant,
        array $productData,
    ): void {
        $data = array(
            'is_inventory_syncing' => false
        );
        $quantity = $this->getQuantityToUpdate($ebayUser, $ebayVariant, $productData);
        $this->serviceArgs = Helper::eBayCredentials($ebayUser);
        $this->service = new TradingService($this->serviceArgs);
        $config = new EbayInventoryConfigService();
        if ($quantity === ($ebayVariant->quantity - $ebayVariant->quantity_sold)) {
            Log::channel('daily')
                ->debug('Inventory same during updateEbayProductInventory ', [
                    'eBay username' => $ebayUser->ebay_user_name,
                    'productData' => $productData,
                    'quantityToUpdate' => $quantity,
                    'ebayVariantId' => $ebayVariant->id
                ]);
            return;
        }
        $req = $config->generateReviseInventoryRequest($ebayVariant, $productData['sku'], $quantity);
        $response = $this->service->reviseInventoryStatus($req);
        Log::channel('daily')
            ->info('Request and Response during updateEbayProductInventory', [
                "eBay username" => $ebayUser->ebay_user_name,
                'Request data' => $req,
                'productData' => $productData,
                'ebayVariantId' => $ebayVariant->id,
                "ACK" => isset($response->Ack) ? $response->Ack : 'No ACK',
                "Errors" => Arr::get($response->toArray(), 'Errors', 'No Errors')
            ]);

        if (isset($response->Ack) && $response->Ack === AckCodeType::C_FAILURE && isset($response->Errors)) {
            foreach ($response->Errors as $error) {
                if ($response->Ack === AckCodeType::C_FAILURE && $error->ErrorCode === '21919474') {
                    $this->updateEbayProductInventoryWithInventoryAPI(
                        $this->serviceArgs['authorization'],
                        $productData['sku'],
                        $quantity,
                        $ebayUser->id
                    );
                    $data['quantity'] = $req->InventoryStatus[0]->Quantity + $ebayVariant->quantity_sold;
                }
                if ($response->Ack == AckCodeType::C_WARNING && $error->ErrorCode === '21917092') {
                    // The existing quantity value is identical to the quantity specified in the request and,
                    // therefore, is not modified.
                    $data['quantity'] = $req->InventoryStatus[0]->Quantity + $ebayVariant->quantity_sold;
                }

                if (
                    $response->Ack == AckCodeType::C_FAILURE &&
                    ($error->ErrorCode === '21916333' || $error->ErrorCode === '21916750')
                ) {
                    $ebayVariant->ebayProduct->update([
                        'listing_status' => "Ended"
                    ]);
                    Log::channel('daily')->error("eBay Item already Ended during updateEbayProductInventory", [
                        "eBay username" => $ebayUser->ebay_user_name,
                        "productData" => $productData,
                        "Request Data" => $req,
                    ]);
                }

                if (
                    $response->Ack == AckCodeType::C_FAILURE &&
                    ($error->ErrorCode === '515' || $error->ErrorCode === '942')
                ) {
                    $data['quantity'] = $req->InventoryStatus[0]->Quantity;
                    EndEbayListingWithItemIdOnly::dispatch(
                        $ebayUser,
                        $req->InventoryStatus[0]->ItemID
                    )->onQueue('end_listing');
                }
            }
            $ebayVariant->update($data);
            return;
        }
        $data['quantity'] = $req->InventoryStatus[0]->Quantity + $ebayVariant->quantity_sold;
        $ebayVariant->update($data);
    }
    public function getQuantityToUpdate(EbayUser $ebayUser, EbayVariation $ebayVariant, array $productData)
    {
        $quantity = $productData['inventory_quantity'];
        $isNotTracked = Arr::get($productData, 'is_not_tracked', false);
        $continueSelling = Arr::get($productData, 'continue_selling', false);
        $isTracked = !$isNotTracked && !$continueSelling;
        $profile = $this->getProfile($ebayVariant);
        $quantityOverride = 0;
        if($profile) {
            $profileQuantityOverride = $profile->override_inventory_by;
            if($profileQuantityOverride) {
                if($quantity > $profileQuantityOverride) {
                    return $profileQuantityOverride;
                }
            }
        }
        if($isTracked) {
            return $quantity;
        }
        $quantityOverride = $ebayUser->userSetting?->override_untracked_or_continue_selling_qty ?? 0;

        if($quantityOverride && $quantity < $quantityOverride) {
            return $quantityOverride;
        }
        return $quantity;
    }

    public function getProfile(EbayVariation $ebayVariant)
    {
        $ebayItemId = $ebayVariant->ebayProduct()
            ->select('id', 'ebay_user_id', 'item_id')
            ->where('ebay_user_id', $ebayVariant->__get('ebay_user_id'))
            ->first()->item_id;

        $shopifyProduct = ShopifyProduct::with('profile')
            ->where('shopify_session_id', $ebayVariant->__get('shopify_session_id'))
            ->where('ebay_product_id', $ebayItemId)->first();
        return $shopifyProduct?->profile;
    }

    public function getEbayInventoryListings(
        $ebayUserId,
        $sessionId,
        $page,
        $search = null,
        $hasShopifyVariant = null
    ): LengthAwarePaginator {
        // Step 1: Get paginated distinct SKUs
        $paginatedEbayVariants = EbayVariation::query()
            ->select('sku')
            ->where('ebay_user_id', $ebayUserId)
            // Conditional optimization: Only include SKUs that exist in ShopifyProductVariants when filtering is requested
            ->when($hasShopifyVariant === true, function ($query) use ($sessionId) {
                $query->whereExists(function ($query) use ($sessionId) {
                    $query->select(DB::raw(1))
                        ->from('shopify_product_variants')
                        ->whereColumn('shopify_product_variants.sku', 'ebay_variations.sku')
                        ->where('shopify_product_variants.session_id', $sessionId);
                });
            })
            // Search functionality with optimized indexes
            ->when($search, function ($query) use ($search) {
                $query->where(function ($query) use ($search) {
                    $query->where('sku', 'LIKE', "%$search%")
                        ->orWhereHas('ebayProduct', function ($query) use ($search) {
                            $query->where('title', 'LIKE', "%$search%");
                        });
                });
            })
            ->distinct('sku')
            ->paginate(10, page: $page)
            ->withQueryString();

        $skus = $paginatedEbayVariants->getCollection()->pluck('sku')->toArray();

        // Step 2: Get all variations for the paginated SKUs with optimized count
        // Now benefits from new indexes for faster execution
        $ebayVariations = EbayVariation::query()
            ->select([
                'id',
                'sku',
                'product_id',
                'ebay_user_id',
                'quantity',
                'quantity_sold'
            ])
            // Add shopifySkuCount using optimized subquery with new indexes
            ->addSelect([
                'shopifySkuCount' => ShopifyProductVariant::query()
                    ->where('session_id', $sessionId)
                    ->whereColumn('sku', 'ebay_variations.sku')
                    ->selectRaw('COUNT(*)')
                    ->limit(1)
            ])
            ->with('ebayProduct:id,title,image_url,listing_status')
            ->where('ebay_user_id', $ebayUserId)
            ->whereIn('sku', $skus)
            ->get();

        // Group variations by SKU (case-insensitive)
        $groupedVariations = $ebayVariations->groupBy(function ($item) {
            return strtolower($item->sku);
        });

        // Replace collection with grouped data
        $paginatedEbayVariants->setCollection($groupedVariations);

        return $paginatedEbayVariants;
    }

    private function calculatePaginationUrls($page, $lastPage, $perPage = 10): array
    {
        $nextPageUrl = ($page < $lastPage) ? request()->fullUrlWithQuery(['page' => $page + 1]) : null;
        $prevPageUrl = ($page > 1) ? request()->fullUrlWithQuery(['page' => $page - 1]) : null;

        return [
            'current_page' => $page,
            'last_page' => $lastPage,
            'per_page' => $perPage,
            'next_page_url' => $nextPageUrl,
            'prev_page_url' => $prevPageUrl,
        ];
    }

    public function getShopifyVariantsBySku(int $session_id, string $sku)
    {
        $shopify_products = $this->repo->getAllShopifyProductsData($session_id);
        $shopify_product_variants = $this->repo->getAllShopifyVariantsData($session_id, $sku);

        $shopify_product_variants->map(function ($variant) use ($shopify_products) {
            $variant['shopify_option_value'] = $variant->__get('title');
            $shopify_product = $shopify_products->where('id', $variant->shopify_product_id)->first();
            $variant['title'] = $shopify_product->title;
            $variant['img'] = $shopify_product->image_src;
            $variant['isShopify'] = 1;
            return $variant;
        });

        return $shopify_product_variants->groupBy('sku');
    }

    public function updateEbayProductInventoryWithInventoryAPI(
        string $accessToken,
        string $sku,
        int $quantity,
        int $ebayUserId
    ) {
        Log::channel('daily')->info(
            "Syncing inventory with Inventory API",
            [
                'ebayUserId' => $ebayUserId,
                'sku' => $sku,
                'quantity' => $quantity
            ]
        );

        $headers = ['Content-Type' => 'application/json', 'Content-Language' => 'en-US'];

        $service = new RestfulService($accessToken, $headers);

        $getOfferUri = "https://api.ebay.com/sell/inventory/v1/offer?sku=$sku";

        $updateInventoryUri = "https://api.ebay.com/sell/inventory/v1/bulk_update_price_quantity";

        if (config('ebay.sandbox') && config('ebay.mockAPI')) {
            $getOfferUri = "https://api.sandbox.ebay.com/sell/inventory/v1/offer?sku=$sku";
            $updateInventoryUri = "https://api.sandbox.ebay.com/sell/inventory/v1/bulk_update_price_quantity";
        }

        $skuOfferResponse = $service->performAPICall($getOfferUri);

        $skufOfferId = (($skuOfferResponse->offers)[0])->offerId ?? null;

        if ($skufOfferId === null) {
            Log::channel('daily')->info(
                'func:updateEbayProductInventoryWithInventoryAPI - No SKU Offer found',
                [
                    'ebayUserId' => $ebayUserId,
                    'sku' => $sku,
                    'response' => $skuOfferResponse
                ]
            );
            return;
        }

        $skufOfferId = (($skuOfferResponse->offers)[0])->offerId ?? null;

        $payload = [
            "requests" => [
                [
                    "offers" => [
                        [
                            "availableQuantity" => $quantity,
                            "offerId" => $skufOfferId
                        ]
                    ],
                    "shipToLocationAvailability" => [
                        "quantity" => $quantity
                    ],
                    "sku" => $sku
                ]
            ]
        ];

        $response =  $service->performPostAPICall($updateInventoryUri, $payload);

        Log::channel('daily')->info(
            'Response in updateEbayProductInventoryWithInventoryAPI',
            [
                'ebayUserId' => $ebayUserId,
                'sku' => $sku,
                'quantity' => $quantity,
                'requestData' => $payload,
                'inventoryUpdateMethod' => 'bulkUpdatePriceQuantity',
                'response' => json_decode($response->body()) ?? ''
            ]
        );

        if (isset(json_decode($response->body())->errors)) {
            Log::channel('daily')->error('Error:updateEbayProductInventoryWithInventoryAPI ' . $response->body());
        }
    }
}
