<?php

namespace App\Module\Ebay\Services\Product\Contracts;

use App\Models\Session;
use App\Models\ShopifyProductError;

interface ErrorResolverInterface
{
    /**
     * Resolve the product upload error
     *
     * @param ShopifyProductError $error
     * @param array $data
     * @param Session $session
     * @return bool Whether a redirect is needed
     */
    public function resolve(ShopifyProductError $error, array $data, Session $session): bool;

    /**
     * Get the error codes this resolver can handle
     *
     * @return array
     */
    public function getHandledErrorCodes(): array;
} 