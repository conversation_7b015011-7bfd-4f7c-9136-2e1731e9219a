<?php

namespace App\Module\Ebay\Services\Product\ErrorResolvers;

use App\Models\Ebay\EbayUserSetting;
use App\Models\Session;
use App\Models\ShopifyProductError;
use App\Module\Ebay\Repositories\Product\UploadErrorResolutionRepository;
use App\Module\Ebay\Services\Product\ProductUpload\EbayFixedPriceItemUploadService;

class EndAndUploadProductAgainErrorResolver extends AbstractErrorResolver
{
    public function __construct(
        protected UploadErrorResolutionRepository $uploadErrorResolutionRepository,
        protected EbayFixedPriceItemUploadService $ebayFixedPriceItemUploadService
    ) {
        parent::__construct($ebayFixedPriceItemUploadService);
    }

    /**
     * @inheritDoc
     */
    public function resolve(ShopifyProductError $error, array $data, Session $session): bool
    {
        $applyToAll = $data['obey_variation_state_change_for_all'] ?? false;
        $applyToSelectedProdOnly = $data['obey_variation_state_change_for_this_prod_only'] ?? false;
        $productRowId = $data['shopify_row_id'] ?? null;

        if ($applyToSelectedProdOnly && !$applyToAll && $productRowId) {
            $error->shopifyProduct()
                ->where('id', $productRowId)
                ->update(['variation_state_override' => true]);
            return false; // No redirect needed for single product
        }

        if ($applyToAll) {
            return $this->handleBulkVariationStateChange($session);
        }
        
        return false;
    }

    /**
     * Handle bulk variation state change for all products with this error
     *
     * @param Session $session
     * @return bool
     */
    private function handleBulkVariationStateChange(Session $session): bool
    {
        // Update user setting to obey variation state changes
        EbayUserSetting::where('ebay_user_id', $session->ebayUser->id)
            ->update(['obey_variation_state_change' => true]);
            
        $ids = $this->getProductIdsByErrorCode($session->session_id, 1);
        $products = $this->uploadErrorResolutionRepository->fetchShopifyProducts($session->id, $ids);
        
        if ($products->isEmpty()) {
            return false;
        }

        // Create and dispatch upload jobs
        $jobs = $this->createUploadJobs($products, $session);
        $this->dispatchUploadResolutionJobs($jobs, $session->session_id);

        return true; // Redirect needed
    }

    /**
     * @inheritDoc
     */
    public function getHandledErrorCodes(): array
    {
        return [1];
    }
} 