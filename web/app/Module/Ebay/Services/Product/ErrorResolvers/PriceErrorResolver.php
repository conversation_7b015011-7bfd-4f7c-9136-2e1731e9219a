<?php

namespace App\Module\Ebay\Services\Product\ErrorResolvers;

use App\Models\Session;
use App\Models\ShopifyProductError;

class PriceErrorResolver extends AbstractErrorResolver
{
    /**
     * @inheritDoc
     */
    public function resolve(ShopifyProductError $error, array $data, Session $session): bool
    {
        $error->shopifyProduct()->update(['price_override' => $data['price']]);
        
        return false; // No redirect needed
    }

    /**
     * @inheritDoc
     */
    public function getHandledErrorCodes(): array
    {
        return [73];
    }
} 