<?php

declare(strict_types=1);

namespace App\Module\Ebay\Services\Product\ErrorResolvers;

use App\Models\Session;
use App\Models\ShopifyInventoryLevel;
use App\Models\ShopifyProduct;
use App\Models\ShopifyProductError;
use App\Models\ShopifyProductVariant;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Shopify\Clients\Graphql;

class QuantityInvalidErrorResolver extends AbstractErrorResolver
{
    /**
     * @inheritDoc
     */
    public function resolve(ShopifyProductError $error, array $data, Session $session): bool
    {
        $shopifyProduct = $error->shopifyProduct;
        
        // Validate required data
        if (!isset($data['locationId']) || !isset($data['quantity'])) {
            Log::channel('daily')->error('Missing required data for quantity invalid error resolution', [
                'product_id' => $shopifyProduct->id,
                'data' => $data
            ]);
            return false;
        }
        
        try {
            $this->updateShopifyInventory($shopifyProduct, (int) $data['quantity'], (string) $data['locationId']);
            
            Log::channel('daily')->info('Quantity invalid error resolved successfully', [
                'product_id' => $shopifyProduct->id,
                'location_id' => $data['locationId'],
                'quantity' => $data['quantity']
            ]);
            
            return false; // Return success to indicate resolution worked
        } catch (\Exception $e) {
            Log::channel('daily')->error('Failed to resolve quantity invalid error', [
                'product_id' => $shopifyProduct->id,
                'location_id' => $data['locationId'],
                'quantity' => $data['quantity'],
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * @inheritDoc
     */
    public function getHandledErrorCodes(): array
    {
        return [515, 942];
    }

    /**
     * Update Shopify inventory for all variants in a specific location
     */
    public function updateShopifyInventory(ShopifyProduct $shopifyProduct, int $quantity, string $locationId): void
    {
        $session = Session::with('ebayUser')->where('session_id', $shopifyProduct->shopify_session_id)->first();
        
        if (!$session) {
            throw new \Exception('No Shopify session found for inventory update');
        }
        
        $client = new Graphql($session->shop, $session->access_token);
        
        // Get ALL product variants (not just the ones with inventory item IDs)
        $variants = $shopifyProduct->variations;
        if ($variants->isEmpty()) {
            Log::channel('daily')->warning('No variants found for inventory update', [
                'session_id' => $session->session_id,
                'location_id' => $locationId,
                'product_id' => $shopifyProduct->id,
            ]);
            throw new \Exception('No variants found for inventory update');
        }
        
        // Check activation status and handle accordingly
        $this->handleInventoryActivationAndQuantities($client, $variants, $quantity, $locationId);
        
        // Update local database records
        $this->syncLocalInventory($variants, $quantity, $locationId);
    }
    
    /**
     * Handle inventory activation and quantity setting based on current activation status
     */
    private function handleInventoryActivationAndQuantities(Graphql $client, Collection $variants, int $quantity, string $locationId): void
    {
        
        // Check which variants already have inventory activated at this location
        $variantIds = $variants->pluck('id')->toArray();
        $activatedVariantIds = ShopifyInventoryLevel::whereIn('shopify_variant_id', $variantIds)
            ->where('shopify_location_id', $locationId)
            ->pluck('shopify_variant_id')
            ->toArray();
        
        // Separate variants into two groups
        $needActivation = $variants->filter(function (ShopifyProductVariant $variant) use ($activatedVariantIds): bool {
            return !in_array($variant->id, $activatedVariantIds);
        });
        $alreadyActivated = $variants->filter(function (ShopifyProductVariant $variant) use ($activatedVariantIds): bool {
            return in_array($variant->id, $activatedVariantIds);
        });
        
        Log::channel('daily')->info('Inventory activation status analysis', [
            'total_variants' => $variants->count(),
            'need_activation' => $needActivation->count(),
            'already_activated' => $alreadyActivated->count(),
            'location_id' => $locationId
        ]);
        
        // Handle variants that need activation (use inventoryActivate)
        if ($needActivation->isNotEmpty()) {
            $this->activateNewInventoryItems($client, $needActivation, $quantity, $locationId);
        }
        
        // Handle variants that are already activated (use inventoryAdjustQuantities)
        if ($alreadyActivated->isNotEmpty()) {
            $this->updateExistingInventoryQuantities($client, $alreadyActivated, $quantity, $locationId);
        }
    }
    
    /**
     * Activate inventory tracking and set quantities for new variants using inventoryActivate
     */
    private function activateNewInventoryItems(Graphql $client, Collection $variants, int $quantity, string $locationId): void
    {
        $activationMutation = '
            mutation inventoryActivate($inventoryItemId: ID!, $locationId: ID!, $available: Int) {
                inventoryActivate(inventoryItemId: $inventoryItemId, locationId: $locationId, available: $available) {
                    userErrors {
                        field
                        message
                    }
                    inventoryLevel {
                        id
                         quantities(names: ["available"]) {
                            name
                            quantity
                        }
                        item {
                            id
                        }
                        location {
                            id
                        }
                    }
                }
            }
        ';
        
        $successCount = 0;
        $errorCount = 0;
        
        foreach ($variants as $variant) {
            /** @var ShopifyProductVariant $variant */
            $variables = [
                'inventoryItemId' => "gid://shopify/InventoryItem/{$variant->inventory_item_id}",
                'locationId' => "gid://shopify/Location/{$locationId}",
                'available' => $quantity  // Set the target quantity directly
            ];
            
            try {
                $response = $client->query(['query' => $activationMutation, 'variables' => $variables]);
                $responseBody = json_decode($response->getBody()->getContents(), true);
                
                if (!empty($responseBody['data']['inventoryActivate']['userErrors'])) {
                    $errors = $responseBody['data']['inventoryActivate']['userErrors'];
                    Log::channel('daily')->error('Failed to activate inventory and set quantity for variant', [
                        'variant_id' => $variant->id,
                        'inventory_item_id' => $variant->inventory_item_id,
                        'location_id' => $locationId,
                        'target_quantity' => $quantity,
                        'errors' => $errors
                    ]);
                    $errorCount++;
                } else {
                    $inventoryLevel = $responseBody['data']['inventoryActivate']['inventoryLevel'] ?? null;
                    $actualQuantity = $inventoryLevel['available'] ?? 'unknown';
                    
                    Log::channel('daily')->info('Successfully activated inventory and set quantity for variant', [
                        'variant_id' => $variant->id,
                        'inventory_item_id' => $variant->inventory_item_id,
                        'location_id' => $locationId,
                        'target_quantity' => $quantity,
                        'actual_quantity' => $actualQuantity
                    ]);
                    $successCount++;
                }
            } catch (\Exception $e) {
                Log::channel('daily')->error('Exception during inventory activation and quantity setting', [
                    'variant_id' => $variant->id,
                    'inventory_item_id' => $variant->inventory_item_id,
                    'location_id' => $locationId,
                    'target_quantity' => $quantity,
                    'error' => $e->getMessage()
                ]);
                $errorCount++;
            }
        }
        
        Log::channel('daily')->info('Inventory activation and quantity setting completed', [
            'total_variants' => $variants->count(),
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'target_quantity' => $quantity,
            'location_id' => $locationId
        ]);
    }
    
    /**
     * Update inventory quantities for already activated variants using inventorySetQuantities
     */
    private function updateExistingInventoryQuantities(Graphql $client, Collection $variants, int $quantity, string $locationId): void
    {
        // Build quantities array for inventorySetQuantities
        $quantities = [];
        foreach ($variants as $variant) {
            /** @var ShopifyProductVariant $variant */
            $quantities[] = [
                'inventoryItemId' => "gid://shopify/InventoryItem/{$variant->inventory_item_id}",
                'locationId' => "gid://shopify/Location/{$locationId}",
                'quantity' => $quantity
            ];
        }
        
        $this->executeInventorySetQuantities($client, $quantities, $locationId, $quantity);
    }
    

    
    /**
     * Execute inventory set quantities using inventorySetQuantities
     */
    private function executeInventorySetQuantities(Graphql $client, array $quantities, string $locationId, int $quantity): void
    {
        $inventoryMutation = '
            mutation InventorySet($input: InventorySetQuantitiesInput!) {
                inventorySetQuantities(input: $input) {
                    inventoryAdjustmentGroup {
                        createdAt
                        reason
                        referenceDocumentUri
                        changes {
                            name
                            delta
                        }
                    }
                    userErrors {
                        field
                        message
                    }
                }
            }
        ';
        
        $variables = [
            'input' => [
                'ignoreCompareQuantity' => true,
                'name' => 'available',
                'reason' => 'correction',
                'referenceDocumentUri' => 'ebay://quantity-invalid-error-resolution',
                'quantities' => $quantities
            ]
        ];
        
        try {
            $response = $client->query([
                'query' => $inventoryMutation,
                'variables' => $variables
            ]);
            
            $responseBody = json_decode($response->getBody()->getContents(), true);
            
            // Check for GraphQL errors first
            if (isset($responseBody['errors'])) {
                throw new \Exception('GraphQL errors: ' . json_encode($responseBody['errors']));
            }
            
            if (!empty($responseBody['data']['inventorySetQuantities']['userErrors'])) {
                $errors = $responseBody['data']['inventorySetQuantities']['userErrors'];
                throw new \Exception('Shopify inventory set quantities failed: ' . json_encode($errors));
            }
            
            Log::channel('daily')->info('Successfully set inventory quantities', [
                'location_id' => $locationId,
                'quantities_count' => count($quantities),
                'target_quantity' => $quantity
            ]);
            
        } catch (\Exception $e) {
            Log::channel('daily')->error('Exception during inventory set quantities', [
                'location_id' => $locationId,
                'quantities_count' => count($quantities),
                'quantity' => $quantity,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    
    /**
     * Update local variant inventory records
     */
    private function syncLocalInventory(Collection $variants, int $quantity, string $locationId): void
    {
        try {
            foreach ($variants as $variant) {
                /** @var ShopifyProductVariant $variant */
                // Update the selected_locations_inventory field
                $variant->update([
                    'selected_locations_inventory' => $quantity
                ]);
            }
            Log::channel('daily')->info('Local inventory synced successfully', [
                'variants_count' => $variants->count(),
                'location_id' => $locationId,
                'quantity' => $quantity
            ]);
            
        } catch (\Exception $e) {
            Log::channel('daily')->error('Error updating local variant inventory', [
                'location_id' => $locationId,
                'variants_count' => $variants->count(),
                'error' => $e->getMessage()
            ]);
            
            // Don't throw here - Shopify update succeeded, local sync is secondary
        }
    }
} 