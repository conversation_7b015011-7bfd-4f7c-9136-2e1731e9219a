<?php

namespace App\Module\Ebay\Services\Product\ErrorResolvers;

use App\Models\Session;
use App\Models\ShopifyProductError;
use App\Module\Ebay\Services\Product\Handler\EbayProductRelistHandler;
use App\Module\Ebay\Services\Product\ProductUpload\EbayFixedPriceItemUploadService;

class ProductRelistErrorResolver extends AbstractErrorResolver
{
    public function __construct(
        protected EbayProductRelistHandler $ebayProductRelistHandler,
        protected EbayFixedPriceItemUploadService $ebayFixedPriceItemUploadService
    ) {
        parent::__construct($ebayFixedPriceItemUploadService);
    }

    /**
     * @inheritDoc
     */
    public function resolve(ShopifyProductError $error, array $data, Session $session): bool
    {
        $shopifyProduct = $error->shopifyProduct;
        $this->ebayProductRelistHandler->handle($session->ebayUser, $shopifyProduct);
        
        return false; // No redirect needed
    }

    /**
     * @inheritDoc
     */
    public function getHandledErrorCodes(): array
    {
        return [291];
    }
} 