<?php

namespace App\Module\Ebay\Services\Product\ErrorResolvers;

use App\Models\Session;
use App\Models\ShopifyProductError;
use Illuminate\Support\Arr;

class DuplicateListingViolationErrorResolver extends AbstractErrorResolver
{
    /**
     * @inheritDoc
     */
    public function resolve(ShopifyProductError $error, array $data, Session $session): bool
    {
        $shopifyProduct = $error->shopifyProduct;
        $productMetaInfo = $shopifyProduct->meta_info;

        Arr::set($productMetaInfo, 'optimization.title', $data['resolved_title']);

        $shopifyProduct->update(['meta_info' => $productMetaInfo]);
        
        return false; // No redirect needed
    }

    /**
     * @inheritDoc
     */
    public function getHandledErrorCodes(): array
    {
        return [21919067];
    }
} 