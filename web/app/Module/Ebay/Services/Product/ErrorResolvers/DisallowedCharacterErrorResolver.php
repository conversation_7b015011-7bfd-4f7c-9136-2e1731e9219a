<?php

namespace App\Module\Ebay\Services\Product\ErrorResolvers;

use App\Models\Ebay\EbayUserSetting;
use App\Models\Session;
use App\Models\ShopifyProduct;
use App\Models\ShopifyProductError;

class DisallowedCharacterErrorResolver extends AbstractErrorResolver
{
    /**
     * @inheritDoc
     */
    public function resolve(ShopifyProductError $error, array $data, Session $session): bool
    {
        $error->shopifyProduct()->update(['title_override' => $data['title']]);
        
        if ($data['apply_to_all'] && $data['clean_title']) {
            return $this->handleBulkTitleCleaning($session);
        }
        
        return false; // No redirect needed for single product
    }

    /**
     * Handle bulk title cleaning for all products with disallowed character errors
     *
     * @param Session $session
     * @return bool
     */
    private function handleBulkTitleCleaning(Session $session): bool
    {
        // Update user setting to automatically clean titles
        EbayUserSetting::where('ebay_user_id', $session->ebayUser->id)
            ->update(['remove_disallowed_character' => true]);
            
        $products = ShopifyProduct::select('id', 'title', 'title_override', 'shopify_session_id')
            ->where('session_id', $session->id)
            ->whereIn('id', $this->getProductIdsByErrorCode($session->session_id, 21920309))
            ->get();
            
        if ($products->isEmpty()) {
            return false;
        }

        // Clean titles for all products
        foreach ($products as $product) {
            $product->title_override = preg_replace(
                '/[`$^|<*>~=+]/s',
                '',
                $product->title_override ?? $product->title
            );
            $product->save();
        }

        // Create and dispatch upload jobs
        $jobs = $this->createUploadJobs($products, $session);
        $this->dispatchUploadResolutionJobs($jobs, $session->session_id);

        return true; // Redirect needed
    }

    /**
     * @inheritDoc
     */
    public function getHandledErrorCodes(): array
    {
        return [21920309];
    }
} 