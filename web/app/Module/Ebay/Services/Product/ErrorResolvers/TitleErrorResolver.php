<?php

namespace App\Module\Ebay\Services\Product\ErrorResolvers;

use App\Models\Session;
use App\Models\ShopifyProduct;
use App\Models\ShopifyProductError;
use App\Module\Ebay\Repositories\Product\UploadErrorResolutionRepository;
use App\Module\Ebay\Services\Product\ProductUpload\EbayFixedPriceItemUploadService;

class TitleErrorResolver extends AbstractErrorResolver
{
    public function __construct(
        protected UploadErrorResolutionRepository $uploadErrorResolutionRepository,
        protected EbayFixedPriceItemUploadService $ebayFixedPriceItemUploadService
    ) {
        parent::__construct($ebayFixedPriceItemUploadService);
    }

    /**
     * @inheritDoc
     */
    public function resolve(ShopifyProductError $error, array $data, Session $session): bool
    {
        $error->shopifyProduct()->update(['title_override' => $data['title']]);

        if ($data['apply_to_all'] && $data['auto_truncate']) {
            return $this->handleBulkTitleTruncation($error, $session);
        }

        return false; // No redirect needed for single product
    }

    /**
     * Handle bulk title truncation for all products in the profile
     *
     * @param ShopifyProductError $error
     * @param Session $session
     * @return bool
     */
    private function handleBulkTitleTruncation(ShopifyProductError $error, Session $session): bool
    {
        $shopifyProduct = $error->shopifyProduct;
        $profile = $shopifyProduct->profile;

        $profile->update(['truncate_title' => true]);

        $profileProductsWithTitleError = ShopifyProduct::query()
            ->select('id', 'title', 'title_override', 'profile_id', 'shopify_session_id')
            ->with('profile:id,title_prefix,title_suffix')
            ->where('session_id', $session->id)
            ->where('profile_id', $profile->id)
            ->whereHas('errors', function ($query) {
                $query->where('error_code', 70);
            })
            ->get();

        if ($profileProductsWithTitleError->isEmpty()) {
            return false;
        }

        // Update title overrides for all products
        foreach ($profileProductsWithTitleError as $product) {
            $characterLimit = 80 - ($product->profile->title_prefix ? strlen($product->profile->title_prefix) + 1 : 0);
            $characterLimit -= ($product->profile->title_suffix ? strlen($product->profile->title_suffix) + 1 : 0);
            $product->title_override = $this->truncateString($product->title, $characterLimit);
            $product->save();
        }

        // Create and dispatch upload jobs
        $jobs = $this->createUploadJobs($profileProductsWithTitleError, $session);
        $this->dispatchUploadResolutionJobs($jobs, $session->session_id);

        return true; // Redirect needed
    }

    /**
     * @inheritDoc
     */
    public function getHandledErrorCodes(): array
    {
        return [70];
    }
} 