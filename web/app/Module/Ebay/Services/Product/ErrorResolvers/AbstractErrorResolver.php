<?php

namespace App\Module\Ebay\Services\Product\ErrorResolvers;

use App\Events\Product\UpdateShopifyPageProgressInPusher;
use App\Jobs\UploadOrUpdateProductToEbayJob;
use App\Models\Session;
use App\Models\ShopifyProduct;
use App\Models\ShopifyProductError;
use App\Module\Ebay\Helper\Helper;
use App\Module\Ebay\Services\Product\Contracts\ErrorResolverInterface;
use App\Module\Ebay\Services\Product\ProductUpload\EbayFixedPriceItemUploadService;
use App\Traits\AppUtilsTrait;
use Illuminate\Support\Facades\Bus;

abstract class AbstractErrorResolver implements ErrorResolverInterface
{
    use AppUtilsTrait;

    public function __construct(
        protected EbayFixedPriceItemUploadService $ebayFixedPriceItemUploadService
    ) {
    }

    /**
     * Get product IDs by error code and session
     *
     * @param string $sessionId
     * @param int $errorCode
     * @return array
     */
    protected function getProductIdsByErrorCode(string $sessionId, int $errorCode): array
    {
        return ShopifyProductError::select('shopify_product_id')
            ->where('shopify_session_id', $sessionId)
            ->where('error_code', $errorCode)
            ->get()
            ->pluck('shopify_product_id')
            ->all();
    }

    /**
     * Dispatch upload resolution jobs
     *
     * @param array $jobs
     * @param string $sessionId
     * @return void
     */
    protected function dispatchUploadResolutionJobs(array $jobs, string $sessionId): void
    {
        if (count($jobs)) {
            Bus::chain($jobs)
                ->onQueue('product_upload_to_ebay_by_user')
                ->catch(function () use ($sessionId) {
                    $values = [
                        'progress' => 100,
                        'shopify_user_id' => $sessionId,
                        'error' => 'refresh_token_expired'
                    ];
                    broadcast(new UpdateShopifyPageProgressInPusher($values))->toOthers();
                })
                ->onConnection('redis')
                ->dispatch();
        }
    }

    /**
     * Create upload jobs for multiple products
     *
     * @param iterable $products
     * @param Session $session
     * @return array
     */
    protected function createUploadJobs(iterable $products, Session $session): array
    {
        $cacheKey = Helper::generateCacheKeyAndAddCacheData(['progress' => 0, 'processed' => 0]);
        $jobs = [];
        
        foreach ($products as $key => $product) {
            $pusher = [
                'total' => count($products),
                'current' => $key + 1,
                'cacheKey' => $cacheKey,
            ];
            $jobs[] = new UploadOrUpdateProductToEbayJob(
                $product->id,
                $session->id,
                0,
                $pusher
            );
        }
        
        return $jobs;
    }

    /**
     * Upload or update a single product after resolution
     *
     * @param ShopifyProduct $shopifyProduct
     * @return void
     */
    protected function uploadProduct(ShopifyProduct $shopifyProduct): void
    {
        $this->ebayFixedPriceItemUploadService->uploadOrUpdate($shopifyProduct);
    }
} 