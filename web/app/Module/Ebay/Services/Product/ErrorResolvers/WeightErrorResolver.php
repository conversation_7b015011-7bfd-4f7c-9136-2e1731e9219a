<?php

namespace App\Module\Ebay\Services\Product\ErrorResolvers;

use App\Models\Session;
use App\Models\ShopifyProductError;

class WeightErrorResolver extends AbstractErrorResolver
{
    /**
     * @inheritDoc
     */
    public function resolve(ShopifyProductError $error, array $data, Session $session): bool
    {
        $error->shopifyProduct()->update([
            'weight_override' => $data['weight'],
            'weight_unit_override' => $data['weight_unit']
        ]);
        
        return false; // No redirect needed
    }

    /**
     * @inheritDoc
     */
    public function getHandledErrorCodes(): array
    {
        return [717, 219021, 21916495];
    }
} 