<?php

namespace App\Module\Ebay\Services\Product\ErrorResolvers;

use App\Models\Ebay\EbayUserSetting;
use App\Models\Session;
use App\Models\ShopifyProduct;
use App\Models\ShopifyProductError;
use App\Module\Ebay\Repositories\Product\UploadErrorResolutionRepository;
use App\Module\Ebay\Services\Product\ProductUpload\EbayFixedPriceItemUploadService;

class DuplicateSkuInVariantsErrorResolver extends AbstractErrorResolver
{
    public function __construct(
        protected UploadErrorResolutionRepository $uploadErrorResolutionRepository,
        protected EbayFixedPriceItemUploadService $ebayFixedPriceItemUploadService
    ) {
        parent::__construct($ebayFixedPriceItemUploadService);
    }

    /**
     * @inheritDoc
     */
    public function resolve(ShopifyProductError $error, array $data, Session $session): bool
    {
        $applyToAll = $data['apply_to_all'] ?? false;
        $applyToSelectedProdOnly = $data['apply_to_current_product_only'] ?? false;
        $productRowId = $data['shopify_row_id'] ?? null;

        if ($applyToSelectedProdOnly && $productRowId) {
            $error->shopifyProduct()->where('id', $productRowId)->update(['duplicate_sku_override' => true]);
            return false; // No redirect needed for single product
        }

        if ($applyToAll) {
            return $this->handleBulkDuplicateSkuResolution($session);
        }
        
        return false;
    }

    /**
     * Handle bulk duplicate SKU resolution for all products with this error
     *
     * @param Session $session
     * @return bool
     */
    private function handleBulkDuplicateSkuResolution(Session $session): bool
    {
        // Update user setting to automatically remove duplicate SKUs
        EbayUserSetting::where('ebay_user_id', $session->ebayUser->id)
            ->update(['remove_duplicate_sku' => true]);
            
        $ids = $this->getProductIdsByErrorCode($session->session_id, 21916585);
        $products = $this->uploadErrorResolutionRepository->fetchShopifyProducts($session->id, $ids);
        
        if ($products->isEmpty()) {
            return false;
        }

        // Create and dispatch upload jobs
        $jobs = $this->createUploadJobs($products, $session);
        $this->dispatchUploadResolutionJobs($jobs, $session->session_id);

        return true; // Redirect needed
    }

    /**
     * @inheritDoc
     */
    public function getHandledErrorCodes(): array
    {
        return [21916585];
    }
} 