<?php

namespace App\Module\Ebay\Services\Product\ErrorResolvers;

use App\Models\Session;
use App\Models\ShopifyProduct;
use App\Models\ShopifyProductError;

class DescriptionMissingErrorResolver extends AbstractErrorResolver
{
    /**
     * @inheritDoc
     */
    public function resolve(ShopifyProductError $error, array $data, Session $session): bool
    {
        $error->shopifyProduct()->update(['description_override' => $data['description']]);
        
        if ($data['apply_to_all'] && $data['map_to_title']) {
            return $this->handleBulkDescriptionMapping($session);
        }
        
        return false; // No redirect needed for single product
    }

    /**
     * Handle bulk description mapping to title for all products with this error
     *
     * @param Session $session
     * @return bool
     */
    private function handleBulkDescriptionMapping(Session $session): bool
    {
        $products = ShopifyProduct::select('id', 'title', 'title_override', 'shopify_session_id')
            ->where('session_id', $session->id)
            ->whereIn('id', $this->getProductIdsByErrorCode($session->session_id, 106))
            ->get();
            
        if ($products->isEmpty()) {
            return false;
        }

        // Update description override to match title for all products
        foreach ($products as $product) {
            $product->description_override = $product->title;
            $product->save();
        }

        // Create and dispatch upload jobs
        $jobs = $this->createUploadJobs($products, $session);
        $this->dispatchUploadResolutionJobs($jobs, $session->session_id);

        return true; // Redirect needed
    }

    /**
     * @inheritDoc
     */
    public function getHandledErrorCodes(): array
    {
        return [106];
    }
} 