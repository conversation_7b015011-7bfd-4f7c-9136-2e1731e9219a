<?php

namespace App\Module\Ebay\Services\Product\ErrorResolvers;

use App\Models\Session;
use App\Models\ShopifyProduct;
use App\Models\ShopifyProductError;
use Illuminate\Support\Facades\Log;

class InventoryOverrideErrorResolver extends AbstractErrorResolver
{
    /**
     * @inheritDoc
     */
    public function resolve(ShopifyProductError $error, array $data, Session $session): bool
    {
        $shopifyProduct = $error->shopifyProduct;
        $profile = $shopifyProduct->profile;
        
        // Update the profile with the new inventory override value
        $profile->update([
            'override_inventory_by' => $data['override_inventory_by']
        ]);

        // If apply_to_all_in_current_profile is true, update all products with the same error in this profile
        if ($data['apply_to_all_in_current_profile'] ?? false) {
            return $this->handleBulkInventoryOverride($session, $profile);
        }
        
        return false; // No redirect needed for single product
    }

    /**
     * Handle bulk inventory override for all products in the profile with this error
     *
     * @param Session $session
     * @param mixed $profile
     * @return bool
     */
    private function handleBulkInventoryOverride(Session $session, $profile): bool
    {
        // Directly query for products in the specific profile with the inventory override error
        $profileProducts = ShopifyProduct::query()
            ->select('id', 'shopify_session_id', 'profile_id')
            ->where('session_id', $session->id)
            ->where('profile_id', $profile->id)
            ->whereHas('errors', function ($query) {
                $query->where('error_code', 21919188);
            })
            ->get();

        Log::channel('daily')->info('Resolving inventory override error', [
            'session_id' => $session->id,
            'profile_id' => $profile->id,
            'override_inventory_by' => $profile->override_inventory_by,
            'affected_products_count' => $profileProducts->count()
        ]);

        if ($profileProducts->count() > 0) {
            // Create and dispatch upload jobs
            $jobs = $this->createUploadJobs($profileProducts, $session);
            $this->dispatchUploadResolutionJobs($jobs, $session->session_id);
            
            return true; // Redirect needed
        }
        
        return false;
    }

    /**
     * @inheritDoc
     */
    public function getHandledErrorCodes(): array
    {
        return [21919188];
    }
} 