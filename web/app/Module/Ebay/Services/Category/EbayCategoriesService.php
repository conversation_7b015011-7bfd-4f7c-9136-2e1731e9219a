<?php

namespace App\Module\Ebay\Services\Category;

use App\Models\CategoryItemAspects;
use App\Exceptions\EbayAccessTokeOrRefreshTokenException;
use App\Models\Ebay\EbayUser;
use App\Models\EbayCategories;
use App\Models\EbayCategoryItemConditionsMetadata;
use App\Module\Ebay\DTO\EbaySiteMap;
use App\Module\Ebay\Helper\Helper;
use App\Module\Ebay\Services\API\RestfulService;
use DTS\eBaySDK\Taxonomy\Services\TaxonomyService;
use DTS\eBaySDK\Taxonomy\Types\GetACategorySubtreeRestRequest;
use DTS\eBaySDK\Taxonomy\Types\GetItemAspectsForCategoryRestRequest;
use DTS\eBaySDK\Trading\Enums\DetailLevelCodeType;
use DTS\eBaySDK\Trading\Services\TradingService;
use DTS\eBaySDK\Trading\Types\GetCategoryFeaturesRequestType;
use Exception;
use Illuminate\Support\Facades\Log;

class EbayCategoriesService
{
    protected $service;
    protected EbayUser $ebayUser;

    public function __construct()
    {
        if (!config('ebay.sandbox')) {
            $ebayUser = EbayUser::where('ebay_user_name', 'testuser_exportfeed')
                ->orWhere('ebay_user_name', 'subasghimir_3')
                ->first();
        } else {
            $ebayUser = EbayUser::first();
        }
        $this->ebayUser = $ebayUser;
        $this->service = new TaxonomyService(
            Helper::eBayCredentialsForRest($ebayUser, false)
        );
        $this->tradingService = new TradingService(
            Helper::eBayCredentials($ebayUser)
        );
    }

    public function fetchAndSaveAspects($categories)
    {
        $response = '';
        try {
            foreach ($categories as $category) {
                $response = $this->fetchAndStoreItemAspect($category);
            }
        } catch (Exception $exception) {
            Log::channel('daily')->error($exception);
            Log::channel('daily')->error($response);
            Log::channel('daily')->error($category);
        }
        return true;
    }

    /**
     * @throws Exception
     */
    public function fetchAndStoreItemAspect($category, $ebayUser = null)
    {
        if (!$ebayUser || $ebayUser->refresh_token_expired) {
            $ebayUser = $this->ebayUser;
        }
        $service = new RestfulService(Helper::getEbayAccessToken($ebayUser));
        $request = new GetItemAspectsForCategoryRestRequest();
        $category_id = $request->category_id = (string)$category->category_id;
        $category_tree_id=$request->category_tree_id = (string)$category->category_tree_id;
        $uri = "https://api.ebay.com/commerce/taxonomy/v1/category_tree/$category_tree_id/get_item_aspects_for_category?category_id=$category_id";
        if (config('ebay.sandbox')) {
            $uri = "https://api.sandbox.ebay.com/commerce/taxonomy/v1/category_tree/$category_tree_id/get_item_aspects_for_category?category_id=$category_id";
        }
        $response = $service->performAPICall($uri);
        if (!isset($response->aspects)) {
            Log::channel('daily')->error('Problem with the get item aspects for category request.', [
                'category' => $category->category_id,
                'ebayUser' => $ebayUser->ebay_user_name,
                'response' => json_encode($response),
            ]);
            throw new Exception('Problem with the get item aspects for category request. Error:'. json_encode($response));
        }
        foreach ($response->aspects as $aspect) {

            $data = CategoryItemAspects::where('category_id', $category->id)
                ->where('localized_aspect_name', $aspect->localizedAspectName)
                ->first();

            if (!$data) {
               CategoryItemAspects::create([
                    'category_id' => $category->id,
                    'localized_aspect_name' => $aspect->localizedAspectName,
                    'ebay_category_id' => $category->category_id,
                    'marketplace_id' => $category->marketplace_id,
                    'attributes' => $aspect,
                ]);
            } else {
                $data->update(['attributes' => $aspect]);
            }
        }
        return $response;
    }

    public function fetchAndStoreEbayCategoryTree($category_tree_id, $category_id, $marketplace_id)
    {
        try {
            $request = new GetACategorySubtreeRestRequest();
            $request->category_id = $category_id;
            $request->category_tree_id = $category_tree_id;
            Log::channel('daily')->info(
                'func:fetchAndStoreEbayCategoryTree Fetching categories with TaxonomyService getACategorySubtree first_user'
            );
            $response = $this->service->getACategorySubtree($request)->toArray();
            $category_nodes = $response['categorySubtreeNode']['childCategoryTreeNodes'];
            $category = $response['categorySubtreeNode']['category']['categoryName'];
            $this->saveCategory($category_tree_id, $marketplace_id, $category_nodes, $category);
        } catch (Exception $e) {
            logger($e);
        }
    }

    public function saveCategory($category_tree_id, $marketplace_id, $category_node, $category = '')
    {
        foreach ($category_node as $node) {
            $this->saveLeafNode($category_tree_id, $marketplace_id, $node, $category);
        }
    }

    private function saveLeafNode($category_tree_id, $marketplace_id, $node, string $category)
    {
        if ($category) {
            $category .= " >> ";
        }
        $category .= $node['category']['categoryName'];
        if (array_key_exists('leafCategoryTreeNode', $node)) {
            $ebayCategory = EbayCategories::where('marketplace_id', '=', $marketplace_id)
                ->where('category_id', $node['category']['categoryId'])
                ->first();
            if ($ebayCategory) {
                $ebayCategory->update([
                    'category_name' => $category,
                    'category_tree_id' => $category_tree_id,
                ]);
            } else {
                EbayCategories::create([
                    'marketplace_id' => $marketplace_id,
                    'category_id' => $node['category']['categoryId'],
                    'category_name' => $category,
                    'category_tree_id' => $category_tree_id,
                ]);
            }
        } else {
            $this->saveCategory($category_tree_id, $marketplace_id, $node['childCategoryTreeNodes'], $category);
        }
    }

    public function fetchAndStoreEbayCategoryFeatures(EbayCategories $category, EbayUser $ebayUser): void
    {
        try {
            $request = new GetCategoryFeaturesRequestType();
            $request->DetailLevel = [DetailLevelCodeType::C_RETURN_ALL];
            $request->OutputSelector = ['UPCEnabled,EANEnabled,ISBNEnabled'];
            $request->CategoryID = (string)$category->category_id;
            $service = new TradingService( Helper::eBayCredentials($ebayUser));
            $response = $service->getCategoryFeatures($request)->toArray();
            if (!array_key_exists('Category', $response)) {
                return;
            }
            $feature = $response['Category'][0];
            $data = [
                'UPC_enabled' => $feature['UPCEnabled'] ?? null,
                'EAN_enabled' => $feature['EANEnabled'] ?? null,
                'ISBN_enabled' => $feature['ISBNEnabled'] ?? null,
            ];
            $category->update($data);
        } catch (Exception $e) {
            Log::channel('daily')->error("Error occurred while fetching conditions");
        }
    }

    /**
     * @throws EbayAccessTokeOrRefreshTokenException
     * @throws Exception
     */
    public function fetchAndStoreEbayCategoryItemConditionsMetadata(EbaySiteMap $ebaySiteMap, EbayUser $ebayUser): void
    {
        $response = $this->getItemConditionPoliciesMetadata($ebaySiteMap, $ebayUser);

        if (!property_exists($response, 'itemConditionPolicies')) {
            throw new Exception("Error occurred while fetching the item conditions metadata");
        }

        $existingCategories = EbayCategories::where('marketplace_id', $ebaySiteMap->marketplace)
            ->pluck('id', 'category_id');

        foreach ($response->itemConditionPolicies as $itemConditionPolicy) {
            $ebayCategoryId = $itemConditionPolicy->categoryId;
            if (!$existingCategories->has($ebayCategoryId)) {
                continue;
            }

            $categoryId = $existingCategories->get($ebayCategoryId);
            $this->handleItemConditionsMetaDataSave(
                $categoryId,
                $ebayCategoryId,
                $ebaySiteMap->marketplace,
                $itemConditionPolicy
            );
        }
    }

    private function getItemConditionPoliciesMetadata(EbaySiteMap $ebaySiteMap, $ebayUser)
    {
        $service = new RestfulService(
            Helper::getEbayAccessToken($ebayUser),
            [
                'Accept-Language' => $ebaySiteMap->language
            ]
        );
        $uri = sprintf(
            "https://api.ebay.com/sell/metadata/v1/marketplace/%s/get_item_condition_policies",
            $ebaySiteMap->marketplace
        );

        return $service->performAPICall($uri);
    }

    private function handleItemConditionsMetaDataSave(
        int $categoryId,
        int $ebayCategoryId,
        string $marketplaceId,
        $itemConditionPolicy
    ): void {
        $data = [
            'category_id' => $categoryId,
            'ebay_category_id' => $ebayCategoryId,
            'marketplace_id' => $marketplaceId,
            'item_conditions' => json_encode(
                $itemConditionPolicy->itemConditions,
                JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
            ),
            'item_condition_required' => $itemConditionPolicy->itemConditionRequired
        ];

        EbayCategoryItemConditionsMetadata::updateOrCreate(
            [
                'category_id' => $categoryId,
                'ebay_category_id' => $ebayCategoryId,
            ],
            $data
        );
    }
}
