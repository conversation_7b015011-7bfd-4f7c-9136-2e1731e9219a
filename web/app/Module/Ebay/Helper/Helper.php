<?php

namespace App\Module\Ebay\Helper;

// For Ebay OAuth
use App\Exceptions\EbayAccessTokeOrRefreshTokenException;
use App\Http\Helper\CountryCallingCodes;
use App\Models\Ebay\EbayUser;
use App\Models\Session;
use App\Models\ShopifyProduct;
use App\Models\ShopifyProductVariant;
use App\Module\Ebay\Enums\Order\EbayOrderCancelReasonEnum;
use App\Module\Ebay\Repositories\OAuth\EbayOAuthRepository;
use App\Module\Ebay\Services\OAuth\EbayOAuthService;
use App\Module\Ebay\Services\User\UserSiteIdService;
use App\Module\Shopify\Enums\Order\ShopifyOrderCancelReasonEnum;
use DateTime;
use DateTimeZone;
use DTS\eBaySDK\Trading\Types\ErrorType;
use DTS\eBaySDK\Trading\Types\GetItemRequestType;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class Helper
{
    /**
     * @throws EbayAccessTokeOrRefreshTokenException
     */
    public static function getEbayAccessToken($ebayUser): string
    {
        $ebayOAuthRepo = new EbayOAuthRepository();
        $ebayOAuthService = new EbayOAuthService($ebayOAuthRepo);
        return $ebayOAuthService->getAccessToken($ebayUser);
    }

    /**
     * @throws Exception if Refresh Token is expired or requesting Access Token fails
     * Returns access token if successful
     */
    public static function getEbayAccessTokenBySessionId($session_id): string
    {
        $ebayOAuthRepo = new EbayOAuthRepository();
        $ebayOAuthService = new EbayOAuthService($ebayOAuthRepo);
        return $ebayOAuthService->getAccessTokenBySessionId($session_id);
    }

    /**
     * @throws EbayAccessTokeOrRefreshTokenException
     */
    public static function eBayCredentials($ebayUser): array
    {
        $siteId = new UserSiteIdService();
        $config = array(
            'credentials' => [
                'appId' => config('ebay.client_id'),
                'certId' => config('ebay.client_secret'),
                'devId' => config('ebay.dev_id'),
            ],
            'ruName' => config('ebay.ru_value'),
            'sandbox' => config('ebay.sandbox'),
            'authorization' => self::getEbayAccessToken($ebayUser),
            'siteId' => $siteId->getSiteId($ebayUser->__get('site')),
        );
        if ($requestLanguage = $siteId->getRequestLanguage($ebayUser->__get('site'))) {
            $config['requestLanguage'] = $requestLanguage;
        }
        return $config;
    }


    public static function eBayCredentialsWithoutUser($access_token, $site): array
    {
        $siteId = new UserSiteIdService();
        return array(
            'credentials' => [
                'appId' => config('ebay.client_id'),
                'certId' => config('ebay.client_secret'),
                'devId' => config('ebay.dev_id'),
            ],
            'ruName' => config('ebay.ru_value'),
            'sandbox' => config('ebay.sandbox'),
            'authorization' => $access_token,
            'siteId' => $siteId->getSiteId($site)
        );
    }

    /**
     * @param $ebayUser
     * @param bool $siteId
     * @param bool $compressResponse
     * @return array
     * @throws Exception if Refresh Token is expired or requesting Access Token fails
     */
    public static function eBayCredentialsForRest($ebayUser, $siteId = true, $compressResponse = false): array
    {
        $args = array(
            'sandbox' => config('ebay.sandbox'),
            'authorization' => self::getEbayAccessToken($ebayUser),
            'apiVersion' => 'v1',
        );
        if ($siteId) {
            $args['siteId'] = (new UserSiteIdService())->getSiteId($ebayUser->site);
        }
        if ($compressResponse) {
            $args['compressResponse'] = true;
        }
        return $args;
    }

    public static function getItemRequestType($item_id)
    {
        $request = new GetItemRequestType();
        $request->ItemID = (string)$item_id;
        return $request;
    }

    /**
     * @throws Exception
     */
    public static function getEndTime($start_time): DateTime
    {
        $new_end_time = date('Y-m-d', strtotime($start_time->format('Y-m-d') . ' +119 days'));
        $date = date("Y-m-d H:i:s");
        $time = strtotime($date);
        //https://developer.ebay.com/support/kb-article?KBid=222
        $time = $time - (2 * 60);
        $date = date("Y-m-d H:i:s", $time);
        if ($date < $new_end_time) {
            $new_end_time = $date;
        }
        return new DateTime($new_end_time);
    }

    /**
     * @throws Exception
     */
    public static function add119days($start_time): DateTime
    {
        $new_end_time = date('Y-m-d', strtotime($start_time->format('Y-m-d') . ' +119 days'));
        return new DateTime($new_end_time);
    }


    /**
     * @throws Exception
     */
    public static function getOrderEndTime($start_time): DateTime
    {
        $new_end_time = date('Y-m-d H:i:s', strtotime($start_time->format('Y-m-d') . ' +90 days'));
        if ($new_end_time > date('Y-m-d H:i:s')) {
            $new_end_time = date('Y-m-d H:i:s');
        }
        return new DateTime($new_end_time);
    }

    public static function convertDatetimeToISO8601Format(DateTime $date)
    {
        $date->setTimezone(new DateTimeZone('UTC'));
        return $date->format('Y-m-d\TH:i:s.v\Z');
    }

    public static function getShopifySessionId(Request $request)
    {
        $session = $request->get('shopifySession');
        return $session->getId();
    }

    /**
     * @param float $price
     * @param EbayUser $ebayUser
     * @param Session $session
     * @param bool $toEbay is true if upload to eBay or false if upload to Shopify
     * @return float
     */
    public static function checkCurrencyDifferenceAndGetPrice(
        float $price,
        EbayUser $ebayUser,
        Session $session,
        bool $toEbay = false
    ): float {
        $shopify_currency = $session->__get('currency');
        $ebay_currency = $ebayUser->__get('currency');

        if (is_null($shopify_currency)) {
            return $price;
        }

        $same_currency = strcmp($shopify_currency, $ebay_currency) === 0;
        $conversion_rate = $ebayUser->userSetting->currency_conversion_rate;

        if (empty($conversion_rate) || $same_currency) {
            return $price;
        }

        if ($toEbay) {
            $price *= $conversion_rate;
        } else {
            $price /= $conversion_rate;
        }

        return $price;
    }

    public static function generateCacheKeyAndAddCacheData(array $cacheData, int $ttl = 86400): string
    {
        $cacheKey = 'cache_' . Str::uuid()->toString();

        if (!Cache::has($cacheKey)) {
            Cache::put($cacheKey, $cacheData, $ttl);
        }

        return $cacheKey;
    }

    public static function calculateCacheProgress(array $cacheData, $processedJobs = 0)
    {
        if (empty($cacheData)) {
            return null;
        }
        $lastProgress = $cacheData['progress'] ?? 0;
        if ($lastProgress >= 100) {
            return null;
        }
        $totalJobs = $cacheData['total_jobs'] ?? 1;
        $processedJobs = ($cacheData['processed_jobs'] ?? 1) + $processedJobs;
        $progress = ($totalJobs > 0) ? round(($processedJobs / $totalJobs) * 100, 2) : 100;

        if ($lastProgress > $progress) {
            $progress = $lastProgress;
        }
        return [
            'total_jobs' => $totalJobs,
            'processed_jobs' => $processedJobs,
            'progress' => $progress
        ];
    }

    public static function updateAndRetrieveLatestProgress($cacheKey, int $productCount, $progress_override = null)
    {
        $cacheData = Cache::get($cacheKey, []);
        if (!is_null($progress_override)) {
            $cacheData = [
                'total_jobs' => $cacheData['total_jobs'] ?? 1,
                'processed_jobs' => $cacheData['processed_jobs'] ?? 1,
                'progress' => $progress_override
            ];
        } else {
            $cacheData = Helper::calculateCacheProgress($cacheData, $productCount);
        }
        if (!is_null($cacheData)) {
            Cache::put($cacheKey, $cacheData, 86400);
        }
        return $cacheData;
    }


    public static function generateUniqueEbayVariantSkuIfEmpty(
        bool $isAutoSkuGenerationEnabled,
        string $itemId,
        string $sku,
        $optionValues
    ): string {
        if (empty($sku) && $isAutoSkuGenerationEnabled) {
            $sku = 'EB-' . $itemId . '-' . implode('-', $optionValues);
        }
        return $sku;
    }

    public static function getOrderUri($ebayUser): string
    {
        $start_time = new \DateTime($ebayUser->last_order_synced_at ?? $ebayUser->created_at);
        $start_time = self::convertDatetimeToISO8601Format($start_time);
        $apiUrl = 'https://api.ebay.com/sell/fulfillment/v1/order';
        $params = array(
            'filter' => "lastmodifieddate:%5B$start_time..%5D",
            'fieldGroups' => 'TAX_BREAKDOWN'
        );

        $apiUrl .= '?' . http_build_query($params);
        return $apiUrl;
    }

    public static function updatePhoneNumberWithCountryCallingCode($countryCode, mixed $phone_number)
    {
        if (self::hasCountryCode($phone_number) || !$countryCode) {
            return $phone_number;
        }
        return '+' . CountryCallingCodes::getCountryCallingCode($countryCode) . $phone_number;
    }

    public static function hasCountryCode($phoneNumber)
    {
        // Define a regular expression pattern for a phone number with country code
        $pattern = '/^\+\d{1,4}\d{1,}$/';

        // Use preg_match to check if the phone number matches the pattern
        return preg_match($pattern, $phoneNumber) === 1;
    }

    public static function shouldEndListing(
        ShopifyProduct $shopifyProduct,
        EbayUser $ebayUser,
    ): bool {
        if (
            $ebayUser->userSetting?->is_out_of_stock_option_enabled === 0 &&
            !$shopifyProduct->hasActiveInventory()
        ) {
            return true;
        }
        return false;
    }

    public static function prepareRequestError(
        string $shortMessage,
        string $longMessage,
        string $errorCode = '1'
    ): ErrorType {
        $error = new ErrorType();
        $error->ErrorCode = $errorCode;
        $error->SeverityCode = 'Error';
        $error->ErrorClassification = 'RequestError';
        $error->ShortMessage = $shortMessage;
        $error->LongMessage = $longMessage;
        return $error;
    }

    public static function getEbayCancellationReason(string $cancelReason): string
    {
       return match ($cancelReason) {
            ShopifyOrderCancelReasonEnum::CUSTOMER->value => EbayOrderCancelReasonEnum::BUYER_ASKED_CANCEL->value,
            ShopifyOrderCancelReasonEnum::FRAUD->value => EbayOrderCancelReasonEnum::ADDRESS_ISSUES->value,
            ShopifyOrderCancelReasonEnum::DECLINED->value => EbayOrderCancelReasonEnum::ORDER_UNPAID->value,
            ShopifyOrderCancelReasonEnum::INVENTORY->value => EbayOrderCancelReasonEnum::OUT_OF_STOCK_OR_CANNOT_FULFILL->value,
            default => '',
        };
    }

    public static function isEpsTestMode(): bool
    {
        return config('ebay.eps_test_mode') === true;
    }
}
