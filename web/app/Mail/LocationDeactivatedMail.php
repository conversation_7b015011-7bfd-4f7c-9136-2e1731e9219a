<?php

declare(strict_types=1);

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class LocationDeactivatedMail extends Mailable
{
    use Queueable;
    use SerializesModels;

    /**
     * Create a new message instance.
     *
     * @param string $firstName
     * @param string $locationName
     * @param string $shopDomain
     * @return void
     */
    public function __construct(
        private readonly string $firstName,
        private readonly string $locationName,
        private readonly string $shopDomain,
    ) {
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        $appRedirectUri = "/settings";
        $shopifyApiKey = config('shopify.setup.shopify_api_key');
        $settingsUrl = "https://admin.shopify.com/store/" . str_replace('.myshopify.com', "", $this->shopDomain) . "/apps/".$shopifyApiKey.$appRedirectUri;
        
        return $this->view('mail.location_deactivated_mail_view')
            ->with([
                'firstName' => $this->firstName,
                'locationName' => $this->locationName,
                'settingsUrl' => $settingsUrl,
                'fallbackApplied' => true, // Always true since we only send this email for fallback cases
            ])
            ->from(config('ebay.smtp_mail_from_address'))
            ->subject("Location deactivated - Automatic fallback activated - eBay Integration");
    }
} 