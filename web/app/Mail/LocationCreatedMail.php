<?php

namespace App\Mail;

use App\Models\Session;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class LocationCreatedMail extends Mailable
{
    use Queueable;
    use SerializesModels;

    /**
     * Create a new message instance.
     *
     * @param string $firstName
     * @param string $locationName
     * @param string $shopDomain
     * @param bool $usesAllLocations
     * @return void
     */
    public function __construct(
        private readonly string $firstName,
        private readonly string $locationName,
        private readonly string $shopDomain,
        private readonly bool $usesAllLocations,
    ) {
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        $appRedirectUri = "/settings";
        $shopifyApiKey = config('shopify.setup.shopify_api_key');
        $settingsUrl = "https://admin.shopify.com/store/" . str_replace('.myshopify.com', "", $this->shopDomain) . "/apps/".$shopifyApiKey.$appRedirectUri;
        
        // Customize subject based on location selection mode
        $subject = $this->usesAllLocations 
            ? "New location created - Already syncing with eBay - eBay Integration" 
            : "New location created - Update inventory sync settings - eBay Integration";
        
        return $this->view('mail.location_created_mail_view')
            ->with([
                'firstName' => $this->firstName,
                'locationName' => $this->locationName,
                'settingsUrl' => $settingsUrl,
                'usesAllLocations' => $this->usesAllLocations,
            ])
            ->from(config('ebay.smtp_mail_from_address'))
            ->subject($subject);
    }
} 