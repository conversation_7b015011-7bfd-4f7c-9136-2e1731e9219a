<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Session;
use Illuminate\Support\Facades\Log;
use App\Jobs\UpdateShopStatsJob;
use Illuminate\Support\Facades\Bus;

class UpdateShopStatistics extends Command
{
    protected $signature = 'shop:update-stats {session_id?}';

    protected $description = 'Update shop statistics for shops which have subscribed the pricing plan';

    public function handle()
    {
        $query = Session::query()
            ->where('enable_sync', 1)
            ->whereNotNull('subscribed_package')
        ;
    
        if ($sessionId = $this->argument('session_id')) {
            $query->where('id', $sessionId);
        }

        $totalDispatched = 0;
        $chunkSize = 100; 

        $query->chunkById($chunkSize, function($sessions) use (&$totalDispatched) {
            $updateShopStatsJobs = [];
            foreach ($sessions as $session) {
                $updateShopStatsJobs[] = new UpdateShopStatsJob($session->id, $session->shop);
            }

            if (!empty($updateShopStatsJobs)) {
                Bus::chain($updateShopStatsJobs)
                    ->onQueue('shopStats')
                    ->dispatch();
                $totalDispatched += count($updateShopStatsJobs);
                Log::channel('daily')->info("UpdateShopStatistics: Dispatched chain of " . count($updateShopStatsJobs) . " jobs.");
                $this->info("UpdateShopStatistics: Dispatched chain of " . count($updateShopStatsJobs) . " jobs.");
            }
        });

        Log::channel('daily')->info("UpdateShopStatistics: Finished dispatching chunk of " . $chunkSize . " jobs. Total dispatched jobs: $totalDispatched");
        $this->info("UpdateShopStatistics: Finished dispatching chunk of " . $chunkSize . " jobs. Total dispatched jobs: $totalDispatched");
    }

   
}
