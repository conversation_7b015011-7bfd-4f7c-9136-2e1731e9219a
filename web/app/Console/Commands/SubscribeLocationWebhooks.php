<?php

namespace App\Console\Commands;

use App\Models\Session;
use App\Module\Shopify\Services\WebhookSubscriptionService;
use dpl\ShopifySync\Jobs\LocationFetchJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Psr\Http\Client\ClientExceptionInterface;
use Shopify\Exception\InvalidArgumentException;
use Shopify\Exception\UninitializedContextException;
use Shopify\Exception\WebhookRegistrationException;

class SubscribeLocationWebhooks extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'subscribe:location-webhooks';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Subscribe users to location webhooks (create, update, delete)';

    private WebhookSubscriptionService $webhookService;

    public function __construct(WebhookSubscriptionService $webhookService)
    {
        parent::__construct();
        $this->webhookService = $webhookService;
    }

    /**
     * Execute the console command.
     *
     * @return void
     * @throws ClientExceptionInterface
     * @throws InvalidArgumentException
     * @throws UninitializedContextException
     * @throws WebhookRegistrationException
     */
    public function handle(): void
    {
        $this->info('Started subscribing to location webhooks ...');

        Session::query()->select('id', 'shop', 'access_token')
            ->where('enable_sync', 1)
            ->whereNotNull('access_token')
            ->lazyByIdDesc(100, 'id')
            ->each(function ($shop) {
                try {
                    $this->webhookService->subscribeToLocationDelete($shop->shop, $shop->access_token);
                    $this->webhookService->subscribeToLocationUpdate($shop->shop, $shop->access_token);
                    $this->webhookService->subscribeToLocationCreate($shop->shop, $shop->access_token);
                    LocationFetchJob::dispatch($shop->shop, $shop->access_token)->onQueue('location_fetch');
                    
                    Log::channel('daily')->info('Successfully subscribed to location webhooks', [
                        'shopName' => $shop->shop
                    ]);
                    
                    $this->line("✓ Subscribed {$shop->shop} to location webhooks");
                    
                } catch (\Exception $e) {
                    Log::channel('daily')->error('Failed to subscribe to location webhooks', [
                        'shopName' => $shop->shop,
                        'error' => $e->getMessage()
                    ]);
                    
                    $this->error("✗ Failed to subscribe {$shop->shop}: {$e->getMessage()}");
                }
            });

        $this->info("Finished subscribing to location webhooks.");
    }
} 