<?php

declare(strict_types=1);

namespace App\Lib\Handlers;

use App\Module\Shopify\Jobs\SyncShopifyLocationJob;
use App\Jobs\Notifications\SendLocationCreatedEmailJob;
use Illuminate\Support\Facades\Log;
use Shopify\Webhooks\Handler;

class LocationsCreate implements Handler
{
    public function handle(string $topic, string $shop, array $body): void
    {
        try {
            Log::channel('daily')->info('Location create webhook received', [
                'shop' => $shop,
                'body' => $body
            ]);
            // Dispatch a job to process the location update asynchronously
            SyncShopifyLocationJob::dispatch($shop, $body, 'create')->onConnection('redis');
            // Dispatch job to send email notification about new location
            SendLocationCreatedEmailJob::dispatch($shop, $body)->onConnection('redis');
            
            Log::channel('daily')->info('Location create jobs dispatched successfully', [
                'shop' => $shop,
                'location_id' => $body['id'] ?? 'unknown'
            ]);
        } catch (\Exception $e) {
            Log::channel('daily')->error(
                "Exception on LocationCreateWebhook",
                [
                    'shop' => $shop,
                    'data' => $body,
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            );
        }
    }
} 