<?php

declare(strict_types=1);

namespace App\Lib\Handlers;

use App\Models\Session;
use App\Repositories\ShopifyLocationRepository;
use App\Module\Shopify\Jobs\SyncShopifyLocationJob;
use Illuminate\Support\Facades\Log;
use Shopify\Webhooks\Handler;

class LocationsUpdate implements Handler
{
    public function handle(string $topic, string $shop, array $body): void
    {
        try {
            Log::channel('daily')->info('Location update webhook received', [
                'shop' => $shop,
                'body' => $body
            ]);
             // Find shop by domain
             $shopModel = Session::where('shop', $shop)->first();
            
             if (!$shopModel) {
                 Log::channel('daily')->error("Shop not found for location update job", [
                     'shop' => $shop
                 ]);
                 return;
             }
             $locationRepository = new ShopifyLocationRepository();
             $location = $locationRepository->getLocation($body['id'], $shopModel);

            if (!$location) {
                Log::channel('daily')->warning("Skipping location update - location does not exist", [
                    'shop' => $shop,
                    'location_id' => $body['id'],
                    'event_type' => 'update'
                ]);
                return;
            }
            // Dispatch a job to process the location update asynchronously
            SyncShopifyLocationJob::dispatch($shop, $body, 'update')->onConnection('redis');
            Log::channel('daily')->info('Location update job dispatched successfully', [
                'shop' => $shop,
                'location_id' => $body['id'] ?? 'unknown'
            ]);
            
        } catch (\Exception $e) {
            Log::channel('daily')->error(
                "Exception on LocationUpdateWebhook",
                [
                    'shop' => $shop,
                    'data' => $body,
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            );
        }
    }
} 