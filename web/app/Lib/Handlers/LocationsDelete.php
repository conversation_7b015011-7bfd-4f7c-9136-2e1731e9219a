<?php

declare(strict_types=1);

namespace App\Lib\Handlers;

use App\Module\Shopify\Jobs\DeleteShopifyLocationJob;
use Illuminate\Support\Facades\Log;
use Shopify\Webhooks\Handler;

class LocationsDelete implements Handler
{
    public function handle(string $topic, string $shop, array $body): void
    {
        try {
            $locationId = $body['id'] ?? null;
            
            Log::channel('daily')->info('Location delete webhook received', [
                'shop' => $shop,
                'locationId' => $locationId
            ]);
            
            if ($locationId) {
                // Dispatch job to handle location deletion asynchronously
                DeleteShopifyLocationJob::dispatch($shop, $locationId)
                    ->onQueue('default');
                
                Log::channel('daily')->info('DeleteShopifyLocationJob dispatched', [
                    'shop' => $shop,
                    'locationId' => $locationId
                ]);
            } else {
                Log::channel('daily')->warning('LocationsDelete webhook received without location ID', [
                    'shop' => $shop,
                    'body' => $body
                ]);
            }
        } catch (\Exception $e) {
            Log::channel('daily')->error(
                "Exception on LocationDeleteWebhook",
                [
                    'shop' => $shop,
                    'data' => $body,
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]
            );
        }
    }
} 