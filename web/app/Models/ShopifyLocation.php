<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShopifyLocation extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'shopify_location_id',
        'name',
        'address1',
        'address2',
        'city',
        'zip',
        'province',
        'country',
        'country_code',
        'phone',
        'active',
        'fulfills_online_orders',
        'ships_inventory',
    ];

    protected $casts = [
        'active' => 'boolean',
        'fulfills_online_orders' => 'boolean',
        'ships_inventory' => 'boolean',
    ];

    /**
     * Get the session that owns the location.
     */
    public function session(): BelongsTo
    {
        return $this->belongsTo(Session::class, 'session_id', 'id');
    }
} 