<?php

namespace App\Models\Ebay;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EbayUserSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'inventory_sync',
        'order_sync',
        'shopify_order_prefix',
        'ebay_user_id',
        'currency_conversion_rate',
        'truncate_title',
        'remove_disallowed_character',
        'map_description_to_title',
        'remove_duplicate_sku',
        'is_out_of_stock_option_enabled',
        'end_linked_ebay_item_when_deleted',
        'obey_variation_state_change',
        'sync_ebay_order_id',
        'sync_ebay_collected_tax',
        'shopify_order_tags',
        'vat_percentage',
        'custom_tax_title',
        'import_shipping_profile_name_as_tag',
        'shared_sku_inventory_sync',
        'link_based_on_sku_only',
        'override_untracked_or_continue_selling_qty',
        'sync_ebay_order_email',
        'extract_description_from_template',
        'order_fail_notification',
        'selected_locations',
        'sync_metafields',
        'sync_shipping_as_billing_address'
    ];

    protected $casts = [
        'selected_locations' => 'array',
        'sync_metafields' => 'boolean'
    ];

    /**
     * Get the eBay user that owns this setting
     */
    public function ebayUser(): BelongsTo
    {
        return $this->belongsTo(EbayUser::class, 'ebay_user_id');
    }

    /**
     * Check if metafield synchronization is enabled
     */
    public function shouldSyncMetafields(): bool
    {
        return $this->sync_metafields === true;
    }

    public function hasVatPercentage() : bool
    {
        return $this->vat_percentage > 0;
    }
}
