<?php

namespace App\Models;

use App\Models\Ebay\EbayProduct;
use App\Models\Shopify\ShopifyCollection;
use App\Models\Ebay\Profile;
use App\Module\Shopify\Enums\ShopifyProductUploadStatusEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class ShopifyProduct extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'shopify_session_id',
        'shopify_product_id',
        'shopify_product_url',
        'ebay_product_id',
        'descriptionHtml',
        'handle',
        'title',
        'product_type',
        'status',
        'tags',
        'vendor',
        'image_src',
        'isVariable',
        'ebay_product_url',
        'upload_status',
        'profile_id',
        'price_override',
        'title_override',
        'description_override',
        'quantity_override',
        'weight_override',
        'weight_unit_override',
        'duplicate_sku_override',
        'is_ebay_listing_ended_due_to_inventory',
        'is_variation_state_changed',
        'variation_state_override',
        'was_deleted_from_ebay',
        'is_active',
        'meta_info'
    ];

    public const ALLOWED_SHOPIFY_VARIATION_COUNT = 100;
    public const ALLOWED_OPTION_VALUES_COUNT = 3;

    protected $casts = [
        'upload_status' => ShopifyProductUploadStatusEnum::class,
        'meta_info' => 'array'
    ];




    public function variations()
    {
        return $this->hasMany(ShopifyProductVariant::class);
    }

    public function images()
    {
        return $this->hasMany(ShopifyProductImage::class);
    }

    public function options()
    {
        return $this->hasMany(ShopifyProductOption::class);
    }

    public function errors()
    {
        return $this->hasMany(ShopifyProductError::class)->where('severity', 'Error');
    }

    public function metafields()
    {
        return $this->hasMany(ShopifyProductMetaField::class, 'shopify_product_id', 'shopify_product_id');
    }

    public function collections()
    {
        return $this->belongsToMany(
            ShopifyCollection::class,
            'shopify_products_collections',
            'shopify_product_id',
            'shopify_collection_id',
            'shopify_product_id',
            'id'
        );
    }

    public function profile(): BelongsTo
    {
        return $this->belongsTo(Profile::class);
    }

    public function getFirstVariant(): ShopifyProductVariant
    {
        return $this->variations->first();
    }

    public function ebayProduct(): HasOne
    {
        return $this->hasOne(
            EbayProduct::class,
            'item_id',
            'ebay_product_id'
        );
    }

    public function getTitleAttribute($value)
    {
        $pattern = '/[\x00-\x1F\x7F\x{2700}-\x{27BF}\x{1F600}-\x{1F64F}\x{1F300}-\x{1F5FF}\x{1F680}-\x{1F6FF}\x{2600}-\x{26FF}\x{1F700}-\x{1F77F}]/u';
        $title = preg_replace($pattern, '', $value);
        return trim(preg_replace('/\s+/', ' ', $title));
    }

    public function isNotIncludedInProfile(): bool
    {
        return !$this->profile_id && isset($this->ebay_product_id);
    }
    public function shouldBeRelistedToEbay(): bool
    {
        if (!$this->ebay_product_id || !$this->is_ebay_listing_ended_due_to_inventory) {
            return false;
        }

        // Check if any variant has inventory available
        return $this->variations()->get()->some(function ($variant) {
            // First check selected_locations_inventory
            if ($variant->selected_locations_inventory !== null) {
                return $variant->selected_locations_inventory > 0;
            }
            
            // Fall back to inventory_quantity if selected_locations_inventory is null
            return $variant->inventory_quantity > 0;
        });
    }
    public function hasActiveInventory(): bool
    {
        // Priority Check: If any variation is not tracked OR has continue selling enabled
        if ($this->variations->some(function ($variant) {
            return $variant->isNotTracked() || $variant->continueSellingWhenOutOfStock();
        })) {
            return true;
        }

        // Selected Locations Check: If any variation has selected_locations_inventory not null and > 0
        if ($this->variations->some(function ($variant) {
            return $variant->selected_locations_inventory !== null && $variant->selected_locations_inventory > 0;
        })) {
            return true;
        }

        // Fallback Quantity Check: If ALL selected_locations_inventory are null and any inventory_quantity > 0
        $allSelectedLocationsNull = $this->variations->every(function ($variant) {
            return $variant->selected_locations_inventory === null;
        });

        if ($allSelectedLocationsNull && $this->variations->some(function ($variant) {
            return $variant->inventory_quantity > 0;
        })) {
            return true;
        }

        return false;
    }

    public function isEligibleToUploadOrUpdate(): bool
    {
        return $this->profile_id || $this->ebay_product_id;
    }

    public function session()
    {
        return $this->belongsTo(Session::class);
    }

    public function hasInvalidSpecificValueForPictureError(): bool
    {
        return $this->errors->whereIn('error_code', [21916639, 21916638])->count() > 0;
    }

    /**
     * Many-to-many relationship with locations through lookup table.
     */
    public function locations(): BelongsToMany
    {
        return $this->belongsToMany(
            ShopifyLocation::class,
            'shopify_product_locations', // Pivot table name
            'shopify_product_id',        // Foreign key in pivot table for this model
            'location_id',               // Foreign key in pivot table for related model
            'id',                        // Local key on this model
            'shopify_location_id'       // Local key on related model
        )->withTimestamps();
    }
}
