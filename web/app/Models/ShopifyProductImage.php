<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class ShopifyProductImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'shopify_product_id',
        'session_id',
        'altText',
        'image_id',
        'position',
        'url'
    ];


    public function epsImageMapping(): HasOne
    {
        return $this->hasOne(ShopifyEbayImageMapping::class, 'shopify_image_id', 'image_id');
    }
}
