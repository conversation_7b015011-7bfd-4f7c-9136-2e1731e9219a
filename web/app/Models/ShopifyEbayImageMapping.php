<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use App\Module\Ebay\Enums\EpsUploadStatusEnum;

class ShopifyEbayImageMapping extends Model
{
    protected $table = 'shopify_ebay_image_mappings';

    protected $fillable = [
         'shopify_image_id',
         'shopify_image_url',
         'ebay_eps_url',
         'eps_url_expires_at',
         'eps_upload_status',
         'eps_upload_error_message',
     ];

    protected $casts = [
        'eps_url_expires_at' => 'datetime',
        'eps_upload_status' => EpsUploadStatusEnum::class,
    ];

    /**
     * Scope for valid (non-expired) EPS URLs
     */
    public function scopeValidEpsUrls(Builder $query): Builder
    {
        return $query->whereNotNull('ebay_eps_url')
                    ->where('eps_url_expires_at', '>', now());
    }

    /**
     * Scope for expired EPS URLs
     */
    public function scopeExpiredEpsUrls(Builder $query): Builder
    {
        return $query->whereNotNull('ebay_eps_url')
                    ->whereNotNull('eps_url_expires_at')
                    ->where('eps_url_expires_at', '<=', now());
    }

    /**
     * Scope for mappings without EPS URLs
     */
    public function scopeWithoutEpsUrl(Builder $query): Builder
    {
        return $query->whereNull('ebay_eps_url');
    }

    /**
     * Check if EPS URL is expired
     */
    public function isEpsUrlExpired(): bool
    {
        return !is_null($this->eps_url_expires_at) && $this->eps_url_expires_at->isPast();
    }

    /**
     * Check if EPS URL is valid
     */
    public function hasValidEpsUrl(): bool
    {
        return !empty($this->ebay_eps_url) && !$this->isEpsUrlExpired();
    }

    /**
     * Get days until EPS URL expires
     */
    public function getDaysUntilExpiry(): ?int
    {
        if (!$this->eps_url_expires_at) {
            return null;
        }

        return now()->diffInDays($this->eps_url_expires_at, false);
    }
} 