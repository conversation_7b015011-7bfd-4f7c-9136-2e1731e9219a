<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShopifyInventoryLevel extends Model
{
    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'shopify_variant_id',
        'session_id',
        'shopify_inventory_level_id',
        'shopify_location_id',
        'available_quantity',
        'committed_quantity',
        'on_hand_quantity',
        'shopify_updated_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'shopify_inventory_level_id' => 'integer',
        'shopify_location_id' => 'integer',
        'available_quantity' => 'integer',
        'committed_quantity' => 'integer',
        'on_hand_quantity' => 'integer',
        'shopify_updated_at' => 'datetime',
    ];

    /**
     * Get the variant that owns this inventory level.
     */
    public function variant(): BelongsTo
    {
        return $this->belongsTo(ShopifyProductVariant::class, 'shopify_variant_id');
    }


    public function location(): BelongsTo
    {
        return $this->belongsTo(ShopifyLocation::class, 'shopify_location_id', 'shopify_location_id');
    }

    /**
     * Get the session that owns this inventory level.
     */
    public function session(): BelongsTo
    {
        return $this->belongsTo(Session::class, 'session_id');
    }

    /**
     * Get total quantity available for sale
     * This is typically available_quantity as committed_quantity is already subtracted
     */
    public function getAvailableForSaleAttribute(): int
    {
        return $this->available_quantity;
    }
} 