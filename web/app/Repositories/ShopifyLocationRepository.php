<?php

namespace App\Repositories;

use App\Models\Session;
use App\Models\ShopifyLocation;
use Illuminate\Support\Facades\Log;

class ShopifyLocationRepository
{
    public function getLocation(int $locationId, Session $shop): ?ShopifyLocation
    {
        return ShopifyLocation::where('shopify_location_id', $locationId)->where('session_id', $shop->id)->first();
    }
    /**
     * Process and store a Shopify location
     *
     * @param array $locationData The location data (from API or webhook)
     * @param mixed $shop The shop model
     * @param bool $isWebhook Whether the data is coming from a webhook
     * @param string $eventType The event type (create, update, or sync)
     * @return ShopifyLocation|null
     */
    public function processLocation(array $locationData, $shop, bool $isWebhook = false, string $eventType = 'sync'): ?ShopifyLocation
    {
        // Extract location ID, handling different formats from API vs webhook
        if ($isWebhook) {
            $locationId = (int) $locationData['id'];
            $address = $locationData;
            $countryCodeKey = 'country_code';
            $activeKey = 'active';
        } else {
            $locationId = (int) str_replace('gid://shopify/Location/', '', $locationData['id']);
            $address = $locationData['address'];
            $countryCodeKey = 'countryCode';
            $activeKey = 'isActive';
        }
        
        // Log the processing
        Log::channel('daily')->info('Processing Shopify location', [
            'shop' => $shop->shop ?? $shop->id ?? 'unknown',
            'location_id' => $locationId,
            'source' => $isWebhook ? 'webhook' : 'api'
        ]);
        
        // Update or create the location
        return ShopifyLocation::updateOrCreate(
            [
                'shopify_location_id' => $locationId,
                'session_id' => $shop->id
            ],
            [
                'name' => $locationData['name'],
                'address1' => $address['address1'] ?? null,
                'address2' => $address['address2'] ?? null,
                'city' => $address['city'] ?? null,
                'zip' => $address['zip'] ?? null,
                'province' => $address['province'] ?? null,
                'country' => $address['country'] ?? null,
                'country_code' => $address[$countryCodeKey] ?? null,
                'phone' => $address['phone'] ?? null,
                'active' => $locationData[$activeKey] ?? true,
                'fulfills_online_orders' => $locationData['fulfillsOnlineOrders'] ?? false,
                'ships_inventory' => $locationData['shipsInventory'] ?? false,
            ]
        );
    }
} 