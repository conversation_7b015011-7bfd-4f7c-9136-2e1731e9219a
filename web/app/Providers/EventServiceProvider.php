<?php

namespace App\Providers;

use App\Events\Order\AllOrderLineItemsAreLinked;
use App\Events\Product\EbayProductUpdated;
use App\Listeners\Ebay\Product\EPS\QueueEpsUpload;
use App\Listeners\Order\AdjustOrderLineItemInventory;
use App\Listeners\HandleClosedStore;
use App\Listeners\ProductBulkRequestComplete;
use dpl\ShopifySync\Events\InactiveShopDetectedEvent;
use dpl\ShopifySync\Events\ProductSyncingCompleteEvent;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use App\Events\Product\EPS\EpsUploadCompleted;
use App\Listeners\Ebay\Product\EPS\HandleEpsUploadCompleted;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        AllOrderLineItemsAreLinked::class => [
            AdjustOrderLineItemInventory::class
        ],
        ProductSyncingCompleteEvent::class => [
            ProductBulkRequestComplete::class
        ],
        InactiveShopDetectedEvent::class => [
            HandleClosedStore::class
        ],
        EbayProductUpdated::class => [
            QueueEpsUpload::class,
        ],
        EpsUploadCompleted::class => [
            HandleEpsUploadCompleted::class
        ]
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
