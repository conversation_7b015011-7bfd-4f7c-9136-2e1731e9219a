<?php

namespace App\Providers;

use App\Lib\DbSessionStorage;
use App\Lib\Handlers\AppUninstalled;
use App\Lib\Handlers\CollectionsDelete;
use App\Lib\Handlers\Gdpr\CustomersDataRequest;
use App\Lib\Handlers\Gdpr\CustomersRedact;
use App\Lib\Handlers\Gdpr\ShopRedact;
use App\Lib\Handlers\LocationsDelete;
use App\Lib\Handlers\LocationsUpdate;
use App\Lib\Handlers\LocationsCreate;
use App\Lib\Handlers\OrderCancelled;
use App\Lib\Handlers\OrderDelete;
use App\Lib\Handlers\OrderUpdated;
use App\Lib\Handlers\ProductsDelete;
use App\Lib\Handlers\ProductsUpdate;
use App\Lib\Handlers\ShopUpdate;
use App\Module\Shopify\Services\Collection\HandlesShopifyCollectionService;
use App\Module\Shopify\Services\Location\HandlesShopifyLocationService;
use App\Module\Shopify\Services\Product\HandlesShopifyProductService;
use App\Observers\ShopBulkQueryOperationObserver;
use dpl\ShopifySync\Models\ShopBulkQueryOperation;
use dpl\ShopifySync\ShopifySyncRegistry;
use dpl\ShopifySync\Constants\ShopifySyncTopic;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\App;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\URL;
use Shopify\Context;
use Shopify\ApiVersion;
use Shopify\Webhooks\Registry;
use Shopify\Webhooks\Topics;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
       
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     * @throws \Shopify\Exception\MissingArgumentException
     */
    public function boot()
    {
        $host = str_replace('https://', '', env('HOST', 'not_defined'));
        $this->app['request']->server->set('HTTPS', 'on');

        Context::initialize(
            env('SHOPIFY_API_KEY', 'not_defined'),
            env('SHOPIFY_API_SECRET', 'not_defined'),
            env('SCOPES', 'not_defined'),
            $host,
            new DbSessionStorage(),
            ApiVersion::OCTOBER_2024,
        );

        URL::forceRootUrl("https://$host");
        URL::forceScheme('https');

        Registry::addHandler(Topics::APP_UNINSTALLED, new AppUninstalled());
        Registry::addHandler(Topics::PRODUCTS_DELETE, new ProductsDelete());
        Registry::addHandler(Topics::PRODUCTS_UPDATE, new ProductsUpdate());
        Registry::addHandler(Topics::COLLECTIONS_DELETE, new CollectionsDelete());
        Registry::addHandler(Topics::ORDERS_FULFILLED, new OrderUpdated());
        Registry::addHandler(Topics::ORDERS_UPDATED, new OrderUpdated());
        Registry::addHandler(Topics::ORDERS_CANCELLED, new OrderCancelled());
        Registry::addHandler(Topics::ORDERS_DELETE, new OrderDelete());
        Registry::addHandler(Topics::SHOP_UPDATE, new ShopUpdate());
        Registry::addHandler(Topics::LOCATIONS_DELETE, new LocationsDelete());
        Registry::addHandler(Topics::LOCATIONS_UPDATE, new LocationsUpdate());
        Registry::addHandler(Topics::LOCATIONS_CREATE, new LocationsCreate());
        
        ShopifySyncRegistry::register(
            ShopifySyncTopic::PRODUCT_SYNC,
            App::make(HandlesShopifyProductService::class)
        );
        ShopifySyncRegistry::register(
            ShopifySyncTopic::COLLECTION_SYNC,
            App::make(HandlesShopifyCollectionService::class)
        );
         ShopifySyncRegistry::register(
            ShopifySyncTopic::LOCATION_SYNC,
            App::make(HandlesShopifyLocationService::class)
        );

        /*
         * This sets up the mandatory GDPR webhooks. You'll need to fill in the endpoint to be used by your app in the
         * "GDPR mandatory webhooks" section in the "App setup" tab, and customize the code when you store customer data
         * in the handlers being registered below.
         *
         * More details can be found on shopify.dev:
         * https://shopify.dev/apps/webhooks/configuration/mandatory-webhooks
         *
         * Note that you'll only receive these webhooks if your app has the relevant scopes as detailed in the docs.
         */
        Registry::addHandler('CUSTOMERS_DATA_REQUEST', new CustomersDataRequest());
        Registry::addHandler('CUSTOMERS_REDACT', new CustomersRedact());
        Registry::addHandler('SHOP_REDACT', new ShopRedact());

        Collection::macro('paginate', function ($perPage, $page = null, $total = null, $pageName = 'page') {
            $page = $page ?: LengthAwarePaginator::resolveCurrentPage($pageName);

            return new LengthAwarePaginator(
                $this->forPage($page, $perPage),
                $total ?: $this->count(),
                $perPage,
                $page,
                [
                    'path' => LengthAwarePaginator::resolveCurrentPath(),
                    'pageName' => $pageName,
                ]
            );
        });
        ShopBulkQueryOperation::observe(ShopBulkQueryOperationObserver::class);
    }
}
