<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Jobs\Product\SyncEbayProductInventory;
use App\Models\Ebay\EbayUser;
use App\Models\Ebay\EbayVariation;
use App\Models\ShopifyProductVariant;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class SetForceSyncInventoryJob implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected EbayUser $ebayUser,
        protected int $page = 1,
        protected int $delayBy = 0
    ) {
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Throwable
     */
    public function handle(): void
    {
        // Early validation: Check if any variants have null selected_locations_inventory
        $variantsWithNullSelectedInventory = ShopifyProductVariant::where([
            'session_id' => $this->ebayUser->session_id,
            'is_tracked' => 1,
        ])
            ->whereNotNull('sku')
            ->whereNull('selected_locations_inventory')
            ->exists();

        if ($variantsWithNullSelectedInventory) {
            Log::channel('daily')
                ->warning("SetForceSyncInventoryJob: Some variants have null selected_locations_inventory. Full sync required.", [
                    'ebay_user_id' => $this->ebayUser->ebay_user_name,
                    'session_id' => $this->ebayUser->session_id,
                    'message' => 'User needs to perform full Shopify product sync before force inventory sync'
                ]);
            return;
        }

        $paginatedUniqueShopifySkus = ShopifyProductVariant::select(
            'sku',
            DB::raw('MAX(selected_locations_inventory) as inventory_quantity')
        )
            ->where([
                'session_id' => $this->ebayUser->session_id,
                'is_tracked' => 1,
            ])
            ->whereNotNull('sku')
            ->groupBy('sku')
            ->havingRaw('COUNT(*) = 1')
            ->orderBy('sku')
            ->paginate(100, ['*'], 'page', $this->page);

        $skuAndQuantityArray = $paginatedUniqueShopifySkus->pluck('inventory_quantity', 'sku')->toArray();
        $skusOnly = array_map('strval', array_keys($skuAndQuantityArray));
        
        if (!$skuAndQuantityArray) {
            Log::channel('daily')
                ->alert("SetForceSyncInventoryJob No unique shopify SKU's found for the given session id.", [
                    'ebay_user_id' => $this->ebayUser->ebay_user_name,
                    'session_id' => $this->ebayUser->session_id,
                ]);
            return;
        }
       
        $ebayVariations = EbayVariation::select(
            'id',
            'quantity',
            'product_id',
            'quantity_sold',
            'sku'
        )
            ->with('ebayProduct:id,item_id,shopify_session_id')
            ->where('ebay_user_id', $this->ebayUser->id)
            ->whereRelation('ebayProduct', 'listing_status', 'Active')
            ->whereIn('sku', $skusOnly)
            ->orderBy('sku')
            ->get();

        if ($ebayVariations->isEmpty()) {
            Log::channel('daily')
                ->alert("SetForceSyncInventoryJob No eBay variations found for the given session id.", [
                    'ebay_user_id' => $this->ebayUser->ebay_user_name,
                    'session_id' => $this->ebayUser->session_id,
                ]);
            return;
        }

        if ($this->page !== 1) {
            $this->delayBy = 50 * $this->page;
        }
        Log::channel('daily')
            ->info("SetForceSyncInventoryJob Skus only:", [
                'ebay_user_id' => $this->ebayUser->ebay_user_name,
                'session_id' => $this->ebayUser->session_id,
                'ebay_variations' => $ebayVariations,
            ]);
        
        foreach ($ebayVariations as $variation) {
            $ebaySku = $variation->sku;
            $matchingSkuIndex = '';

            if (isset($skuAndQuantityArray[$ebaySku])) {
                $matchingSkuIndex = $ebaySku;
            } elseif (isset($skuAndQuantityArray[strtolower($ebaySku)])) {
                $matchingSkuIndex = strtolower($ebaySku);
            }

            //fail-safe to return
            if (!$matchingSkuIndex) {
                Log::channel('daily')
                    ->info("SetForceSyncInventoryJob No matching sku found for the given session id.", [
                        'ebay_user_id' => $this->ebayUser->ebay_user_name,
                        'ebay_sku' => $ebaySku,
                    ]);
                continue;
            }

            $ebayActualQuantity = $variation->quantity - $variation->quantity_sold;
            $shopifyVariantQuantity = ($skuAndQuantityArray[$matchingSkuIndex] <= 0)
                ? 0 : $skuAndQuantityArray[$matchingSkuIndex];

            if ($shopifyVariantQuantity === $ebayActualQuantity) {
                Log::channel('daily')
                    ->info("SetForceSyncInventoryJob No need to sync inventory:", [
                        'ebay_user_id' => $this->ebayUser->ebay_user_name,
                        'ebay_sku' => $ebaySku,
                        'ebay_actual_quantity' => $ebayActualQuantity,
                        'shopify_variant_quantity' => $shopifyVariantQuantity,
                    ]);
                continue;
            }
            Log::channel('daily')
                ->info("SetForceSyncInventoryJob Force sync inventory:", [
                    'ebay_user_id' => $this->ebayUser->ebay_user_name,
                    'ebay_sku' => $ebaySku,
                    'ebay_actual_quantity' => $ebayActualQuantity,
                    'shopify_variant_quantity' => $shopifyVariantQuantity,
                ]);
            /* @var ShopifyProductVariant $shopifyVariant */
            $shopifyVariant = $paginatedUniqueShopifySkus->where('sku',$variation->sku)->first();
            SyncEbayProductInventory::dispatch(
                $this->ebayUser->id,
                $variation->id,
                [
                    'inventory_quantity' => $shopifyVariantQuantity,
                    'sku' => $variation->sku,
                    'is_not_tracked' => $shopifyVariant->isNotTracked(),
                    'continue_selling' => $shopifyVariant->continueSellingWhenOutOfStock()
                ]
            )->delay($this->delayBy)->onQueue('inventory');
        }

        if ($paginatedUniqueShopifySkus->currentPage() !== $paginatedUniqueShopifySkus->lastPage()) {
            SetForceSyncInventoryJob::dispatch(
                $this->ebayUser,
                $this->page + 1
            )->onQueue('inventory');
        }
    }
}
