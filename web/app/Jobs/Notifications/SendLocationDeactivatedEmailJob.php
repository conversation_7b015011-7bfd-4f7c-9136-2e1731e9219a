<?php

declare(strict_types=1);

namespace App\Jobs\Notifications;

use App\Mail\LocationDeactivatedMail;
use App\Models\Ebay\EbayUser;
use App\Models\Session;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendLocationDeactivatedEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected string $shop;
    protected array $locationData;
    protected int $ebayUserId;

    /**
     * Create a new job instance.
     *
     * @param string $shop The shop domain
     * @param array $locationData The location data from webhook
     * @param int $ebayUserId The eBay user ID
     * @return void
     */
    public function __construct(string $shop, array $locationData, int $ebayUserId)
    {
        $this->shop = $shop;
        $this->locationData = $locationData;
        $this->ebayUserId = $ebayUserId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            $session = Session::where('shop', $this->shop)->first();
            
            if (!$session || !$session->user_email) {
                Log::channel('daily')->error('Cannot send location deactivated email - session not found or missing email', [
                    'shop' => $this->shop
                ]);
                return;
            }
            
            // Get the EbayUser
            $ebayUser = EbayUser::find($this->ebayUserId);
            if (!$ebayUser) {
                Log::channel('daily')->error('Cannot send location deactivated email - ebay user not found', [
                    'shop' => $this->shop,
                    'ebay_user_id' => $this->ebayUserId
                ]);
                return;
            }

            $ebayUserSetting = $ebayUser->userSetting;
            if (!$ebayUserSetting) {
                Log::channel('daily')->error('Cannot send location deactivated email - ebay user settings not found', [
                    'shop' => $this->shop,
                    'ebay_user_id' => $this->ebayUserId
                ]);
                return;
            }

            $mail = new LocationDeactivatedMail(
                firstName: $session->user_first_name ?? 'Merchant',
                locationName: $this->locationData['name'] ?? 'Unknown Location',
                shopDomain: $this->shop
            );
            
            Mail::to($session->user_email)->send($mail);
            
            Log::channel('daily')->info('Location deactivated email sent successfully', [
                'shop' => $this->shop,
                'location_id' => $this->locationData['id'] ?? 'unknown',
                'to_email' => $session->user_email,
                'ebay_user_id' => $this->ebayUserId
            ]);
        } catch (\Exception $e) {
            Log::channel('daily')->error('Failed to send location deactivated email', [
                'shop' => $this->shop,
                'ebay_user_id' => $this->ebayUserId,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }
} 