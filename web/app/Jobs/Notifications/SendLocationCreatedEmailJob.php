<?php

namespace App\Jobs\Notifications;

use App\Mail\LocationCreatedMail;
use App\Models\Session;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendLocationCreatedEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected string $shop;
    protected array $locationData;

    /**
     * Create a new job instance.
     *
     * @param string $shop The shop domain
     * @param array $locationData The location data from webhook
     * @return void
     */
    public function __construct(string $shop, array $locationData)
    {
        $this->shop = $shop;
        $this->locationData = $locationData;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            $session = Session::where('shop', $this->shop)->first();
            
            if (!$session || !$session->user_email) {
                Log::channel('daily')->error('Cannot send location created email - session not found or missing email', [
                    'shop' => $this->shop
                ]);
                return;
            }
            
            // Get the EbayUser and its settings
            $ebayUser = $session->ebayUser;
            if (!$ebayUser) {
                Log::channel('daily')->error('Cannot send location created email - ebay user not found', [
                    'shop' => $this->shop
                ]);
                return;
            }

            $ebayUserSetting = $ebayUser->userSetting;
            if (!$ebayUserSetting) {
                Log::channel('daily')->error('Cannot send location created email - ebay user settings not found', [
                    'shop' => $this->shop
                ]);
                return;
            }

            // Get the new location ID
            $newLocationId = (string)($this->locationData['id'] ?? '');
            
            // Location will be selected if:
            // 1. selected_locations is null (all locations are selected) OR
            // 2. selected_locations array contains this specific location ID
            $usesAllLocations = $ebayUserSetting->selected_locations === null || 
                            (is_array($ebayUserSetting->selected_locations) && 
                             in_array($newLocationId, $ebayUserSetting->selected_locations));
            
            $mail = new LocationCreatedMail(
                firstName: $session->user_first_name ?? 'Merchant',
                locationName: $this->locationData['name'] ?? 'New Location',
                shopDomain: $this->shop,
                usesAllLocations: $usesAllLocations
            );
            
            Mail::to($session->user_email)->send($mail);
            
            Log::channel('daily')->info('Location created email sent successfully', [
                'shop' => $this->shop,
                'location_id' => $this->locationData['id'] ?? 'unknown',
                'to_email' => $session->user_email,
                'uses_all_locations' => $usesAllLocations
            ]);
        } catch (\Exception $e) {
            Log::channel('daily')->error('Failed to send location created email', [
                'shop' => $this->shop,
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        }
    }
} 