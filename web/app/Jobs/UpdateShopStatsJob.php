<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\ShopStatistics;
use App\Models\ShopifyProduct;
use App\Models\Ebay\Order as EbayOrder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateShopStatsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        public int $sessionId,
        public string $shopName
    ) {
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::channel('daily')->info("Updating shop statistics for shop",[
            'shop_name' => $this->shopName,
            'session_id' => $this->sessionId,
        ]);

        $stats = $this->calculateStats($this->sessionId);

        ShopStatistics::query()->updateOrCreate(
            ['session_id' => $this->sessionId],
            array_merge(['shop_name' => $this->shopName], $stats)
        );

        Log::channel('daily')->info("Updated shop statistics for shop",[
            'shop_name' => $this->shopName,
            'session_id' => $this->sessionId,
            'stats' => $stats
        ]);
    }

     /**
     * Calculate shop statistics for a given session ID.
     *
     * @param int $sessionId
     * @return array
     */
    private function calculateStats(int $sessionId): array
    {
        // Shopify products stats
        $shopifyStats = DB::table('shopify_products')
            ->selectRaw('
                SUM(CASE WHEN ebay_product_id IS NOT NULL AND profile_id IS NOT NULL THEN 1 ELSE 0 END) as products_uploaded,
                SUM(CASE WHEN ebay_product_id IS NOT NULL AND profile_id IS NULL THEN 1 ELSE 0 END) as products_linked
            ')
            ->where('session_id', $sessionId)
            ->first();

        // Orders stats
        $orderStats = DB::table('orders')
            ->selectRaw('
                SUM(CASE WHEN shopify_order_id IS NOT NULL THEN 1 ELSE 0 END) as orders_posted_to_shopify,
                COUNT(*) as orders_fetched_to_system
            ')
            ->where('session_id', $sessionId)
            ->first();

        return [
            'products_uploaded' => $shopifyStats->products_uploaded ?? 0,
            'products_linked' => $shopifyStats->products_linked ?? 0,
            'orders_posted_to_shopify' => $orderStats->orders_posted_to_shopify ?? 0,
            'orders_fetched_to_system' => $orderStats->orders_fetched_to_system ?? 0,
        ];
    }
}
