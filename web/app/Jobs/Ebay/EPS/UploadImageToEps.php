<?php

namespace App\Jobs\Ebay\EPS;

use App\Exceptions\EbayAccessTokeOrRefreshTokenException;
use App\Models\Session;
use App\Module\Ebay\Services\Product\EPS\EpsUploadService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;
use App\Models\ShopifyProductImage;
use App\Module\Ebay\Services\Product\EPS\ShopifyEbayImageMappingService;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class UploadImageToEps implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;


    /**
     * The number of seconds after which the job's unique lock will be released.
     *
     * @var int
     */
    public $uniqueFor = 180; 

    public function __construct(
        public readonly int $shopifySessionId,
        public readonly int $shopifyProductId,
        public readonly int $shopifyImageId
    ) {
    }

    /**
     * @throws EbayAccessTokeOrRefreshTokenException
     */
    public function handle(
        EpsUploadService $epsUploadService,
        ShopifyEbayImageMappingService $shopifyEbayImageMappingService
    ): void {
        try {
            $session = Session::find($this->shopifySessionId);

            if (!$session) {
                Log::channel('daily')->error('EPS upload : Shopify session not found', [
                    'shopify_session_id' => $this->shopifySessionId,
                    'shopify_product_id' => $this->shopifyProductId,
                    'shopify_image_id' => $this->shopifyImageId,
                ]);
                return;
            }

            $ebayUser = $session?->ebayUser;

            if (!$ebayUser) {
                Log::channel('daily')->error('EPS upload : ebay user not found', [
                    'shop' => $session->shop,
                    'shopify_product_id' => $this->shopifyProductId,
                    'shopify_image_id' => $this->shopifyImageId,
                ]);
                return;
            }

            $shopifyProductImage = ShopifyProductImage::where('session_id', $this->shopifySessionId)
                ->where('shopify_product_id', $this->shopifyProductId)
                ->where('image_id', $this->shopifyImageId)
                ->first();

            if (!$shopifyProductImage) {
                Log::channel('daily')->error('EPS upload : Shopify product image not found', [
                    'shop' => $session->shop,
                    'shopify_product_id' => $this->shopifyProductId,
                    'shopify_image_id' => $this->shopifyImageId,
                ]);
                return;
            }

            Log::channel('daily')->info('Starting EPS upload for shopify product image', [
                'shop' => $session->shop,
                'shopify_product_id' => $this->shopifyProductId,
                'shopify_image_id' => $this->shopifyImageId,
                'image_url' => $shopifyProductImage->url
            ]);


            $shopifyEbayImageMapping = $shopifyEbayImageMappingService->getValidMapping($shopifyProductImage->image_id);

            $epsUrl = $shopifyEbayImageMapping?->ebay_eps_url;

            if ($epsUrl) {
                Log::channel('daily')->info('eBay eps url already existed for shopify product image', [
                    'shop' => $session->shop,
                    'shopify_image_id' => $shopifyProductImage->image_id,
                    'shopify_product_id' => $shopifyProductImage->shopify_product_id,
                    'image_url' => $shopifyProductImage->url,
                    'eps_url' => $epsUrl
                ]);
                return;
            }

            $epsUploadService->handleEpsUpload($ebayUser, $shopifyProductImage);
        } catch (Exception $e) {
            Log::channel('daily')->error('EPS upload job failed', [
                'shopify_session_id' => $this->shopifySessionId,
                'shopify_image_id' => $this->shopifyImageId,
                'shopify_product_id' => $this->shopifyProductId,
                'error' => $e->getMessage(),
                'attempt' => $this->attempts()
            ]);
            throw $e;
        }
    }

    public function uniqueId(): string
    {
        return "eps_upload_" . $this->shopifyImageId;
    }
}
