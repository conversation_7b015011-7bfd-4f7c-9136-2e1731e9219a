<?php

namespace App\Events\Product\EPS;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class EpsUploadCompleted
{
    use Dispatchable, InteractsWithSockets, SerializesModels;


    public function __construct(
        public string $shopifySessionId,
        public int $shopifyProductId,
        public int $platformShopifyProductId,
    ) {
    }
} 