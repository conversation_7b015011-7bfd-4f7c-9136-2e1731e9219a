<?php

namespace App\Http\Controllers;

use App\Models\Ebay\EbayUser;
use App\Models\Ebay\EbayUserSetting;
use App\Models\Session;
use App\Services\LocationService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class EbaySettingController extends Controller
{
    use ApiResponseTrait;

    /**
     * Create a new controller instance.
     *
     * @param LocationService $locationService
     * @return void
     */
    public function __construct(protected LocationService $locationService)
    {
    }

    public function update(Request $request)
    {
        $orderAndInventoryData = $request->input('orderAndInventoryData');
        $orderAndInventorySelected = $orderAndInventoryData['orderAndInventorySync'];
        $setOrderIdPrefix = $orderAndInventoryData['orderIdPrefix'];
        $orderIdPrefixValue = $orderAndInventoryData['orderIdPrefixValue'] ?? null;

        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $sessionData = array();

        if ($request->has('language')) {
            $sessionData['language'] = $request->input('language');
        }

        if ($request->has('generalSettingsData.autoSkuGeneration')) {
            $sessionData['auto_sku_generation'] = $request->input('generalSettingsData.autoSkuGeneration');
        }
        if (!empty($sessionData)) {
            Session::where('session_id', $shopify_session_id)
                ->update($sessionData);
        }

        $ebayUser = EbayUser::where('shopify_session_id', $shopify_session_id)->first();
        $data = array(
            'currency_conversion_rate' => $request->input('currencyData.currency_conversion_rate') ?? 1,
            'sync_ebay_order_id' => $request->input('orderAndInventoryData.syncEbayOrderId'),
            'sync_ebay_collected_tax' => $request->input('orderAndInventoryData.syncEbayCollectedTax') ?? false,
            'shared_sku_inventory_sync' => $request->input('orderAndInventoryData.sharedSKUInventorySync') ?? false,
        );
        if ($request->has('importSettingData.importShippingProfileNameAsTag')) {
            $data['import_shipping_profile_name_as_tag'] = (bool) $request->input('importSettingData.importShippingProfileNameAsTag');
        }
        if ($request->has('importSettingData.linkBasedOnSKUOnly')) {
            $data['link_based_on_sku_only'] = (bool) $request->input('importSettingData.linkBasedOnSKUOnly');
        }

        if ($request->has('generalSettingsData.autoEndListing')) {
            $data['end_linked_ebay_item_when_deleted'] = $request->input('generalSettingsData.autoEndListing');
        }
        if ($request->has('orderAndInventoryData.shopifyOrderTags')) {
            $data['shopify_order_tags'] = $request->input('orderAndInventoryData.shopifyOrderTags');
        }
        $data['override_untracked_or_continue_selling_qty'] = $request->input(
            'orderAndInventoryData.overrideUntrackedOrContinueSellingQty'
        );

        $data['vat_percentage'] = $request->input('orderAndInventoryData.vatPercentage') ?? 0;
        $data['custom_tax_title'] = $request->input('orderAndInventoryData.customTaxTitle') ?? null;

        $data['shopify_order_prefix'] = $setOrderIdPrefix && $orderIdPrefixValue ? $orderIdPrefixValue : null;

        $data['sync_ebay_order_email'] = $request->input('orderAndInventoryData.syncEbayOrderEmail', false);
        $data['order_fail_notification'] = $request->input('orderAndInventoryData.orderFailNotification', false);
        $data['sync_shipping_as_billing_address'] = $request->input('orderAndInventoryData.syncShippingAsBillingAddress', false);
        
        $locationChanged = false;
        // Handle location data
        if ($request->has('locationData.selectedLocations')) {
            $selectedLocations = $request->input('locationData.selectedLocations');
            $currentLocations = $ebayUser->userSetting->selected_locations ?? null;
            
            // If both are null, no update needed
            if ($currentLocations === null && $selectedLocations === null) {
                $locationChanged = false;
            } 
            // If one is null and other isn't, update needed
            elseif ($currentLocations === null || $selectedLocations === null) {
                $data['selected_locations'] = $selectedLocations;
                $locationChanged = true;
            }
            // If both are arrays, compare them
            else {
                $sortedCurrent = $currentLocations;
                $sortedSelected = $selectedLocations;
                
                // Sort both arrays for comparison (without modifying originals)
                sort($sortedCurrent);
                sort($sortedSelected);
                
                if ($sortedCurrent !== $sortedSelected) {
                    $data['selected_locations'] = $selectedLocations;
                    $locationChanged = true;
                }
            }
        }

        $this->formatOrderAndInventorySync($orderAndInventorySelected, $data);

        Log::channel('daily')->info("user setting before update for ebayUserId : $ebayUser->id", [
            'values' => $ebayUser->userSetting
        ]);
        Log::channel('daily')
            ->info("user setting has changed for $ebayUser->shopify_session_id to " . json_encode($data));
        
        $ebayUser->userSetting->update($data);
        
        // Process location data changes if location data was provided
        if ($locationChanged) {
            $this->locationService->processLocationDataChanges($ebayUser);
        }
        
        return $this->successResponse($ebayUser->userSetting);
    }

    public function create(Request $request)
    {
        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $ebayUser = EbayUser::where('shopify_session_id', $shopify_session_id)->first();
        $orderAndInventorySelected = $request->input('ebaySettings.orderAndInventorySync');
        $data = [
            'ebay_user_id' => $ebayUser->id,
            'currency_conversion_rate' => $request->input('currencyConversion.ebay_currency_value') ?? 1,
            'shopify_order_tags' => 'Ebay,Ebay Integration & Importer, {ebay_order_id}'
        ];
        $this->formatOrderAndInventorySync($orderAndInventorySelected, $data);

        $settings = EbayUserSetting::updateOrCreate(['ebay_user_id' => $data['ebay_user_id']], $data);

        return $this->successResponse($settings);
    }

    private function formatOrderAndInventorySync(array $orderAndInventorySelected, array &$data): void
    {
        switch (end($orderAndInventorySelected)) {
            case 'order-and-inventory':
                $data['inventory_sync'] = true;
                $data['order_sync'] = true;
                break;
            case 'inventory-only':
                $data['inventory_sync'] = true;
                $data['order_sync'] = false;
                break;
            case 'no-order-and-inventory':
                $data['inventory_sync'] = false;
                $data['order_sync'] = false;
                break;
        }
    }

    public function currency(Request $request)
    {
        $from = $request->get('from');
        $to = $request->get('to');
        $amount = $request->get('amount');

        $base_url = 'https://api.api-ninjas.com/v1/convertcurrency';
        $append = http_build_query([
            'have' => $from,
            'want' => $to,
            'amount' => $amount
        ]);

        $response = Http::withHeaders([
            'X-Api-Key' => config('app.currency_api_key')
        ])
            ->get($base_url . '?' . $append);
        return $this->successResponse($response->json());
    }
    public function updateSharedSkuSyncSetting(Request $request)
    {
        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $ebayUser = EbayUser::where('shopify_session_id', $shopify_session_id)->first();
        EbayUserSetting::where('ebay_user_id', $ebayUser->id)
            ->update(['shared_sku_inventory_sync' => $request->shared_sku_inventory]);
        $ebayUser->update(['show_duplicate_sku_banner' => 0]);
        return $this->successResponse(['success' => true]);
    }
}
