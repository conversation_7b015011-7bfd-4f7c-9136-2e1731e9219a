<?php

namespace App\Http\Controllers\Shopify;

use App\Http\Controllers\Controller;
use App\Models\ShopifyLocation;
use App\Models\Ebay\EbayUserSetting;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ShopifyLocationController extends Controller
{
    use ApiResponseTrait;
    /**
     * Get all Shopify locations for the current shop.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLocations(Request $request)
    {
        try {
            $session = $request->get('shopifySession');
            
            // Get all locations for the current shop
            $locations = ShopifyLocation::where('session_id', $session->getSessionId())
                ->where('active', true)
                ->get();
            
            // If connected_only parameter is provided, filter by user's selected locations
            if ($request->has('connected_only') && $request->boolean('connected_only')) {
                $ebayUser = $session->getSessionModel()->ebayUser;
                $userSetting = EbayUserSetting::where('ebay_user_id', $ebayUser->id)->first();
                $selectedLocationIds = $userSetting?->selected_locations;
                
                // If selected_locations is null, return all locations (null means all selected)
                // If it's an array, filter by those location IDs
                if ($selectedLocationIds !== null && is_array($selectedLocationIds)) {
                    $locations = $locations->whereIn('shopify_location_id', $selectedLocationIds);
                }
            }
                
            return response()->json([
                'success' => true,
                'locations' => $locations->values() // Reset array keys
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching Shopify locations: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Error fetching locations: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getLocationFilterList(Request $request)
    {
         /** @var \App\Lib\Session $session */
         $session = $request->get('shopifySession');
         $sessionId = $session->getSessionId();
        $search = $request->search;
        $locations = ShopifyLocation::where('session_id', $sessionId)
            ->select('shopify_location_id as id', 'name');

        if ($search) {
            $locations->where(
                'name',
                'LIKE',
                '%' . $search . '%'
            );
        }

        return $this->successResponse($locations->paginate(10)->withQueryString());
    }
} 