<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Jobs\SetForceSyncInventoryJob;
use App\Models\Ebay\EbayUser;
use App\Models\ShopifyProductVariant;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class ForceSyncInventoryForEbayUserController extends Controller
{
    use ApiResponseTrait;

    public function __invoke(int $ebayUserId): Response
    {
        $ebayUser = EbayUser::where('id', $ebayUserId)->first();
        if (!$ebayUser) {
            return $this->errorResponse(404, 'No eBay user found');
        }

        // Check if any variants have null selected_locations_inventory
        $hasNullSelectedInventory = ShopifyProductVariant::where([
            'session_id' => $ebayUser->session_id,
            'is_tracked' => 1,
        ])
            ->whereNotNull('sku')
            ->whereNull('selected_locations_inventory')
            ->exists();

        if ($hasNullSelectedInventory) {
            Log::channel('daily')
                ->warning("ForceSyncInventoryForEbayUserController: Variants have null selected_locations_inventory.", [
                    'ebay_user_id' => $ebayUser->ebay_user_name,
                    'session_id' => $ebayUser->session_id,
                    'message' => 'User needs to perform full Shopify product sync before force inventory sync'
                ]);
            return $this->errorResponse(
                400,
                'Cannot force sync inventory. Some products have missing location inventory data. Please perform a full Shopify product sync first to populate location-based inventory data.',
                ['action_required' => 'full_shopify_sync']
            );
        }
        Log::channel('daily')
            ->info("ForceSyncInventoryForEbayUserController: Dispatched jobs to sync inventory for store " . $ebayUser->shopify_session_id, [
                'ebay_user_id' => $ebayUser->ebay_user_name,
                'session_id' => $ebayUser->session_id,
            ]);

        SetForceSyncInventoryJob::dispatch($ebayUser)->onQueue('inventory');

        return $this->successResponse(
            null,
            'Dispatched jobs to sync inventory for store ' . $ebayUser->shopify_session_id
        );
    }
}
