<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Ebay\EbayUser;
use App\Models\Ebay\EbayVariation;
use App\Models\ShopifyProductVariant;
use App\Module\Ebay\Services\Product\Inventory\EbayInventoryService;
use App\Traits\ApiResponseTrait;
use Illuminate\Database\Eloquent\Builder;

class InventoryController extends Controller
{
    use ApiResponseTrait;

    public function syncInventory($ebay_user_id, EbayInventoryService $ebayInventoryService)
    {
        $ebayUser = EbayUser::where('id', $ebay_user_id)->first();
        $sku = request()->sku;

        /** @var ShopifyProductVariant $variant */
        $variant = ShopifyProductVariant::where('session_id', $ebayUser->session_id)->where('sku', '=', $sku)->first();
        if (!$variant) {
            $this->successResponse('Shopify variant not found');
        }

        $variations = EbayVariation::where("ebay_user_id", $ebayUser->id)
            ->where('sku', '=', $sku)
            ->whereHas('ebayProduct', function (Builder $query) use ($ebayUser) {
                $query->where('ebay_user_id', $ebayUser->id)->where('listing_status', '=', 'Active');
            })->get();

        if (!count($variations)) {
            $this->successResponse('eBay variations not found');
        }
        $refreshedVariations = [];
        foreach ($variations as $variation) {
            $formattedVariantData = [
                'inventory_quantity' => $variant->getQuantity(),
                'sku' => $variation->sku,
                'is_not_tracked' => $variant->isNotTracked(),
                'continue_selling' => $variant->continueSellingWhenOutOfStock(),
            ];
            $ebayInventoryService->updateEbayProductInventory(
                $ebayUser,
                $variation,
                $formattedVariantData,
            );
            $refreshedVariations[] = $variation->refresh();
        }
        return $this->successResponse(['shopifyVariant' => $variant, 'ebayVariant' => $refreshedVariations]);
    }
}
