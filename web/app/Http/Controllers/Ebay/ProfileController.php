<?php

namespace App\Http\Controllers\Ebay;

use App\Exceptions\EbayAccessTokeOrRefreshTokenException;
use App\Http\Controllers\Controller;
use App\Http\Resources\ItemAspectsResource;
use App\Jobs\Ebay\EndEbayListingJob;
use App\Jobs\SetProductUploadPaginatedJob;
use App\Models\Ebay\EbayUser;
use App\Models\Ebay\Policy\EbayPaymentPolicy;
use App\Models\Ebay\Policy\EbayReturnPolicy;
use App\Models\Ebay\Policy\EbayShippingPolicy;
use App\Models\EbayCategoryItemConditionsMetadata;
use App\Models\ShopifyProduct;
use App\Models\ShopifyProductError;
use App\Module\Ebay\Helper\Helper;
use App\Module\Ebay\Repositories\Profile\ProfileRepository;
use App\Module\Ebay\Services\Category\EbayCategoriesService;
use App\Module\Ebay\Services\User\EbaySiteMappingService;
use App\Module\Shopify\Repositories\Product\ShopifyProductRepository;
use App\Module\Ebay\Services\Policies\EbayPaymentPolicy as EbayPaymentPolicyService;
use App\Module\Ebay\Services\Policies\EbayReturnPolicy as EbayReturnPolicyService;
use App\Module\Ebay\Services\Policies\EbayShippingPolicy as EbayShippingPolicyService;
use App\Models\Ebay\Profile;
use App\Models\EbayCategories;
use App\Module\Ebay\Services\Category\EbayCategoriesFeatureService;
use App\Module\Ebay\Services\Product\EbayProductService;
use App\Module\Ebay\Services\Profile\EbayProfileDataService;
use App\Module\Ebay\Services\User\UserMarketplaceIdService;
use App\Module\Shopify\Enums\ShopifyProductUploadStatusEnum;
use App\Traits\ActivityLoggerTrait;
use App\Traits\ApiResponseTrait;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Throwable;
use Illuminate\Support\Facades\Log;

class ProfileController extends Controller
{
    use ApiResponseTrait;
    use ActivityLoggerTrait;

    public function __construct(protected ProfileRepository $profileRepository)
    {
    }

    public function getAllPolicies(Request $request)
    {
        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $ebayUser = EbayUser::where('shopify_session_id', $shopify_session_id)->first();
        return $this->getPolicies($ebayUser);
    }

    public function getRefetchedPolicies(
        Request $request,
        EbayPaymentPolicyService $ebayPaymentPolicy,
        EbayShippingPolicyService $ebayShippingPolicy,
        EbayReturnPolicyService $ebayReturnPolicy
    ) {
        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $ebayUser = EbayUser::where('shopify_session_id', $shopify_session_id)->first();
        $ebayReturnPolicy->fetchReturnPolicies($ebayUser);
        $ebayPaymentPolicy->fetchPaymentPolicies($ebayUser);
        $ebayShippingPolicy->fetchShippingPolicies($ebayUser);
        return $this->getPolicies($ebayUser);
    }

    public function getCategories(Request $request)
    {
        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $ebayUser = EbayUser::where('shopify_session_id', $shopify_session_id)->first();
        $marketplace_id = UserMarketplaceIdService::getMarketplaceIdsWithSite($ebayUser->site);
        $categories = EbayCategories::select('id', 'category_id as value', 'category_name as label', 'condition_enabled')
            ->where('marketplace_id', $marketplace_id);
        if ($request->has('query')) {
            $categories->where('category_name', 'LIKE', '%' . $request->input('query') . '%');
        }
        $categories = $categories->simplePaginate();
        return $this->successResponse($categories);
    }

    public function getItemAspects(Request $request, $category_id): Response|Application|ResponseFactory
    {
        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $ebayUser = EbayUser::where('shopify_session_id', $shopify_session_id)->first();
        $marketplace_id = (new EbaySiteMappingService())->getSiteMap($ebayUser->site)->marketplace;
        $category = EbayCategories::where('category_id', $category_id)
            ->where('marketplace_id', $marketplace_id)
            ->first();
        $item_aspects = [];
        if ($category->item_aspect_fetched) {
            $item_aspects = $this->profileRepository->getItemAspects($category);
        }
        if (!count($item_aspects)) {
            try {
                (new EbayCategoriesService())->fetchAndStoreItemAspect($category, $ebayUser);
                $category->update(['item_aspect_fetched' => true]);
            } catch (\Exception $e) {
                Log::channel('daily')->error("Error while fetching item aspects.", [
                    'category' => $category->category_id,
                    'ebayUser' => $ebayUser->ebay_user_name,
                    'error' => $e->getMessage(),
                ]);
            }
            $item_aspects = $this->profileRepository->getItemAspects($category);
        }

        return $this->successResponse(ItemAspectsResource::collection($item_aspects));
    }

    public function fetchAndStoreItemAspect($category_id, $marketplace_id, $refetch = false)
    {
        $category = $this->getEbayCategory($category_id, $marketplace_id);
        if (!$category->item_aspect_fetched || $refetch) {
            (new EbayCategoriesService())->fetchAndStoreItemAspect($category);
            $category->update(['item_aspect_fetched' => true]);
        }
    }

    public function getCategoryConditions(
        $ebayCategoryId,
        Request $request,
        EbayCategoriesFeatureService $ebayCategoriesFeatureService
    ): Response|Application|ResponseFactory {
        $session = $request->get('shopifySession');
        $sessionId = $session->dbSession->id;
        $ebayUser = EbayUser::where('session_id', $sessionId)->first();
        $ebaySiteMap = (new EbaySiteMappingService())->getSiteMap($ebayUser->site);
        $marketplace_id = $ebaySiteMap->marketplace;
        $category = $this->getEbayCategory($ebayCategoryId, $marketplace_id);
        $ebayCategoriesFeatureService->fetchAndStoreEbayCategoryFeatures($category, $ebayUser);
        $conditions = $ebayCategoriesFeatureService->getConditions($ebayUser, $ebayCategoryId, $ebaySiteMap);
        return $this->successResponse($conditions);
    }

   

    public function getEbayCategory($ebayCategoryId, $marketplace_id)
    {
        return EbayCategories::where('category_id', $ebayCategoryId)->where('marketplace_id', $marketplace_id)
            ->first();
    }

    public function getProfiles(Request $request)
    {
        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $paginate = 20;
        if ($request->has('paginate')) {
            $paginate = $request->input('paginate');
        }
        $profiles = Profile::with('shippingPolicy')
            ->withCount([
                'products',
                'collections',
                'products as uploaded_products' => function (Builder $query) {
                    $query->where('upload_status', '=', 1);
                },
                'products as failed_products' => function (Builder $query) {
                    $query->where('upload_status', '=', 2);
                }
            ])
            ->where('shopify_session_id', $shopify_session_id)
            ->orderBy('profile_name');
            if ($request->has('search')) {
                $profiles = $profiles->where('profile_name', 'LIKE', '%' . $request->input('search') . '%');
            }
            $profiles = $profiles->paginate($paginate);
        return $this->successResponse($profiles);
    }

    public function getAllProfiles(Request $request)
    {
        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $profiles = Profile::with('shippingPolicy')
            ->where('shopify_session_id', $shopify_session_id)
            ->orderBy('profile_name');
            $profiles = $profiles->get();
        return $this->successResponse($profiles);
    }

    public function getSingleProfile(Request $request, $id)
    {
        $profile = Profile::withCount('products')
            ->withCount([
                'products as uploaded_products' => function (Builder $query) {
                    $query->where('upload_status', '=', 1);
                },
                'products as failed_products' => function (Builder $query) {
                    $query->where('upload_status', '=', 2);
                }
            ])
            ->with(
                'shippingPolicy',
                'collections:id,title,profile_id',
                'returnPolicy',
                'paymentPolicy',
                'condition'
            )
            ->find($id);
        return $this->successResponse($profile);
    }

    /**
     * @throws Throwable
     */
    public function updateProfile(Request $request, $id, EbayProfileDataService $ebayProfileDataService)
    {

        $session = $request->get('shopifySession');
        $sessionId = $session->dbSession->id;
        $shopify_session_id = $session->dbSession->session_id;

        $ebayUser = EbayUser::with('session', 'userSetting', 'returnPolicies', 'shippingPolicies', 'paymentPolicies')
            ->where('session_id', $sessionId)->first();

        try {
            Helper::getEbayAccessToken($ebayUser);
        } catch (EbayAccessTokeOrRefreshTokenException) {
            return $this->successResponse([
                'profile_id' => $id,
                'process_completed' => true,
                'refresh_token_expired' => true,
            ]);
        }

        $profile = Profile::with(
            'shippingPolicy:id,shipping_options'
        )->withCount('products')->find($id);

        $should_redirect = !!$profile->products_count;
        $requestData = $request->all();
        $filterData = $ebayProfileDataService->filterEbayProfileData($requestData, $shopify_session_id, $ebayUser->id);

        Log::channel('daily')->info('Profile Update Request', [
            'profile_id' => $profile->id,
            'profile_name' => $profile->profile_name,
            'filterData' => $filterData
        ]);
        $profile->update($filterData);
        $cacheKey = Helper::generateCacheKeyAndAddCacheData(['progress' => 0, 'processed' => 0]);
        SetProductUploadPaginatedJob::dispatch($profile, 1, $cacheKey);
        return $this->successResponse(['profile' => $profile, 'should_redirect' => $should_redirect]);
    }

    /**
     * @throws \Throwable
     */
    private function removeProducts(
        $products,
        $ebayUser,
        $reasonSelected,
        EbayProductService $ebayProductService
    ): void {
        $jobs = [];
        $cacheData = array(
            'total_jobs' => $products->count(),
            'processed_jobs' => 0
        );
        $activity = $this->logActivity([
            'title' => 'bulkProductEndOnEbay',
            'parameters' => $products->count(),
            "shopify_session_id" => $ebayUser->shopify_session_id
        ]);
        $cacheKey = Helper::generateCacheKeyAndAddCacheData($cacheData);
        $repo = new ShopifyProductRepository();
        foreach ($products as $product) {
            $jobs[] = new EndEbayListingJob(
                $ebayUser,
                $cacheKey,
                [
                    'ebay_product_id' => $product->ebay_product_id,
                    'cancellation_reason' => $reasonSelected,
                    'title' => $product->title,
                    'image' => $product->image_src,
                    'id' => $product->id
                ],
                $activity
            );
        }
        $ebayProductService->dispatchEndListingsJobs($jobs, $cacheKey, $ebayUser, $repo, $activity);
    }

    public function handleRemovalOfShopifyProductsFromProfile(
        string $shopify_session_id,
        array $productIdsToUpdate
    ): void {
        $productUpdates = [
            'profile_id' => null
        ];

        ShopifyProduct::where('shopify_session_id', $shopify_session_id)
            ->whereIn('id', $productIdsToUpdate)->update($productUpdates);
    }

    public function deleteProductErrors($shopify_session_id, $product_ids)
    {
        ShopifyProductError::where('shopify_session_id', $shopify_session_id)
            ->whereIn('shopify_product_id', $product_ids)
            ->delete();
    }

    public function removeProductFromProfile(
        Request $request,
        EbayProductService $ebayProductService,
    ): Response|Application|ResponseFactory {
        $shopify_session_id = Helper::getShopifySessionId($request);
        $ebayUser = EbayUser::where('shopify_session_id', $shopify_session_id)->first();
        $endListingsCheckbox = $request->input('endListingsCheckbox');
        $reasonSelected = $request->input('reasonSelected');
        $ids = $request->get('shopify_product_ids');
        $uploadedProducts = ShopifyProduct::where('shopify_session_id', $shopify_session_id)
            ->whereIn('id', $ids)
            ->whereNotNull('ebay_product_id')
            ->get();
        if ($uploadedProducts->count() && $endListingsCheckbox) {
            $this->removeProducts(
                $uploadedProducts,
                $ebayUser,
                $reasonSelected,
                $ebayProductService,
            );
        }
        if ($endListingsCheckbox) {
            $ids = array_diff($ids, $uploadedProducts->pluck('id')->toArray());
        }

        $this->handleRemovalOfShopifyProductsFromProfile($shopify_session_id, $ids);
        $this->deleteProductErrors($shopify_session_id, $ids);

        Log::channel('daily')->info('Product removed successfully from profile', [
            'message' => 'Product removed successfully from profile',
            'shopifySessionId' => $shopify_session_id,
            'shopifyProductIds' => $ids,
            'endListingsCheckbox' => $endListingsCheckbox,
            'reasonSelected' => $reasonSelected,
        ]);

        return $this->successResponse([
            'message' => 'Product removed successfully from profile',
            'end_listing_count' => $endListingsCheckbox ? $uploadedProducts->count()-count($ids) : 0,
        ]);
    }


    /**
     * @throws \Throwable
     */
    public function deleteProfile(Request $request, Profile $profile, EbayProductService $ebayProductService,): Response|Application|ResponseFactory
    {
        $shopify_session_id = Helper::getShopifySessionId($request);
        $ebayUser = EbayUser::where('shopify_session_id', $shopify_session_id)->first();
        $endListingsCheckbox = $request->input('endListingsCheckbox');
        $reasonSelected = $request->input('reasonSelected');
        $uploadedProducts = $profile->products()->whereNotNull('ebay_product_id');
        $uploadedProductsCount = $uploadedProducts->count();

        Log::channel('daily')->info('Profile Deletion Request', [
            'shopify_session_id' => $shopify_session_id,
            'profile_id' => $profile->id,
            'profile_name' => $profile->profile_name,
            'uploadedProductsCount' => $uploadedProductsCount,
            'reasonSelected' => $reasonSelected,
            'ebayUserName' => $ebayUser->ebay_user_name,
        ]);


        if ($uploadedProductsCount && $endListingsCheckbox) {
            $uploadedProducts
                ->chunk(2000, function ($products) use ($ebayUser, $reasonSelected, $ebayProductService) {
                    $this->removeProducts(
                        $products,
                        $ebayUser,
                        $reasonSelected,
                        $ebayProductService
                    );
                });
        }
        $failedProductsQuery = ShopifyProduct::select('id')
            ->where('session_id', $ebayUser->session_id)
            ->where('profile_id', $profile->id)
            ->whereNull('ebay_product_id');
        $failedProductsQuery->chunkById(2000, function ($products) use ($shopify_session_id) {
            $productIds = $products->pluck('id')->toArray();
            $this->handleRemovalOfShopifyProductsFromProfile($shopify_session_id, $productIds);
            $this->deleteProductErrors($shopify_session_id, $productIds);
        });
        $profile->collections()->update(['profile_id' => null]);
        $profile->delete();
        return $this->successResponse();
    }

    private function getProductUpdates(): array
    {
        return [
            'profile_id' => null,
            'upload_status' => ShopifyProductUploadStatusEnum::NOT_UPLOADED->value
        ];
    }

    /**
     * @param $ebayUser
     * @return Application|ResponseFactory|Response
     */
    public function getPolicies($ebayUser): ResponseFactory|Application|Response
    {
        $policies['return_policy'] = EbayReturnPolicy::select('id', 'name')
            ->where('ebay_user_id', $ebayUser->id)->where('is_active', 1)->get();
        $policies['shipping_policy'] = EbayShippingPolicy::select('id', 'name', 'shipping_options')
            ->where('ebay_user_id', $ebayUser->id)->where('is_active', 1)->get();
        $policies['payment_policy'] = EbayPaymentPolicy::select('id', 'name')
            ->where('ebay_user_id', $ebayUser->id)->where('is_active', 1)->get();
        return $this->successResponse($policies);
    }
}
