<?php

namespace App\Http\Controllers\Ebay;

use App\Exceptions\EbayAccessTokeOrRefreshTokenException;
use App\Http\Controllers\Controller;
use App\Module\Ebay\Services\Product\UploadErrorResolutionService;
use App\Models\Session;
use App\Traits\ApiResponseTrait;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class UploadErrorResolutionController extends Controller
{
    use ApiResponseTrait;

    public function __construct(protected UploadErrorResolutionService $uploadErrorResolutionService)
    {
    }

    public function resolveProductUpload(int $error_id, Request $request): Response
    {
        $session = $request->get('shopifySession');
        $data = $request->all();
        $shouldRedirect = false;
        $session = Session::with('ebayUser')->where('session_id', $session->getId())->first();
        try {
            $shouldRedirect = $this->uploadErrorResolutionService->resolveProductUpload($error_id, $data, $session);
        } catch (EbayAccessTokeOrRefreshTokenException $ebayAccessTokeOrRefreshTokenException) {
            return $this->errorResponse(
                403,
                $ebayAccessTokeOrRefreshTokenException->getMessage(),
                [
                    'refresh_token_expired' => true,
                ]
            );
        } catch (Exception $exception) {
            Log::channel('daily')->error('Exception occurred while resolving product upload', [
                'sessionId' => $session->id,
                'errorId' => $error_id,
                'error' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine()
            ]);
            
            return $this->errorResponse(500, 'An error occurred while resolving the upload error');
        }
        return $this->successResponse(['redirect' => $shouldRedirect]);
    }
}
