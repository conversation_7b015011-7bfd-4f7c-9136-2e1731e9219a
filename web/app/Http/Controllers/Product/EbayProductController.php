<?php

namespace App\Http\Controllers\Product;

use App\Exceptions\EbayAccessTokeOrRefreshTokenException;
use App\Http\Controllers\Controller;
use App\Jobs\Ebay\EndEbayListingJob;
use App\Models\Ebay\EbayProduct;
use App\Models\Ebay\EbayUser;
use App\Models\Ebay\EbayVariation;
use App\Models\EbayUserItemLocations;
use App\Models\Session;
use App\Module\Ebay\Helper\Helper;
use App\Module\Ebay\Services\Product\EbayProductDataService;
use App\Module\Ebay\Services\Product\EbayProductService;
use App\Module\Ebay\Services\Product\Inventory\EbayInventoryService;
use App\Module\Shopify\Enums\ShopifyBulkActionEnum;
use App\Module\Shopify\Repositories\Product\ShopifyProductRepository;
use App\Module\Shopify\Services\Product\ShopifyProductService;
use App\Traits\ActivityLoggerTrait;
use DateTime;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use App\Traits\ApiResponseTrait;
use Illuminate\Support\Facades\Log;
use Throwable;

class EbayProductController extends Controller
{
    use ApiResponseTrait;
    use ActivityLoggerTrait;

    /**
     * @throws Throwable
     */
    public function fetchProducts(Request $request, EbayProductService $ebayProductService)
    {
        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $ebayUser = EbayUser::where('shopify_session_id', $shopify_session_id)->first();
        try {
            Helper::getEbayAccessToken($ebayUser);
        } catch (EbayAccessTokeOrRefreshTokenException) {
            return $this->successResponse([
                'process_completed' => true,
                'refresh_token_expired' => true,
            ]);
        }
        $ebayProductService->dispatchFetchEbayProductJobs($ebayUser);
        Log::channel('daily')->info('Sync Products From Ebay Action ', [
            'ebayUserId' => $ebayUser->id
        ]);
        $data = array(
            'message' => 'Successfully dispatched jobs to fetch products'
        );
        return $this->successResponse($data);
    }

    public function getEbayProductCounts(Request $request)
    {
        $session = $request->get('shopifySession');
        $ebayUser  = EbayUser::where('shopify_session_id', $session->getId())->first();
        $remainingActiveEbayProductQuery = EbayProduct::where('ebay_user_id', $ebayUser->id)
            ->whereNull('shopify_product_id')
            ->where('listing_status', '=', 'Active');
        if($request->has('selected_item_location')) {
            $remainingActiveEbayProductQuery->whereIn('site', explode(',', $request->input('selected_item_location')));
        }
        $remainingActiveEbayProductCount = $remainingActiveEbayProductQuery->count();

        $response = [
            'remainingActiveEbayProductCount' => $remainingActiveEbayProductCount,
        ];

        return $this->successResponse($response);
    }


    public function getListings(Request $request): Response
    {
        $session = $request->get('shopifySession');
        $search = $request->__get('search');
        $filter = $request->__get('filter');
        $site = $request->input('selected_item_location');
        $paginate = 50;
        $session = Session::with('ebayUser:id,session_id')->where('session_id', $session->getId())->first();
        $ebay_user_id = $session->ebayUser->id;

        $simple_ebay_products_query = EbayProduct::where('ebay_user_id', $ebay_user_id);

        $ebay_products_query = EbayProduct::with([
            'variations' => function ($q) use ($ebay_user_id) {
                $q->select('ebay_user_id', 'product_id', 'option_values', 'price', 'sku', 'quantity', 'quantity_sold')
                    ->where('ebay_user_id', $ebay_user_id);
            }
        ])
            ->select('id', 'ebay_user_id', 'title', 'image_url', 'isVariable', 'item_id', 'listing_status', 'shopify_product_id', 'shopify_product_url', 'view_item_url', 'site', 'import_remarks')
            ->where('ebay_user_id', $ebay_user_id);

        $ebay_user_item_locations_query = EbayUserItemLocations::where('ebay_user_id', $ebay_user_id);

        if (!empty($site)) {
            $ebay_products_query->whereIn('site', explode(',', $site));
        }
        $ended_query = clone $simple_ebay_products_query;
        $ended_count = $ended_query->where('listing_status', '=', 'Ended')->count();
        $simple_ebay_products_query->where('listing_status', '=', 'Active');
        $exported_query = clone $simple_ebay_products_query;
        $not_exported_query = clone $simple_ebay_products_query;
        $exported_count = $exported_query->whereNotNull('shopify_product_id')->count();
        $not_exported_count = $not_exported_query->whereNull('shopify_product_id')->count();
        // apply conditions for search or filter
        $ebay_products_query = $this->applySearchFilters($ebay_products_query, $search, $ebay_user_id);
        $ebay_products_query = $this->applyExportedFilter($ebay_products_query, $filter);

        // apply sorting
        $sortBy = $request->has('sort_by') ? $request->input('sort_by') : 'default';
        match ($sortBy) {
            'default asc', 'default' => $ebay_products_query->orderBy('title', 'asc'),
            'default desc' => $ebay_products_query->orderBy('title', 'desc'),
            'end_time asc' => $ebay_products_query->orderBy('end_time', 'asc'),
            'end_time desc' => $ebay_products_query->orderBy('end_time', 'desc'),
            'start_time asc' => $ebay_products_query->orderBy('start_time', 'asc'),
            'start_time desc' => $ebay_products_query->orderBy('start_time', 'desc'),
            'updated asc' => $ebay_products_query->orderBy('updated_at', 'asc'),
            'updated desc' => $ebay_products_query->orderBy('updated_at', 'desc')
        };

        if ($request->has('paginate')) {
            $paginate = $request->input('paginate');
        }

        $ebay_products = $ebay_products_query->paginate($paginate)->withQueryString();

        $response = [
            'exported_count' => $exported_count,
            'not_exported_count' => $not_exported_count,
            'ended_count' => $ended_count,
            'products' => $ebay_products,
            'filter_by_locations' => $ebay_user_item_locations_query->get(),
        ];

        return $this->successResponse($response);
    }

    private function applySearchFilters($query, $search, $ebay_user_id)
    {
        if (!is_null($search)) {
            $query->where(function (Builder $query) use ($search, $ebay_user_id) {
                $query->where('title', 'LIKE', '%' . $search . '%')
                    ->orWhereHas('variations', function (Builder $q) use ($search, $ebay_user_id) {
                        $q->where('ebay_user_id', $ebay_user_id)
                            ->where('ebay_variations.sku', 'LIKE', '%' . $search . '%');
                    });
            });
        }
        return $query;
    }

    private function applyExportedFilter($query, $filter)
    {
        if ($filter == 'ended') {
            $query->where('listing_status', '=', 'Ended');
        } else {
            $query->where('listing_status', '=', 'Active');
        }
        if ($filter === 'imported') {
            return $query->whereNotNull('shopify_product_id');
        } elseif ($filter === 'not_imported') {
            return $query->whereNull('shopify_product_id');
        }
        return $query;
    }

    public function getLinkedListings(Request $request, EbayProductDataService $ebayProductDataService): Response
    {
        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $data = array();

        $products = EbayProduct::with('variations')->where([
            ['shopify_session_id', $shopify_session_id],
            ['listing_type', 'FixedPriceItem'],
            ['shopify_product_id', '<>', null]
        ]);

        $data['noShopifyProducts'] = $products->count() === 0;

        if ($searched_term = $request->__get('search')) {
            $products = $products->where(
                'title',
                'LIKE',
                '%' . $searched_term . '%'
            )->orWhere(
                'sku',
                'LIKE',
                '%' . $searched_term . '%'
            );
            $data['isSearched'] = true;
        }
        $data['products'] = $products->paginate(5)->withQueryString();
        return $this->successResponse($data);
    }

    public function unlinkListing(ShopifyProductService $productService, EbayProduct $ebayProduct)
    {
        $shopify_session_id = $ebayProduct->__get('shopify_session_id');
        $session = Session::where('session_id', $shopify_session_id)->first();

        $productService->deleteProduct($session, $ebayProduct);
        $ebayProduct->update(['shopify_product_id' => null, 'shopify_product_url' => null]);
        return $this->successResponse($ebayProduct, "Product deleted successfully.");
    }

    public function checkIfDataFetchingFromEbay(Request $request, EbayProductDataService $ebayProductDataService)
    {
        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $data = $ebayProductDataService->getIsProductFetchingDetails($shopify_session_id);

        return $this->successResponse($data);
    }

    public function getVariations(Request $request, $id)
    {
        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $variations = EbayVariation::where('shopify_session_id', $shopify_session_id)->where('product_id', $id)->get();
        return $this->successResponse($variations);
    }

    /**
     * @throws Throwable
     */
    public function ebayEndListing(
        Request $request,
        ShopifyProductRepository $repo,
        EbayProductService $ebayProductService
    ): Response|Application|ResponseFactory {
        $session = $request->get('shopifySession');
        $shopify_session_id = $session->getId();
        $ebayUser = EbayUser::where('shopify_session_id', $shopify_session_id)->first();
        $products = $request->input('products');
        $reasonSelected = $request->input('cancellation_reason');

        Log::channel('daily')->info('end listing on ebay', [
            'shopify_session_id' => $shopify_session_id,
            'productsCount' => count($products),
            'reasonSelected' => $reasonSelected,
            'ebayUserName' => $ebayUser->ebay_user_name,
           
        ]);

        $cacheData = array(
            'total_jobs' => count($products),
            'processed_jobs' => 0
        );
        $cacheKey = Helper::generateCacheKeyAndAddCacheData($cacheData);
        $activity = $this->logActivity([
            'title' => 'bulkProductEndOnEbay',
            'parameters' => count($products),
            "shopify_session_id" => $ebayUser->shopify_session_id
        ]);
        $jobs = [];
        foreach ($products as $product) {
            $jobs[] = new EndEbayListingJob(
                $ebayUser,
                $cacheKey,
                [
                    'ebay_product_id' => $product['ebay_product_id'],
                    'cancellation_reason' => $reasonSelected,
                    'title' => $product['title'],
                    'image' => $product['image_src'],
                    'id' => $product['id'],
                ],
                $activity
            );
        }
        $ebayProductService->dispatchEndListingsJobs($jobs, $cacheKey, $ebayUser, $repo, $activity);

        //updates the products are ending in database
        $repo->updateIsShopifyBulkAction($ebayUser, ShopifyBulkActionEnum::DELETING->value);

        return $this->successResponse([
            'message' => 'Job dispatched to end listings in eBay'
        ]);
    }
}
