<?php

namespace App\Http\Controllers\Product;

use App\Http\Controllers\Controller;
use App\Models\ShopifyInventoryLevel;
use App\Models\ShopifyLocation;
use App\Models\Ebay\EbayUserSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class VariantInventoryController extends Controller
{
    /**
     * Get inventory levels by location for a specific variant
     *
     * @param Request $request
     * @param int $variantId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInventoryByLocation(Request $request, int $variantId): \Illuminate\Http\JsonResponse
    {
        try {
            $session = $request->get('shopifySession');
            $sessionId = $session->getSessionId();
            
            // Get ebay user id from session model's ebay user relation
            $ebayUserId = $session->getSessionModel()->ebayUser->id;
            
            // Get user's selected locations from EbayUserSetting using ebay user id
            $userSetting = EbayUserSetting::where('ebay_user_id', $ebayUserId)->first();
            $selectedLocations = $userSetting ? ($userSetting->selected_locations ?? []) : [];
            
            // Get inventory levels joined with location data
            $inventoryData = ShopifyInventoryLevel::where('shopify_inventory_levels.shopify_variant_id', $variantId)
                ->where('shopify_inventory_levels.session_id', $sessionId)
                ->join('shopify_locations', function($join) use ($sessionId) {
                    $join->on('shopify_inventory_levels.shopify_location_id', '=', 'shopify_locations.shopify_location_id')
                         ->where('shopify_locations.session_id', '=', $sessionId);
                })
                ->select(
                    'shopify_locations.name as location',
                    'shopify_locations.shopify_location_id as location_id',
                    'shopify_inventory_levels.available_quantity as available'
                )
                ->get()
                ->map(function ($item) use ($selectedLocations) {
                    $item->is_selected = in_array($item->location_id, $selectedLocations);
                    return $item;
                });
                
            return response()->json([
                'success' => true,
                'inventory' => $inventoryData
            ]);
        } catch (\Exception $e) {
            Log::channel('daily')->error('Error fetching variant inventory by location: ' . $e->getMessage(), [
                'session_id' => $sessionId ?? null,
                'ebay_user_id' => $ebayUserId ?? null,
                'variant_id' => $variantId,
                'shop_domain' => $session->getShop() ?? null,
                'request_method' => $request->method(),
                'request_url' => $request->fullUrl(),
                'exception_message' => $e->getMessage(),
                'exception_file' => $e->getFile(),
                'exception_line' => $e->getLine(),
                'exception_code' => $e->getCode()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error fetching inventory data: ' . $e->getMessage()
            ], 500);
        }
    }
} 