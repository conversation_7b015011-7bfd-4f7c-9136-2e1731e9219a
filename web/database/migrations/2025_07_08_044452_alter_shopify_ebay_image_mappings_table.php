<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterShopifyEbayImageMappingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shopify_ebay_image_mappings', function (Blueprint $table) {
            // Drop unique constraint first
            $table->dropUnique(['shopify_image_id']);
            
            // Alter column type from string to bigInteger
            $table->bigInteger('shopify_image_id')->change();

            $table->integer('eps_upload_status')->default(0)->change()->comment('0: pending, 1: success, 2: failed, 3: validation_failed');
            
            // Re-add unique constraint
            $table->unique('shopify_image_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shopify_ebay_image_mappings', function (Blueprint $table) {
            // Drop unique constraint first
            $table->dropUnique(['shopify_image_id']);
            
            // Alter column type back to string
            $table->string('shopify_image_id')->change();

            $table->string('eps_upload_status')->default('pending')->change();
            
            // Re-add unique constraint
            $table->unique('shopify_image_id');
        });
    }
}
