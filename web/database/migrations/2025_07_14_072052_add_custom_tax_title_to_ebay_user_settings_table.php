<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddCustomTaxTitleToEbayUserSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ebay_user_settings', function (Blueprint $table) {
            $table->string('custom_tax_title')->after('vat_percentage')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ebay_user_settings', function (Blueprint $table) {
            $table->dropColumn('custom_tax_title');
        });
    }
}
