<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddSyncShippingAsBillingAddressToEbayUserSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('ebay_user_settings', function (Blueprint $table) {
            $table->boolean('sync_shipping_as_billing_address')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('ebay_user_settings', function (Blueprint $table) {
            $table->dropColumn('sync_shipping_as_billing_address');
        });
    }
}
