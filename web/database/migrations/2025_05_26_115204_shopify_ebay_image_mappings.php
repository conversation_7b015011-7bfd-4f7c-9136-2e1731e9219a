<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ShopifyEbayImageMappings extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shopify_ebay_image_mappings', function (Blueprint $table) {
            $table->id();
            $table->string('shopify_image_id');
            $table->text('shopify_image_url');
            $table->text('ebay_eps_url')->nullable();
            $table->timestamp('eps_url_expires_at')->nullable();
          
            $table->string('eps_upload_status')->default('pending');//pending, success, failed,validation_failed
            $table->text('eps_upload_error_message')->nullable();
    
           
            $table->timestamps();

            $table->index('eps_url_expires_at');
            // Add unique constraint on shopify_image_id since ebay_user_id is removed
            $table->unique('shopify_image_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shopify_ebay_image_mappings');
    }
}
