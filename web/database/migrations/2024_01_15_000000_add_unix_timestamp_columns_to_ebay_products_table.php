<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ebay_products', function (Blueprint $table) {
            $table->unsignedInteger('start_time_unix')->nullable()->after('start_time');
            $table->unsignedInteger('end_time_unix')->nullable()->after('end_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ebay_products', function (Blueprint $table) {
            $table->dropColumn(['start_time_unix', 'end_time_unix']);
        });
    }
}; 