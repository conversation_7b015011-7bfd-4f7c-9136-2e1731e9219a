<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShopifyProductLocationLookupTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shopify_product_locations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('session_id');
            $table->unsignedBigInteger('shopify_product_id'); // shopify_product_id is the id from the shopify_products table
            $table->unsignedBigInteger('location_id'); // location_id is the shopify_location_id from the shopify_locations table
            
            $table->unique(['session_id', 'shopify_product_id', 'location_id'], 'spl_product_location_unique');
            $table->timestamps();


            $table->foreign('session_id', 'spl_session_fk')
                  ->references('id')
                  ->on('sessions')
                  ->onDelete('cascade');

            $table->foreign('shopify_product_id', 'spl_product_fk')
                  ->references('id')
                  ->on('shopify_products')
                  ->onDelete('cascade');
                
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shopify_product_locations');
    }
}
