<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShopStatsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shop_statistics', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('session_id')->unique();
            $table->string('shop_name');
            $table->integer('products_uploaded')->nullable();
            $table->integer('products_linked')->nullable();
            $table->integer('orders_posted_to_shopify')->nullable();
            $table->integer('orders_fetched_to_system')->nullable();
            $table->timestamps();

            $table->foreign('session_id')->references('id')->on('sessions')->onDelete('cascade');
            $table->index('session_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shop_statistics');
    }
}
