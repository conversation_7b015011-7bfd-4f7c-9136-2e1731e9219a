<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Performance indexes for shopify_product_variants table
     * Optimizes SKU filtering operations at billion scale
     */
    public function up(): void
    {
        Schema::table('shopify_product_variants', function (Blueprint $table) {
            // Critical composite index for session-based SKU filtering
            // Supports: WHERE session_id = ? AND sku = ?
            $table->index(['session_id', 'sku'], 'idx_shopify_variants_session_sku');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shopify_product_variants', function (Blueprint $table) {
            $table->dropIndex('idx_shopify_variants_session_sku');
        });
    }
}; 