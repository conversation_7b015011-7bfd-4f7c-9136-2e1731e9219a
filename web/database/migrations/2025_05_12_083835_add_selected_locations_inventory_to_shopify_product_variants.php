<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('shopify_product_variants', function (Blueprint $table) {
            $table->integer('selected_locations_inventory')->nullable()->after('inventory_quantity')
                ->comment('Inventory quantity from only selected locations in ebay_user_settings');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('shopify_product_variants', function (Blueprint $table) {
            $table->dropColumn('selected_locations_inventory');
        });
    }
}; 