<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     * 
     * Performance indexes for ebay_variations table
     * Optimizes user and SKU-based filtering operations at billion scale
     */
    public function up(): void
    {
        Schema::table('ebay_variations', function (Blueprint $table) {
            // Critical composite index for user-based SKU filtering
            // Supports: WHERE ebay_user_id = ? AND sku = ?
            $table->index(['ebay_user_id', 'sku'], 'idx_ebay_variations_user_sku');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ebay_variations', function (Blueprint $table) {
            $table->dropIndex(['idx_ebay_variations_user_sku']);
        });
    }
}; 