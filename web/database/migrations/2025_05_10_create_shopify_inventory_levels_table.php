<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shopify_inventory_levels', function (Blueprint $table) {
            $table->id();
            // Foreign key to shopify_variants table
            $table->foreignId('shopify_variant_id')
                ->constrained('shopify_product_variants')
                ->onDelete('cascade');
            
            // Session relationship
            $table->foreignId('session_id')
                ->constrained('sessions')
                ->onDelete('cascade');
            
            // Shopify specific fields - using unsignedBigInteger for Shopify's numeric IDs
            $table->unsignedBigInteger('shopify_inventory_level_id');
            $table->unsignedBigInteger('shopify_location_id');
            
            // Inventory quantities
            $table->integer('available_quantity')->default(0);
            $table->integer('committed_quantity')->default(0);
            $table->integer('on_hand_quantity')->default(0);
            
            // Tracking fields
            $table->timestamp('shopify_updated_at');
            $table->timestamps();
            
            // Indexes for performance
            $table->index('shopify_inventory_level_id');
            $table->index('shopify_location_id');
            $table->index('shopify_updated_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shopify_inventory_levels');
    }
}; 