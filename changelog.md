4# Changelog
All notable changes related to eBay Integration & Sync after 2023-11-20 will be documented in this file.

[//]: # (Line 5 should always indicate current stable version Donot change line 5)

Current Stable version:v11.9.3

### [11.9.3] 2025-07-24
### Fixed
 - EB[2116] App is glitching on mobile device.

### [11.9.2] 2025-07-23
### Fixed
 - EB[2106] Product inventory sync to eBay from all the locations despite connecting to one locations in the app

### [11.9.1] 2025-07-22
### Fixed
 - EB[2105] eBay products end automatically | Uploaded from our app

### [11.9.0] 2025-7-21
### Added
 - EB[2069] Need to sync shipping address as the billing address

### [11.8.1] 2025-07-15
### Fixed
 - EB[2083] Create a sample order with GST included in Shopify.

### [11.8.0] 2025-07-11
### Added
 - EB[2088] Change/Add the App page wise documentation link.

### [11.7.0] 2025-07-11
### Added
 - EB[2082] Migrate Page title to App brige titlebar

### [11.6.0] 2025-07-09
### Added
 - EB[1897] EPS Implementation
 - EB[1974] Revise the ebay product with eps image urls
 - EB[1973] Validate shopify image urls while uploading to eps for ebay product
 - EB[1898] Upload Shopify image to eBay.

###[11.5.2] 2025-07-09
### Fixed
 - EB[1985] Add location selection on resolve button from the "Quantity Not Valid" error

### [11.5.1] 2025-07-08
### Fixed
 - EB[2080] Items being automatically relisted on eBay caused overselling.

### [11.5.0] 2025-07-07
### Added
- EB[2039] Add a tab filter on every page.
- EB[2041] Make failed and uploaded status on profile page clickable and redirect to specific tab

### [11.4.0] 2025-06-30
### Added
 - EB[2023] Automatically relist item if instead of Auction ended error message

### [11.3.0] 2025-06-26
### Added
 - EB[1996] Fetch assigned category metafield definitions during profile attribute mapping.
 - EB[1998] Map Category Metafield Value while syncing to eBay

### [11.2.0] 2025-06-23
### Added
 - EB[2046] Add what's new section on dashboard

### [11.1.1] 2025-06-18
### Fixed
 - EB[2024] Client had issue with Invalid variation specific name for pictures

### [11.1.0] 2025-06-17
### Added
 - EB[1997] Add resolve button on selling limit error.
 
### [11.0.0] 2025-06-16
### Added
 - EB[1937] Fetch location wise inventories.
 - EB[1941] Show total inventory of the product from enabled locations only.
 - EB[1938] Sync location update from Shopify to the app.
 - EB[1940] Add location filter in Shopify products page.
 - EB[1971] When a user creates a new location, a message should be displayed under "Things to Consider."
 - EB[1936] Give an option to choose locations for inventory sync to eBay. 
 - EB[1970] If a user creates a new location, they should receive an email.
 - EB[1939] The shopify location should sync to the app automatically.

### [10.43.1] 2025-06-12
### Fixed
 - EB[2016] Sorting products in eBay Products section not working

### [10.43.0] 2025-06-10
### Added
 - EB[2013] Optimize bulk update  while syncing collection

### [10.42.0] 2025-06-10
### Added
 - EB[1979] API update to 2024-10

### [10.41.0] 2025-06-10
### Added
 - EB[2009] Upgrade shopify sync package to 2.1.5

### [10.40.1] 2025-06-04
### Fixed
 - EB[1991] Selecting collections across multiple pages then uploading is not uploading all the selected collections | Investigation Card

### [10.40.0] 2025-06-03
### Added
 - EB[1992] Linked Inventory section count mismatch

### [10.39.0] 2025-06-03
### Added
 - EB[1990] Keep the user-defined sorting value of Shopify products section without resetting  

### [10.38.4] 2025-06-02
### Fixed
 - EB[1993] Error with accessing our application on Chrome browser
 
### [10.38.3] 2025-05-28
### Fixed
 - EB[1983] Issue with switching pages when searching collections to upload to eBay | 

### [10.38.2] 2025-05-21
### Fixed
 - EB[1967] Order sync limit utilized above 70% - Receive this email for Feb on May.

### [10.38.1] 2025-05-20
### Fixed
 - EB[1964] Condition and Condition description is not syncing.

### [10.38.0] 2025-05-19
### Added
 - EB[1925] Sync eBay's Condition and Condition description to Shopify while importing products.
 
### Fixed
 - EB[1962] Shopify updates are not automatically synced to eBay | client needs to update the entire profile.

### [10.37.0] 2025-05-16
### Added
 - EB[1953] Connected profile should be displayed on the Shopify products page for associated products.

### [10.36.4] 2025-05-15
### Fixed
 - EB[1957] All eBay products not synced to our application

### [10.36.3] 2025-05-14
### Fixed
 - EB[1951] Shipping address didn't synced to Shopify order

### [10.36.2] 2025-05-09
### Fixed
 - EB[1944] eBay order not being synced to our application
 
### [10.36.1] 2025-05-08
### Fixed
 - EB[1929] Metafield data not syncing in correct format 
 - EB[1932] Invalid shopify variant in the system while importing to shopify

### [10.36.0] 2025-05-06
### Added
 - EB[1919] Show Optimization Call banner for those clients whose Shopify creation date has already reached 43 days.

### [10.35.0] 2025-05-05
### Added
 - EB[1840] Product titles are being truncated instead of suffix when uploading to eBay

### [10.34.2] 2025-05-02
### Fixed
 - EB[1907] Map the measurement metafield on eBay

### [10.34.1] 2025-05-02
### Fixed
 - EB[1912] Order synced to Shopify with eBay's modified title.

### [10.34.0] 2025-05-02
### Added
 - EB[1868] eBay UPCs/GTIN should be synced to the Barcode section in Shopify when importing products.

### [10.33.0] 2025-04-29
### Added
 - EB[1888] Map shopify metafields to eBay 
 - EB[1869] Show metafield mapping in attribute mapping section dynamically in profile
 - EB[1870] Mapped metafields should be synced to ebay as set on profile.
 - EB[1271] Save and show the selected metafields mapping on profile.

### [10.32.0] 2025-04-24
### Added
 - EB[1858] Map eBay brand with Shopify vendor while importing products from eBay to Shopify.

### [10.31.3] 2025-04-23
### Fixed
 - EB[1891] Need to modify the address in order when sync to Shopify | Investigation Card

### [10.31.2] 2025-04-22
### Fixed
 - EB[1890] Shopify products are not being fetched to our application

### [10.31.1] 2025-04-18
### Fixed 
 - EB[1883] Issue while syncing order | Invalid address 

### [10.31.0] 2025-04-18
### Added
 - EB[1879] Update the phone number wherever it is used in the application.

### [10.30.5] 2025-04-16
### Fixed
 - EB[1867] Change the title syncing while resolving duplication listiing violation policy 

### [10.30.4] 2025-04-16
### Fixed
 - EB[1874] Need to sync order with invalid address using clients provided address 

### [10.30.3] 2025-04-11
### Fixed
 - EB[1872] Faced an issue while importing the product

### [10.30.2] 2025-04-09
### Fixed
 - EB[1861] Shipping Address on Shopify Order Missing Information Provided in eBay Order

### [10.30.1] 2025-04-09
### Fixed
 - EB[1844] Update Ebay products skus to shopify products for the client

### [10.30.0] 2025-04-08
### Addde
 - EB[1785] Change optimized store sync warning banner to info banner
 - EB[1774] Remove critical action button from modals

### [10.29.0] 2025-04-07
### fixed
 - EB[1839] Not imported button not showing on iPad Air 11 in M3

### Added
 - EB[1847] Show 50 products at once and remove the pagination on both the Shopify and eBay product pages.
 - EB[1848] Change the color of the "End on eBay" and "Delete from Shopify" buttons from red to black.
 - EB[1849] Upload collection button should be shown as a secondary action

### [10.28.1] 2025-04-07
### Fixed
 - EB[1846] Order synced to Shopify without shipping address 

### [10.28.0] 2025-04-07
### Added
 - EB[1779] Add icons instead of bullets for errors in profile details page
 - EB[1776] Make fulfillment status gray on orders page 

### [10.27.2] 2025-04-04
### Fixed
 - EB[1843] Resolve item cannot be accessed error while relisting

### [10.27.1] 2025-04-04
### Fixed
 - EB[1841] GDPR webhooks are failing

### [10.27.0] 2025-04-03
### Added
 - EB[1820] Change title fetching strategy while syncing to eBay 

### [10.26.0] 2025-04-02
### Added
 - EB[1772] Remove hidden line on right side of each page/frame

### [10.25.0] 2025-04-01
### Added
 - EB[1771] Plan Cards on App Plans page should have consistent height

### [10.24.0] 2025-04-01
### Added
 - EB[1773] Show cursor pointer only for clickable elements
 - EB[1775] Change "Change your eBay store" from red to blue in dashboard

### [10.23.0] 2025-03-31
### Added
 - EB[1769] Group settings cards when there are more than 5 blocks

### [10.22.1] 2025-03-28
### Fixed
 - EB[1822] eBay Order not syncing to Shopify

### [10.22.0] 2025-03-27
### Added
 - EB[1768] Make info banners collapsible, even in inner components 

### [10.21.0] 2025-03-27
### Added
 - EB[1818] Add read location scope
 
### [10.20.0] 2025-03-26
### Added
 - EB[1767] Make info banners dismissable on all pages

### [10.19.0] 2025-03-26
### Added
 - EB[1766] Make pages default width if there is less data 

### [10.18.0] 2025-03-26
### Added
 - EB[1765] Ensure navigation menu and page titles are consistent 
 - EB[1787] Remove "view profile settings" button in profile detail page
 - EB[1790] Move "Learn More" buttons to footer section on each page

### [10.17.0] 2025-03-25
### Added
 -  EB[1764] Make pagination links sticky with keyboard navigation support

### [10.16.0] 2025-03-25
### Added
 - EB[1763] Adjust the navigation menu active state based on the current page 

### [10.15.2] 2025-03-24
### Fixed
 - EB[1806] Product Imported to Shopify as 0 Price

### [10.15.1] 2025-03-24
### Fixed
 - EB[1802] Refactor linking shopify variant strategy while syncing ebay order to shopify

### [10.15.0] 2025-03-24
### Added
 - EB[1762] Add back button in each inner page

### [10.14.0] 2025-03-21
### Added
 - EB[1803] Add Button to enable and disable the Optimization banner in the support dashboard.
 
### [10.13.0] 2025-03-21
### Added
 - EB[1748] Upgrade APIs to 2024-07

### [10.12.0] 2025-03-19
### Added
 - EB[1796] Include Shopify Store Specifier in the Order Failed Notification email.

### [10.11.0] 2025-03-17
### Fixed
 - EB[1758] Ended products on eBay should be removed from Profile aswell. 

### [10.10.0] 2025-03-12
### Added
 - EB[1703] AI assisted title modification
 - EB[1706] Ask user to upgrade plan for AI enable.
 - EB[1705] AI generated title on resolve model.
 - EB[1707] Cache AI Generated Title.

### [10.9.1] 2025-03-11
### Fixed
 - EB[1742] Resolve now for description and weight not working 

### [10.9.0] 2025-03-11
### Added
 - EB[1544] Adjust Inventory for Orders with Committed 
 - EB[1731] Send order sync fail email due to inventory issue
 - EB[1729] Show order sync failed message in order page
 - EB[1737] Send an email to the user every 6 hours if an order fails to post.
 - EB[1736] Add an option for failed order notifications on the settings page.
 
### [10.8.2] 2025-03-06
### Fixed
 - EB[1733] Issue with importing eBay products to Shopify | Keep Spinning 
 
### [10.8.1] 2025-03-03
### Fixed
 - EB[1725] Condition: Certified Refurbished not showing while uploading to eBay | Investigation Card

### [10.8.0] 2025-02-25
### Added
 - EB[1711] Client wants to sync the description only from eBay to Shopify 

### [10.7.1] 2025-02-24
### Fixed
 - EB[1716] Images not switching for eBay product when color variation is changed on eBay's side.

### [10.7.0] 2025-02-21
### Added
 - EB[1717] Sync Order cancellation for seller other than Europe and UK

### [10.6.0] 2025-02-20
### Added
 - EB[1355] Sync images based on image sync setting on profile
 - EB[1670] Image sync setting in profile
 - EB[1354] Add image sync setting on profile

### [10.5.0] 2025-02-19
### Added 
 - EB[1451] After filtering products by location, we should be able to import all at once for that location only.
 
###Fixed
 - EB[1715] Tag not syncing correctly while uploading from Shopify to eBay

### [10.4.3] 2025-02-12
### Fixed
 - EB[1698] Face JavaScript error and page goes blank

### [10.4.2] 2025-02-10
### Fixed
 - EB[1694] eBay and Shopify icons not aligned properly in the 'View On' column

### [10.4.1] 2025-02-07
### Fixed
 - EB[1690] Ended eBay item still active in app even after revise inventory status api error

### [10.4.0] 2025-02-06
### Added
 - EB[1689] Update FAQ section in our application

### [10.3.1] 2025-02-06
### Fixed 
 - EB[1684] Error: Input data is invalid while uploading the products to eBay

### [10.3.0] 2025-02-05
### Added
 - EB[1662] Limit order according to pricing  plan
 - EB[1376] Mail user if the order limit is reached 
 - EB[1375] Prevent order to be posted according to the plan limits
 - EB[1378] Display critical banner once the order limit is reached
 - EB[1377] Reset monthly order limits of all user
 - EB[1379] Display order limit information in the dashboard

### [10.2.4] 2025-02-04
### Fixed
 - EB[1680] Receiving a warning email from eBay because Shopify is sending order update emails to the eBay email address.

### [10.2.3] 2025-01-31
### Fixed
 - EB[1679] EAN not mapped for variant products.

### [10.2.2] 2025-01-29
### Fixed
 - EB[1671] Shopify store currency not reflecting on app's currency setting page.

### [10.2.1] 2025-01-24
### Fixed
 - EB[1667] Varinats images are not switching to correct one when clicking on different variants on ebay

### [10.2.0] 2025-01-24
### Added
 - EB[1668] Update dpl/shopifysync package to 2.0.3

### [10.1.6] 2025-01-23
### Fixed
 - EB[1660] Upload Issue | Shipping services showing invalid but the same shipping is used during manual upload is working
 - EB[1595] removed policy are still in our app even after clicking fetch business policy

### [10.1.5] 2025-01-23
### Fixed
 - EB[1657] Fulfillment not syncing to eBay 

### [10.1.4] 2025-01-23
### Fixed
 - EB[1635] Products not shown as imported after importing to Shopify | causing duplicated imports 

### [10.1.3] 2025-01-23
### Fixed
 - EB[1658] Order Prefix settings adds an additional space in the order ID 

### [10.1.2] 2025-01-22
### Fixed
 - EB[1646] Removing the attribute and updating the profile is not working | Still saves the removed attribute.

### [10.1.1] 2025-01-22
### Fixed
 - EB[1629] Resolve button for weight not working

### [10.1.0] 2025-01-21
### Added
 - EB[1656] Handle closed store 

### [10.0.0] 2025-01-20
### Added
- EB[1536] Integrate Shopify Sync Graphql for product sync.
- EB[1537] Integrate Shopify Sync Graphql for collection sync.
- EB[1587] Show progress bar while importing products from shopify
- EB[1538] Optimize product update process to ebay from regular product sync.

### [9.3.0] 2025-01-20
### Added
 - EB[1644] Improvise pricing page to accept dynamic description
Current Stable version:v10.0.0

### [9.2.5] 2025-01-16
### Fixed
 - EB[1608] Issue with the Refund owed showing on the order

### [9.2.4] 2025-01-14
### Fixed
 - EB[1623] Products removed from Shopify is not ended on eBay

### [9.2.3] 2025-01-13
### Fixed
 - EB[1626] eBay order Discount not applied in the order while syncing to Shopify.

### [9.2.2] 2025-01-03
### Fixed
 - EB[1611] The application is running slower than expected.

### [9.2.1] 2025-01-03
### Fixed
 - EB[1579] Screen stuck on product upload from Shopify to eBay 

### [9.2.0] 2025-01-02
### Added
 - EB[1574] Migrate Product Delete webhook to aws arn for old users

### [9.1.7] 2025-01-01
### Fixed
 - EB[1596] eBay Business policies not syncing to our application

### [9.1.6] 2024-12-31
### Fixed
 - EB[1588]The tax synced to Shopify is not correct.

### [9.1.5] 2024-12-31
### Fixed 
 - EB[1553] Moniter failed webhooks rate for the app

### [9.1.4] 2024-12-31
### Fixed
 - EB[1590] I encounter an error when I click the "Create Business Policy" button on the UK site.

### [9.1.3] 2024-12-30
### Fixed
 - EB[1585] Imported all 5480 products from eBay to Shopify but only imported 3857 and 1460 Showing upload to eBay in app

### [9.1.2] 2024-12-26
### Fixed 
 - EB[1565] Prevent jobs execution if refresh token expired is caught

### [9.1.1] 2024-12-25
### Fixed
 - EB[1577] Our app keeps adding the removed email address in Shopify.

### [9.1.0] 2024-12-24
### Added 
 - EB[1560] Display the notice message if the shopify variation  limit is reached.

### [9.0.6] 2024-12-24
### Fixed 
 - EB[1567] After clicking on sync button of eBay, the page is being flashing

### [9.0.5] 2024-12-19
### Fixed 
 - EB[1571] Order Sync Delayed | Investigation Card

### [9.0.4] 2024-12-16
### Fixed 
 - EB[1566] Deleted Shopify products is still showing in our application

### [9.0.3] 2024-12-13
### Fixed
 - EB[1554]Refactor accessToken expired exception while uploading to eBay

### [9.0.2] 2024-12-12
### Fixed
 - EB[1556] Client wants us to stop the export process

### [9.0.1] 2024-12-10
### Fixed
 - EB[1551] Page stuck on exporting products to Shopify

### [9.0.0] 2024-12-10
### Added
 - EB[1539] Update product creation to 2024-04 GQL process.
 - EB[1540] Update variant creation to 2024-04 GQL process.

### [8.0.1] 2024-12-06
### Fixed
 - EB[1550] Update the message displayed on the subscription page and modify the button text.

### [8.0.0] 2024-12-05
### Added
 - EB[1533] Display multiple pricing plans without limitations.

### [7.7.0] 2024-12-05
### Added
 - EB[1543] Subscribe new users to aws for product delete webhook

### [7.6.0] 2024-12-04
### Added
 - EB[1520] Migrate Product Delete webhook to aws arn

### [7.5.1] 2024-11-28
### Fixed
 - EB[1525] Handle the update process when an order is deleted from Shopify
 - EB[1526] Don't retry creating an order for a store with a dormant plan.

### [7.5.0] 2024-11-26
### Added
 - EB[1495] Subscribe old users orders updated webhook to aws arn

### [7.4.0] 2024-11-18
### Added
 - EB[1477] Auction Ended error handling
 - EB[1480] Relist the product if it was not relisted previously while resolving  the “Auction Ended” Error
 - EB[1479] Check and link erroneous products to relisted products if exists
 - EB[1478] Add a resolve button to the “Auction Ended” Error

### [7.3.0] 2024-11-15
### Added
 - EB[1494] Optimize product upload or update process

### [7.2.0] 2024-11-15
### Added
 - EB[1492] Subscribe new users to aws for order updated webhook

### [7.1.0] 2024-11-14
### Added
 - EB[1333] Configure OrderUpdated Webhook in AWS SQS

### [7.0.2] 2024-11-13
### Fixed
 - EB[1483] Duplicate Products Issue.

### [7.0.1] 2024-11-6
### Fixed
 - EB[1457] Does not include the shipping costs to the taxes from eBay to Shopify orders.

### [7.0.0] 2024-11-06
### Added
 - EB[1284] Orders of items in bundles in Shopify are not syncing. [Research]
 - EB[1440] Fetch bundle details and map line items to bundle variants while syncing
 - EB[1460] Calculate and sync refunds according to order bundle relationship
 - EB[1461] Sync fulfillment based on order bundle relationship

### [6.6.4] 2024-10-30
### Fixed
 - EB[1447] Duplicate order is created to Shopify while syncing

### [6.6.3] 2024-10-30
### Fixed
 - EB[1450] The Shopify product title appears twice on the linked inventory page.

### [6.6.2] 2024-10-29
### Fixed
 - EB[1448] Query Refactor - Linked Inventory Page 

### [6.6.1] 2024-10-28
### Fixed
 - EB-1443 Server Optimization

### [6.6.0] 2024-10-21
### Added
 - EB[1401] Inventory sync for untracked or continue-selling
 - EB[1383] Handle inventory sync cases for Not tracked and "Continue Selling When Out of stock " 
 - EB[1391]Add a setting in general section for a value of custom quantity when untracked or continue selling when out of stock
 - EB[1430]Add Information message in the profile under Inventory settings.

### [6.5.4] 2024-10-17
### Fixed 
 - EB[1329] Issue with Order Fulfillment Status Sync 

### [6.5.3] 2024-10-16
### Fixed
 - EB[1423] error: remarks JavaScript not Allowed 

### [6.5.2] 2024-10-07
### Fixed
 - EB[1400] "Set Prefix for Shopify Order ID" option not working

### [6.5.1] 2024-10-02
### Fixed
 - EB[1384] Importing products from eBay to Shopify stuck for 20 hours now

### [6.5.0] 2024-10-01
### Added
 - EB[1343] Adjust Dimension values for the client while updating to ebay 
 
### Fixed
 - EB[1363] Inventories minused one (-1 ) for every product variation | Need to investigate.

### [6.4.4] 2024-09-30
### Fixed
 - EB[1370] The order has not been posted to the country codes 'AS', 'GU', 'MP', 'UM', or 'VI'.

### [6.4.3] 2024-09-25
### Fixed
 - EB[1347] Image did not sync when importing to Shopify

### [6.4.2] 2024-09-25
### Fixed
 - EB[1353] The extend Trial button does not disappear even after accepting the Trial.

### [6.4.1] 2024-09-20
### Fixed
 - EB[1342] Error : Revise item failed.

### [6.4.0] 2024-09-18
### Added
 - EB[1341] Handle Product Delete Webhook in queued job

### [6.3.2] 2024-09-18
### Fixed
 - EB[1337] When uploaded from Shopify to eBay product status is " Not Uploaded "

### [6.3.1] 2024-09-18
### Fixed
 - EB[1335] Orders are not syncing from eBay to Shopify | Taking too long 

### [6.3.0] 2024-09-17
### Added
 - EB[1316] Refresh Metafields in our app 

### [6.2.0] 2024-09-16
### Added
 - EB[1131] Integrate Partnerjam

### [6.1.1] 2024-09-11
### Added
 - EB[1360] Country/region not supported while posting order to Shopify
 - EB[1296] Order Shippment | Mark as Shipped from Shopify but not synced to eBay

### [6.1.0] 2024-09-10
### Added
 - EB[1286] create user profile if user doesnot exist when updating user data daily
 - EB[1290] Create " new user " tag on Crisp once the user install our application.

### [6.0.0] 2024-09-09
### Added
 - EB[1175] Restructure existing app
 - EB[1269] Cancellation status should be sync between Shopify and eBay | Investigation card.

### [5.17.0] 2024-09-03
### Added
 - EB[1233] Create A daily scheduler to  update app data to crisp
 - EB[1274] Add Phone number to custom data.

### [5.16.1] 2024-09-02
### Fixed
 - EB[1280] eBay error : Javascript not allowed
 - EB[1282] Wrong order is syncing from eBay to Shopify | Variant product with same SKUs

### [5.16.0] 2024-08-30
### Added
 - EB[1193] Show rating card on successful import and upload
 - EB[1197] Show rating card on app dashboard.
 - EB[1192] Save user ratings 
 - EB[1196] Don't show the rating once the user submit the rating

### [5.15.0] 2024-08-27
### Added
 - EB[1234] Create crisp user during installation

### [5.14.4] 2024-08-27
### Fixed
 - EB[1253] Products are showing as "Not uploaded" while trying to upload as collection
 - EB[1258] Integrate Faro

### [5.14.3] 2024-08-26
### Fixed
 - EB[1254] eBay order address incorrectly sync to Shopify.

### [5.14.2] 2024-08-26
### Fixed
 - EB[1255] LCP Optimization.

### [5.14.1] 2024-08-26
### Fixed
 - EB[1240] Image not uploaded to eBay issue

### [5.14.0] 2024-08-23
### Added
 - EB[1244] Improvise FAQ page in the App.

### [5.13.4] 2024-08-22
### Fixed
 - EB[1245] Flickering issue.

### [5.13.3] 2024-08-20
### Fixed
 - EB[1238] Change email address in reauthenticate email template

### [5.13.2] 2024-08-19
### Fixed
 - EB[1236] Loader in Linked Inventory section kept on going for hours

### [5.13.1] 2024-08-19
### Fixed 
 - EB[1229] Product Images are not changing as per their variation on eBay.

### [5.13.0] 2024-08-16
### Fixed
 - EB[1231] Send user data and shopify details to crisp 

### [5.12.4] 2024-08-15
### Fixed
 - EB[1235] The user get stuck while importing a shopify product

### [5.12.3] 2024-08-14
### Fixed
 - EB[1218] No order & inventory setting not working | Import from eBay to Shopify

### [5.12.2] 2024-08-13
### Fixed
 - EB[1215] Client stuck on shopify products page while importing

### [5.12.1] 2024-08-09
### Fixed
 - EB[1212] Umalaut character need to be supported on eBay

### [5.12.0] 2024-08-09
### Added
 - EB[1213] Integrate Partnerjam

### [5.11.0] 2024-08-06
### Added
 - EB[1208] Move collection between profiles

### [5.10.1] 2024-08-05
### Fixed
 - EB[1205] XML Parse Error 

### [5.10.0] 2024-08-02
### Added
 - EB[1106] Add Things to consider on the dashboard once the optimization is completed.
 - EB[1199] Add Ask for help link in the error remarks on the profile page.
 - EB[1202] Collection filter is not closing when clicked outside the popover on add product modal
 - EB[1200] Replace the welcome banner with optimize your store banner

### [5.9.0] 2024-08-01
### Added
 - EB[1101] Show modal to let user select products to add in the profile
 - EB[1194]Add collection filter to add product modal on profile
 - EB[1102] Let user filter the products by title

### [5.8.0] 2024-07-31
### Fixed
 - EB[1105] Add filter by " Last created " in both Shopify and eBay products page.

### [5.7.9] 2024-07-30
### Fixed
 - EB[1172] Importing to Shopify taking a lot of time to complete 

### [5.7.8] 2024-07-26
### Fixed
 - EB[1167] Prices setting being unchecked while selecting an existing profile

### [5.7.7] 2024-07-26
### Fixed
 - EB[1170] Map Shopify SKUs to the MPN field in the eBay.

### [5.7.6] 2024-07-25
### Fixed
 - EB[1165] "During bulk product deletion, the message displayed is 'Ending eBay product".

### [5.7.5] 2024-07-25
### Fixed
 - EB[1166] Error "Missing name in name-value list."

### [5.7.4] 2024-07-23
### Fixed
 - EB[1163] When eBay motor selling region is selected then US region related products need to get sync as well

### [5.7.3] 2024-07-23
### Fixed
 - EB[1164] eBay username needs to be sync with the order on Shopify as a tag.

### [5.7.2] 2024-07-19
### Fixed
 - EB[1152] Removing products from Profile not working 

### [5.7.1] 2024-07-19
### Fixed
 - EB[1159] Linked inventory page goes blank while changing pagination. 
 - EB[1158] Profile page goes blank when changing pagination after clicking 'View Profile'
  
### [5.7.0] 2024-07-18
### Added
 - EB[1156] Upgrade App Bridge  React from v3 to v4

### [5.6.1] 2024-07-17
### Fixed
 - EB[1154] App went blank when not imported option is selected from dashboard

### [5.6.0] 2024-07-16
### Added
 - EB[1124] Use contextual save bar placed at the top of the page

### [5.5.0] 2024-07-16
### Added
 - EB[1137] Adjust Image size in product list pages
 - EB[1124] Adjust shopify product page,Bulk upload page, Profile Page Layout to Non-Full Width
 - EB[1125] Move Back Button to Top Left If page appeared from parent page
 - EB[1120] Setting page Layout should only have a container for the right column
 - EB[1126] Adjust order page to default width layout, add divider in order line items.

### [5.4.2] 2024-07-11
### Fixed
 - EB[1134] "Not Imported" section not function as it should

### [5.4.1] 2024-07-10
### Fixed
 - EB[1110] Collection update not reflecting on Our application 

### [5.4.0] 2024-07-09
### Added
 - EB[1114]  Add not optimized message on dashboard page

### [5.3.2] 2024-07-05
### Fixed
 - EB[1076] eBay Error: Condition descriptor value 61866093 is not valid for condition descriptor 27503. |  Valid information is required for descriptor Certification Number

### [5.3.1] 2024-07-03
### Fixed
 - EB[1087] Resolve button not working for the client

### [5.3.0] 2024-07-02
### Added
 - EB[1081] Apply polaris pattern on card layout ,resource-index-layout

### [5.2.1] 2024-07-01
### Fixed
 - EB[1084] Importing  Low-Resolution Image Quality from eBay to Shopify 

### [5.2.0] 2024-06-28
### Added
 - EB[1082] Optimize shopify sync process to optimize CPU usage

### [5.1.1] 2024-06-28
### Fixed
 - EB[1080] Change the banner color in the profile section while removing the product.

### [5.1.0] 2024-06-27
### Added
 -  EB[608] polaris pattern  on settings pages, index pages, and resource detail pages
   
### [5.0.0] 2024-06-27
### Added
 - EB[1059] Migrate to polaris v12
 - EB[608] polaris pattern  on settings pages, index pages, and resource detail pages

### [4.9.1] 2024-06-27
### Added
 - EB[1075] After full sync as well the Shopify inventory are not synced to eBay | Need to investigate.

### [4.9.0] 2024-06-27
### Added
 - EB[609] Use full-width layout only on large lists of data, or pages with three columns.

### [4.8.0] 2024-06-25
### Added
 - EB[610] Non critical Banners must be dismissible.

### [4.7.3] 2024-06-24
### Fixed
 - EB[1066] eBay Error: Release Title's value of "Nat King Cole - The Christmas Song (Limited Edition, Color Vinyl) (LP)" has too many characters.

### [4.7.2] 2024-06-21
### Fixed
 - EB[1064] Client could not add currency below 0.1

### [4.7.1] 2024-06-20
### Fixed
 - EB[1047] Issue with images not being imported when importing products from eBay to Shopify

### [4.7.0] 2024-06-18
### Fixed
 - EB[1054] Integrate Crisp chat and move helpscout links to open in new tab

### [4.6.2] 2024-06-18
### Fixed
 - EB[1052] Full name of Attributes not syncing to eBay

### [4.6.1] 2024-06-17
### Fixed
 - EB[1053] Reverse sync is not working for clients without eBay Products

###[4.6.0] 2024-06-17
###Added
 - EB[1046] Automatic Linking Issue | Titles and SKUs are same between shopify and eBay.

###[4.5.5] 2024-06-14
###Fixed
 - EB[1043] Client able to access the eBay products page while reverse sync is in progress.

###[4.5.4] 2024-06-12
###Fixed
 - EB[1042] Automatic Linking Issue | Products are already available on Shopify, but there is an option to import them to eBay.

###[4.5.3] 2024-06-11
###Fixed
 - EB[1039] Error: Description contain HTTP resources | Importing from Shopify to eBay

###[4.5.2] 2024-06-10
###Fixed
 - EB[1035] OUR APPLICATION NOT SYNCING THE UPDATES FROM SHOPIFY 

###[4.5.1] 2024-06-06
###Fixed
 - EB[1030] Need to remove the Address Line 2 from the order.

###[4.5.0] 2024-06-06
###Added
 - EB[994] UK client will love to see VAT included on items uploaded by our app

###[4.4.1] 2024-06-05
###Fixed
 - EB[1027] Order status not syncing to Shopify

###[4.4.0] 2024-06-05
###Added
 - EB[1029] Subscribe product update webhook of existing client to eventbridge

###[4.3.0] 2024-06-04
###Added
 - EB[1025] Integrate grafana faro react
 
###Fixed
 - EB[1024] Shopify Inventory is not updated to eBay.

###[4.2.0] 2024-06-03
###Added
- EB[880] Subscribe product update webhook to eventbridge for new clients

###[4.1.0] 2024-06-03
###Added
 - EB[971] Display a notice message if new users have shared SKUs.

###[4.0.0] 2024-05-31
###Added
 - EB[700] Integrate eventbridge on ebay integration and sync
 - EB[880] Subscribe product update webhook to eventbridge for new clients
 - EB[1020] Restart Command for Sqs Poll Workers

###[3.6.1] 2024-05-24
###Fixed
 - EB[1007] Image sync not working

###[3.6.0] 2024-05-22
###Added
  - EB[1002] Out of stock/deleted Shopify products is showing stock on eBay products

###[3.5.3] 2024-05-21
###Fixed
  - EB[1004] INVENTORY NOT GETTING UPDATED ON EBAY WHEN SALE MADE IN SHOPIFY

###[3.5.2] 2024-05-16
###Fixed
  - EB[991] Price sync | eBay product imported to Shopify is showing different prices

###[3.5.1] 2024-05-14
###Fixed
  - EB[980] Link products based on SKU regardless of title

###[3.5.0] 2024-05-13
###Added
  - EB[993] Remove Status and add Site.
###Fixed
  - EB[979] Products are available in both Shopify and eBay but listed in the " Not Imported" section of our app.

###[3.4.0] 2024-05-10
###Added
  - EB[987] Removal of virtual tracking number added in addressline2 from ebay order posted using Royal Mail Shipping Carrier while import order on shopify

###[3.3.2] 2024-05-09
###Fixed
  - EB[981] Sync only Active listings from eBay

###[3.3.1] 2024-05-08
###Fixed
  - EB[984] Variant image not synced while importing eBay products to Shopify

###[3.3.0] 2024-05-06
###Added
  - EB[972] Provide an option to enable or disable the sharing of SKUs through the settings menu.

###[3.2.2] 2024-04-24
###Fixed
  - EB[877] New added variation in a Shopify Variation products is not updated to eBay.
  - EB[952] If variation synced is turned off, then the SKU quantity and EAN number are not synced.
  - EB[964] Once the product is out of stock, it is not listed again.

###[3.2.1] 2024-04-18
###Fixed
  - EB[950] Valid EAN number sync as  " Does not Apply " in eBay.

###[3.2.0] 2024-04-18
###Added
  - EB[934] Add ebay error remarks while saving profile with non-existing store categories
###Fixed
  - EB[945] No store category found with the search keyword
  
###[3.1.0] 2024-04-18
###Added
  - EB[949] Add banner to first skeleton while loading page
  - EB[947] Implement translation for text with inline buttons

###[3.0.2] 2024-04-17
###Fixed
  - EB[941] Some products are being duplicated when importing to Shopify 

###[3.0.1] 2024-04-16
###Fixed
 - EB[943] Not able to Import Variation products to eBay due to invalid EAN number.

###[3.0.0] 2024-04-15
###Added
 - EB[764] Feature : Store Categories 
 - EB[921] Add refetch store categories in profile create/edit page
 - EB[917] Post storecategories selection while uploading product to ebay
 - EB[765] Let User select store categories in profile create/edit
 - EB[836] Saving Store categories while ebay store is connected

###[2.17.7] 2024-04-15
###Fixed
  - EB[936] Issue while importing eBay products to Shopify

###[2.17.6] 2024-04-12
###Fixed
  - EB[938] Not able to Import products to eBay due to invalid EAN number.

###[2.17.5] 2024-04-11
###Fixed
  - EB[926] Items are out of stock in Shopify but stocks available in eBay | Orders has been made for those products in eBay.

###[2.17.4] 2024-04-09
###Added
  - EB[615] Host build files to cloudfront

###[2.17.3] 2024-04-08
###Fixed
  - EB[923] Price must be uploaded as decimal value as well

###[2.17.2] 2024-04-05
###Fixed
  - EB[913] Product Syncing issues from eBay to Shopify.

###[2.17.1] 2024-04-02
###Fixed
  - EB[907] Imported Products being duplicated in eBay

###[2.17.0] 2024-04-02
###Added
  - EB[914] Add "Synced " & " Not Synced " filter option in the orders page of our app.

###[2.16.1] 2024-03-28
###Fixed
  - EB[904] eBay Error: "Invalid value provided for Weight Minor"

###[2.16.0] 2024-03-26
###Added
   - EB[851] Order should be synced based on the linked products | Currently it syncs based on SKUs only.

###[2.15.3] 2024-03-25
###Fixed
  - EB[860] Client are facing error while importing eBay products to Shopify while they are on trial period on shopify

###[2.15.2] 2024-03-22
###Fixed
  - EB[898] Order sync | While trying to manually linked the products, the product is not showing 

###[2.15.1] 2024-03-20
###Fixed
  - EB[892] eBay profile getting reset while uploading the product

###[2.15.0] 2024-03-18
###Added
  - EB[890] Update shopify API version

###[2.14.1] 2024-03-18
###Fixed
  - EB[878] optimize variation sync to our app in shopify-products sync command

###[2.14.0] 2024-03-13
###Added
  - EB[850] Need to Show the all attribute option as per their category while uploading products to eBay.
  
###[2.13.0] 2024-03-12
###Added
  - EB[837] Let user select location while creating and updating profile

###[2.12.8] 2024-03-07
###Fixed
  - EB[873] Getting condition error in the profile section of our app | even though client filled the condition value provided by our app.

###[2.12.7] 2024-03-05
###Fixed
  - EB[865] Order Sync | Removal of special german characters from the Customer's name in Shopify

###[2.12.6] 2024-03-01
###Fixed
  - EB[863] Resolve 'ebay products are not importing to shopify due to duplicate handle' issue
  - EB[862] While syncing eBay products to our app, stuck on the loading page only

###[2.12.5] 2024-03-01
###Fixed
  - EB[855] eBay orders are not syncing to Shopify.

###[2.12.4] 2024-02-28
###Fixed
   - EB[858] Variation Product Import to Shopify Syncing Price

###[2.12.3] 2024-02-27
###Fixed
   - EB[847] eBay Error: Das Feld EAN fehlt. while uploading products

###[2.12.2] 2024-02-20
###Fixed
   - EB[830] Not able to Truncate the long title | Resolve button not working.
   
###[2.12.1] 2024-02-20
###Fixed
  - EB[831]Shopify collection not showing to our app

###[2.12.0] 2024-02-20
###Added
  - EB[817]Conditions are not shown for belgium sites

###[2.11.3] 2024-02-19
###Fixed
  - EB[788]Fetch and store all the ShippingServiceDetails in a table
  - EB[789]Sync actual shipping carrier name to shopify while syncing order.

###[2.11.2] 2024-02-13
###Fixed
  - EB[732]Sync VAT from eBay and calculated VAT for VAT for VAT inclusive products 
  - EB[825]The total Shopify order amount is varies from eBay | No PST/QST tax included.

###[2.11.1] 2024-02-9
###Fixed
  - EB[811]Not able to select conditions for the selected category when uploading to eBay 

###[2.11.0] 2024-02-06
###Added
  - EB[766]Handle Duplicate Product Title Exception from shopify
  - EB[696]Need to sync the eBay product weight to Shopify while import eBay products to Shopify.
  - EB[748]Add an option for users to manually add custom tags while syncing orders from eBay to Shopify.
  
###[2.10.0] 2024-02-05
###Added
   - EB[769]Sync shipping policy as tag while importing products to Shopify
   
###[2.9.0] 2024-02-02
###Added
  - EB[786]Modifications to the pricing plan page

###[2.8.1] 2024-02-01
###Fixed
   - EB[790]Adding Author Name Attribute in Profile shows Blank Screen

###[2.8.0] 2024-01-30
###Added
  - EB[776]Add an option to select the " Barcode "  in our app's Profile’s “ Attribute Mapping “ section.
  
###[2.7.0] 2024-01-29
###Added
  - EB[688]Store selected sub-conditions on profile and also include them in  the request for upload/update


###[2.6.3] 2024-01-26
###Fixed 
  - EB[763]Tracking number is not syncing from Shopify to eBay.

###[2.6.2] 2024-01-25
###Fixed
   - EB[772]The customer placed an eBay order for 3 items, and when the order came through the app, only 2 items were showing.

###[2.6.1] 2024-01-25
###Fixed
  - EB[763]Tracking number is not syncing from Shopify to eBay.

###[2.6.0] 2024-01-24
###Added
   - EB[760]Upgrade pricing to $19.99

###[2.5.3] 2024-01-24
###Fixed
   - EB[771]App is automatically updating the customer address to its eBay default value after I have modified it on Shopify
   - EB[762]Issue while importing eBay products to Shopify | App says its created but it is not available in Shopify.

###[2.5.2] 2024-01-23
###Fixed
  - EB[756]Facing issues while Importing 22k eBay products to Shopify at once.

###[2.5.1] 2024-01-19
###Fixed
  - EB[761]Orders notes are being deleted by the app for the order imported from eBay

###[2.5.0] 2024-01-19
###Added
  - EB[686]Create migration to store and fetch  item conditions and its description from eBay
  - EB[704]Create endpoint to force sync inventory of Shopify products
###Fixed
  - EB[755]Tracking number is not syncing from Shopify to eBay.

###[2.4.0] 2024-01-15
###Added
  - EB[735]eBay orders in Shopify should be numbered incrementally, starting from the next available number after the previous order.
  - EB[737]Need to Improvise To-Do list from our app Dashboard.
  - EB[734]Add a settings which allows User to not Sync their eBay order Sales Tax to Shopify.

###[2.3.2] 2024-01-12
###Fixed
  - EB[733]All Products Images are not Syncing while uploading products from Shopify to eBay.

###[2.3.1] 2024-01-11
###Fixed
  - EB[726]Inventory shows wrong inside the app
###[2.3.0] 2024-01-10
###Added
  - EB[731]Fix query while fetching inventory data in linked inventory page

###[2.2.2] 2024-01-09
###Fixed
  - EB[709]Unable to sync | fetch the Shipping policy in our app.
  - EB[728]Update profile process is not completed.

### [2.2.1] 2024-01-09
### fIXED
  - EB[699]Client is getting Items cannot not be listed or modified error.

### [2.2.0] 2024-01-08
### Added
 - EB[723]Make the inventory sync based upon the eBay variant SKU

### [2.1.1] 2024-01-08
### Fixed
 - EB[725]Quantity is Tracked in Shopify but still out of stock product in Shopify is available in eBay.
 - EB[729]" Condition " Attribute is not showing in the profile but asked to provide condition before proceed to upload.
 
### [2.1.0] 2024-01-0
### Added
  - EB[702] add option on select profile to stop automatic upload on collection
  - EB[703]Check collection update before dispatching collection jobs
  - EB[693]Deleted products from eBay should not be re-uploaded
### Fixed
  - EB[692]eBay Products duplicated | Unable to change eBay store | 2 STAR CLIENT


### [2.0.0] 2024-01-04
### Added
  - EB[515]eBay Motors US marketplace support


### [1.9.3] 2024-01-03
### Fixed
  - EB[710]Handle the product changed from single to variation and again back to single.

### [1.9.2] 2024-01-02
### FIxed
  - Shopify product not found while searching with the product SKUs.
  - Getting Error Message while Updating the profile.
 
### [1.9.1] 2024-01-02
### Fixed
  - Image resolution is lower when importing listings from eBay to Shopify.

### [1.9.0] 2023-12-25
### Added
  - Fix item specific values more than 65 characters too long issue

###[ 1.8.0] 2023-12-25
### Added
  - Filter by Shopify collection in the Shopify Products page of our app.
  - Add resolve functionality on variation state change error

### [1.7.6] 2023-12-21
### Fixed
  - Price Settings not working properly

### [1.7.5] 2023-12-19
### Fixed
  - Issue while updating the existing profile.

### [1.7.4] 2023-12-19
###  Fixed
  - eBay orders are not syncing automatically to Shopify.

### [1.7.3] 2023-12-18
  - Fulfillment status remains " Unfulfilled " in our app.
 
### [1.7.2] 2023-12-15
### Fixed
  - Shopify Primary image and the eBay primary image are different.

###[1.7.1] 2023-12-13
### Fixed
  - Do not Sync the eBay orders prior to our app Installation, eventhough there is an update on eBay orders.

### [1.7.0] 2023-12-12
### Added
  - Fetch eBay user preferences periodically
  - Add eBay item cannot be listed or modified error message
### Fixed
  - Sync " Message to Seller Note" from eBay orders to Shopify.

### [1.6.1] 2023-12-7
### Fixed
  - Change the title name on eBay product page.
   - Shopify product is not found in our app.
   - The newly added translation is not loaded.
   
### [1.6.0] 2023-12-5
### Added
  - Syncing eBay orders are taking longer time than expected | Need to Investigate if there is some issue.
  
### [1.5.0] 2023-12-4
### Added
  - Add a confirmation page showing all the "Select all" products from the eBay products
  
### [1.4.0]
### Added
  - Two-column equal width layout on dashboard
### [1.3.2] 2023-11-29
### Fixed
   - All fields are not filled and Upload to Proceed button is disabled but User are able to upload automatically.
   
### [1.3.1] 2023-11-28
### Fixed
  -  Missing Shipping details while syncing eBay orders to Shopify.
  -  " Grant Access " button is not renewing the eBay connection.
  - Research and Optimize CLS

### [1.3.0] 2023-11-23
### Added
   - Show progressbar after editing profile instead of Shopify products page

### [1.2.0] 2023-11-22
### Added
  - Add sorting option for arranging the product list in our app.
  
### [1.1.2] 2023-11-22
### Fixed 
  - Shipping Costs are not synced from eBay orders to Shopify.

### [1.1.1] 2023-11-21
### Fixed
  -  Product is sold out on shopify and deleted automatically from Shopify and our app profile but not ended in eBay.
  
# [1.1.0] 2023-11-20
### Fixed 
   - Inventory is different in our app from eBay.
### Added 
  - Update Pricing to 14.99 USD

# [1.0.2] 2023-11-20
### Fixed
   - Products automatically deleted from the Shopify Profile, once the product out of stock in Shopify.


 
