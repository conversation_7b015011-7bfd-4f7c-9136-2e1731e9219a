# Support Integration Testing Guide

This guide outlines how to test the Crisp Chat integration for customer support across different environments.

## Prerequisites

1. Ensure the `VITE_CRISP_ID` environment variable is set in your `.env` file
2. Verify that the Crisp website ID is correct and matches your Crisp account
3. Make sure the app is running in embedded mode within Shopify admin

## Testing Checklist

### 1. Environment Configuration Test

**Development Environment:**
- [ ] Check that `VITE_CRISP_ID` is set in `.env` file
- [ ] Verify Crisp loads in development mode (check browser console)
- [ ] Confirm no console errors related to Crisp initialization

**Production Environment:**
- [ ] Verify Crisp loads in production mode
- [ ] Check that user data (email, name) is properly set in Crisp
- [ ] Confirm no console errors in production

### 2. Navigation Menu Test

- [ ] "Get Support" link is visible in the app navigation menu
- [ ] Clicking "Get Support" navigates to `/support` route
- [ ] Navigation works in both development and production

### 3. Support Page Test

- [ ] Support page loads correctly at `/support` route
- [ ] Page displays proper content and styling
- [ ] Crisp chat automatically opens when page loads
- [ ] Fallback email button works if <PERSON><PERSON><PERSON> fails
- [ ] All translations display correctly

### 4. App Bridge Support API Test

**Note:** This requires the support link extension to be deployed.

- [ ] Support link extension is created and deployed
- [ ] "Get Support" button appears in Shopify admin (outside the app)
- [ ] Clicking admin support button redirects to app's support page
- [ ] App Bridge Support API callback is registered and working

### 5. Crisp Chat Functionality Test

- [ ] Crisp chat widget opens successfully
- [ ] User email is pre-populated in chat
- [ ] User name is pre-populated in chat
- [ ] Chat messages can be sent and received
- [ ] Chat history is maintained across sessions

### 6. Error Handling Test

- [ ] Test with Crisp unavailable (block crisp.chat domain)
- [ ] Verify fallback to email support works
- [ ] Check console for proper error logging
- [ ] Ensure no app crashes when Crisp fails

### 7. Cross-Browser Test

- [ ] Chrome: All functionality works
- [ ] Firefox: All functionality works
- [ ] Safari: All functionality works
- [ ] Edge: All functionality works

## Testing Commands

### Start Development Server
```bash
cd web/frontend
npm run dev
# or
yarn dev
```

### Check Environment Variables
```bash
# In web directory
grep VITE_CRISP_ID .env
```

### Deploy Support Extension
```bash
# In project root
shopify app deploy
```

## Test Component Usage

For detailed testing, you can temporarily add the `SupportTest` component to any page:

```jsx
import SupportTest from '../components/Support/SupportTest';

// Add to your page component
<SupportTest />
```

This component will show:
- Environment information
- Crisp loading status
- Test buttons for functionality

## Common Issues and Solutions

### Issue: Crisp not loading in development
**Solution:** Check that `VITE_CRISP_ID` is set in `.env` file

### Issue: Support button not appearing in admin
**Solution:** Ensure support link extension is deployed with `shopify app deploy`

### Issue: User data not pre-populated
**Solution:** Check that user authentication is working and data is being set in `ProtectedRoute.jsx`

### Issue: Chat not opening automatically on support page
**Solution:** Check browser console for JavaScript errors and ensure Crisp is loaded

## Success Criteria

The integration is successful when:

1. ✅ Crisp chat loads in both development and production
2. ✅ "Get Support" appears in navigation menu
3. ✅ Support page loads and auto-opens chat
4. ✅ App Bridge Support API integration works
5. ✅ User data is pre-populated in chat
6. ✅ Fallback mechanisms work when Crisp is unavailable
7. ✅ No console errors or app crashes
8. ✅ Consistent behavior across all supported browsers

## Rollback Plan

If issues are encountered:

1. Remove `SupportManager` from `App.jsx`
2. Remove support link from navigation menu
3. Revert environment variable changes
4. Remove support page and extension files

The app will continue to work with the previous support mechanism.
