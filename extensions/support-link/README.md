# Support Link Extension

This extension provides a "Get Support" link in the Shopify admin that redirects users to the app's support page where Crisp chat is automatically opened.

## Configuration

The extension is configured to:
- Target: `admin.app.support.link`
- Redirect to: `app://support` (the app's support route)
- Display text: "Get Support"

## How it works

1. When merchants click "Get Support" anywhere in the Shopify admin
2. They are redirected to the app's `/support` route
3. The support page automatically opens the Crisp chat widget
4. If already in the app, the App Bridge Support API callback is triggered directly
