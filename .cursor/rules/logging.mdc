---
description: Rules made for logging in laravel
alwaysApply: false
---
# Logging Standards for eBay Integration Application

## Core Logging Principles
- **Always use the daily log channel**: `Log::channel('daily')` for all application logs
- **Consistent context ordering**: Always prioritize session and user identification at the top of log context
- **Comprehensive error context**: Include all relevant debugging information for efficient troubleshooting

## Required Log Context Structure
When logging errors or important events, always include context in this specific order:

### Priority Order (Top to Bottom):
1. **Session identification**: `session_id` - Shopify session identifier
2. **User identification**: `ebay_user_id` - eBay user identifier  
3. **Entity identification**: Relevant entity IDs (product_id, variant_id, order_id, etc.)
4. **Shop context**: `shop_domain` - Shopify shop domain
5. **Request context**: `request_method`, `request_url` - HTTP request details
6. **Exception details**: `exception`, `trace` - Full exception and stack trace

## Standard Log Context Template
```php
Log::channel('daily')->error('Error message here', [
    'session_id' => $sessionId ?? null,
    'ebay_user_id' => $ebayUserId ?? null,
    'entity_id' => $entityId, // product_id, variant_id, order_id, etc.
    'shop_domain' => $session->getShop() ?? null,
    'request_method' => $request->method(),
    'request_url' => $request->fullUrl(),
    'exception' => $e,
    'trace' => $e->getTraceAsString()
]);
```
## Log Level Guidelines
- **Error**: Use for exceptions, failures, and critical issues
- **Warning**: Use for recoverable issues or deprecated functionality
- **Info**: Use for important business events (orders processed, products synced)
- **Debug**: Use for detailed debugging information (only in development)

## Best Practices
- **Descriptive messages**: Write clear, actionable error messages
- **Safe null handling**: Always use null coalescing operator (`??`) for optional context
- **Consistent naming**: Use snake_case for log context keys
- **No sensitive data**: Never log passwords, API keys, or sensitive user information
- **Performance consideration**: Keep log context concise but complete

## Examples

### Controller Error Logging
```php
} catch (\Exception $e) {
    Log::channel('daily')->error('Error processing product sync', [
        'session_id' => $sessionId ?? null,
        'ebay_user_id' => $ebayUserId ?? null,
        'product_id' => $productId,
        'shop_domain' => $session->getShop() ?? null,
        'request_method' => $request->method(),
        'request_url' => $request->fullUrl(),
        'exception' => $e,
        'trace' => $e->getTraceAsString()
    ]);
}
```

### Service Class Logging
```php
Log::channel('daily')->info('eBay listing created successfully', [
    'session_id' => $this->sessionId,
    'ebay_user_id' => $this->ebayUserId,
    'product_id' => $product->id,
    'ebay_listing_id' => $listingId,
    'shop_domain' => $this->shopDomain
]);
```

### API Integration Logging
```php
Log::channel('daily')->warning('eBay API rate limit approaching', [
    'session_id' => $sessionId,
    'ebay_user_id' => $ebayUserId,
    'api_endpoint' => $endpoint,
    'requests_remaining' => $remainingRequests,
    'reset_time' => $resetTime
]);
```
